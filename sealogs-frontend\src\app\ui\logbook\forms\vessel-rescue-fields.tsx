'use client'

import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import {
    CreateEventType_VesselRescue,
    UpdateEventType_VesselRescue,
    CreateCGEventMission,
    UpdateCGEventMission,
    CreateMissionTimeline,
    UpdateMissionTimeline,
} from '@/app/lib/graphQL/mutation'
import {
    CREW_LIST,
    CrewMembers_LogBookEntrySection,
    GET_LOGBOOK_ENTRY_BY_ID,
    GetTripEvent_VesselRescue,
} from '@/app/lib/graphQL/query'
import {
    GetLogBookEntriesMembers,
    getSeaLogsMembersList,
    getVesselByID,
} from '@/app/lib/actions'
import Editor from '../../editor'
import { useLazyQuery, useMutation } from '@apollo/client'
import TimeField from '../components/time'

import SeaLogsMemberModel from '@/app/offline/models/seaLogsMember'
import EventType_VesselRescueModel from '@/app/offline/models/eventType_VesselRescue'
import CGEventMissionModel from '@/app/offline/models/cgEventMission'
import MissionTimelineModel from '@/app/offline/models/missionTimeline'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { useMediaQuery } from '@reactuses/core'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/comboBox'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { ArrowLeft } from 'lucide-react'
import { toast } from 'sonner'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { H3, P } from '@/components/ui/typography'
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    CardDescription,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { useSearchParams } from 'next/navigation'
import { CrewMember } from '../../crew/types'
import { GetCrewListWithTrainingStatus } from '@/app/lib/actions'
import CrewMembers_LogBookEntrySectionModel from '@/app/offline/models/crewMembers_LogBookEntrySection'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import {
    Button,
    ScrollArea,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'
import { RecordCard } from '@/app/ui/maintenance/task/task'

export default function VesselRescueFields({
    geoLocations,
    selectedEvent = false,
    closeModal,
    handleSaveParent,
    currentRescueID,
    type,
    eventCurrentLocation,
    locationDescription,
    setLocationDescription,
    offline = false,
    locked = false,
}: {
    geoLocations: any
    selectedEvent: any
    closeModal: any
    handleSaveParent: any
    currentRescueID: any
    type: any
    eventCurrentLocation: any
    locationDescription: any
    setLocationDescription: any
    offline?: boolean
    locked?: boolean
}) {
    const searchParams = useSearchParams()
    const [locations, setLocations] = useState<any>(false)
    const [time, setTime] = useState<any>(dayjs().format('HH:mm'))
    const [openCommentsDialog, setOpenCommentsDialog] = useState(false)
    const [commentTime, setCommentTime] = useState<any>()
    const [members, setMembers] = useState<any>(false)
    const [content, setContent] = useState<any>()
    const [rescueData, setRescueData] = useState<any>(false)
    const [missionData, setMissionData] = useState<any>(false)
    const [commentData, setCommentData] = useState<any>(false)
    const [timeline, setTimeline] = useState<any>(false)
    const [deleteCommentsDialog, setDeleteCommentsDialog] = useState(false)
    const [allVesselCrews, setAllVesselCrews] = useState<CrewMember[]>([])
    const [vessel, setVessel] = useState<any>()
    const [masterID, setMasterID] = useState(0)
    const [crewMemberOptions, setCrewMemberOptions] = useState<any>([])
    const [allMembers, setAllMembers] = useState<any>([])
    const [crewMembers, setCrewMembers] = useState<any>(false)
    const [crewMembersList, setCrewMembersList] = useState<any>([])
    const [logbook, setLogbook] = useState<any>(false)
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const [currentMissionLocation, setCurrentMissionLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const [showInputDetailsP, setShowInputDetailsPanel] = useState(false)
    const isWide = useMediaQuery('(min-width: 640px)')

    const memberModel = new SeaLogsMemberModel()
    const vesselRescueModel = new EventType_VesselRescueModel()
    const cgEventMissionModel = new CGEventMissionModel()
    const missionTimelineModel = new MissionTimelineModel()
    const seaLogsMemberModel = new SeaLogsMemberModel()
    const cmlbsModel = new CrewMembers_LogBookEntrySectionModel()
    const logbookModel = new LogBookEntryModel()
    const handleTimeChange = (date: any) => {
        setTime(dayjs(date).format('HH:mm'))
    }
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {
        setRescueData(false)
        if (currentRescueID) {
            getCurrentEvent(currentRescueID)
        }
    }, [currentRescueID])

    useEffect(() => {
        setCurrentLocation(eventCurrentLocation?.currentLocation)
        handleLocationChange({ value: eventCurrentLocation?.geoLocationID })
    }, [eventCurrentLocation])

    useEffect(() => {
        if (rescueData) {
            setRescueData((prev: any) => {
                return { ...prev, locationDescription }
            })
        }
    }, [locationDescription])

    const getCurrentEvent = async (currentRescueID: any) => {
        if (currentRescueID > 0) {
            if (offline) {
                const event = await vesselRescueModel.getById(currentRescueID)
                if (event) {
                    setRescueData({
                        vesselName: event?.vesselName ? event?.vesselName : '',
                        callSign: event?.callSign ? event?.callSign : '',
                        pob: event?.pob ? event?.pob : '',
                        latitude: event?.latitude
                            ? event?.latitude
                            : eventCurrentLocation?.latitude,
                        longitude: event?.longitude
                            ? event?.longitude
                            : eventCurrentLocation?.longitude,
                        locationDescription: event?.locationDescription
                            ? event?.locationDescription
                            : '',
                        vesselLength: event?.vesselLength
                            ? event?.vesselLength
                            : '',
                        vesselType: event?.vesselType ? event?.vesselType : '',
                        makeAndModel: event?.makeAndModel
                            ? event?.makeAndModel
                            : '',
                        color: event?.color ? event?.color : '',
                        ownerName: event?.ownerName ? event?.ownerName : '',
                        phone: event?.phone ? event?.phone : '',
                        email: event?.email ? event?.email : '',
                        address: event?.address ? event?.address : '',
                        ownerOnBoard: event?.ownerOnBoard
                            ? event?.ownerOnBoard
                            : false,
                        cgMembership: event?.cgMembership
                            ? event?.cgMembership
                            : '',
                        locationID: event?.vesselLocationID
                            ? event?.vesselLocationID
                            : eventCurrentLocation?.geoLocationID,
                        missionID: event?.mission?.id ? event?.mission?.id : '',
                        operationType: event?.operationType
                            ? operationType.filter((operation: any) =>
                                  event?.operationType
                                      .split(',')
                                      .includes(operation.value),
                              )
                            : [],
                        operationDescription: event?.operationDescription
                            ? event?.operationDescription
                            : '',
                        vesselTypeDescription: event?.vesselTypeDescription
                            ? event?.vesselTypeDescription
                            : '',
                    })
                    setTime(event?.mission?.completedAt)
                    setMissionData({
                        missionType: event?.mission?.missionType?.replaceAll(
                            '_',
                            ' ',
                        ),
                        description: event?.mission?.description,
                        operationOutcome:
                            event?.mission?.operationOutcome?.replaceAll(
                                '_',
                                ' ',
                            ),
                        currentLocationID: event?.mission?.currentLocation?.id,
                        operationDescription:
                            event?.mission?.operationDescription,
                        lat: event?.mission?.currentLocation?.lat,
                        long: event?.mission?.currentLocation?.long,
                    })
                    setTimeline(event?.missionTimeline?.nodes)
                    setCurrentLocation({
                        latitude: event?.mission?.currentLocation?.lat,
                        longitude: event?.mission?.currentLocation?.long,
                    })
                    setCurrentMissionLocation({
                        latitude: event?.lat,
                        longitude: event?.long,
                    })
                    setLocationDescription(event?.locationDescription)
                }
            } else {
                getTripEvent({
                    variables: {
                        id: +currentRescueID,
                    },
                })
            }
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent_VesselRescue, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneEventType_VesselRescue
            if (event) {
                setRescueData({
                    vesselName: event?.vesselName ? event?.vesselName : '',
                    callSign: event?.callSign ? event?.callSign : '',
                    pob: event?.pob ? event?.pob : '',
                    latitude: event?.latitude
                        ? event?.latitude
                        : eventCurrentLocation?.latitude,
                    longitude: event?.longitude
                        ? event?.longitude
                        : eventCurrentLocation?.longitude,
                    locationDescription: event?.locationDescription
                        ? event?.locationDescription
                        : '',
                    vesselLength: event?.vesselLength
                        ? event?.vesselLength
                        : '',
                    vesselType: event?.vesselType ? event?.vesselType : '',
                    makeAndModel: event?.makeAndModel
                        ? event?.makeAndModel
                        : '',
                    color: event?.color ? event?.color : '',
                    ownerName: event?.ownerName ? event?.ownerName : '',
                    phone: event?.phone ? event?.phone : '',
                    email: event?.email ? event?.email : '',
                    address: event?.address ? event?.address : '',
                    ownerOnBoard: event?.ownerOnBoard
                        ? event?.ownerOnBoard
                        : false,
                    cgMembership: event?.cgMembership
                        ? event?.cgMembership
                        : '',
                    locationID: event?.vesselLocationID
                        ? event?.vesselLocationID
                        : eventCurrentLocation?.geoLocationID,
                    missionID: event?.mission?.id ? event?.mission?.id : '',
                    operationType: event?.operationType
                        ? operationType.filter((operation: any) =>
                              event?.operationType
                                  .split(',')
                                  .includes(operation.value),
                          )
                        : [],
                    operationDescription: event?.operationDescription
                        ? event?.operationDescription
                        : '',
                    vesselTypeDescription: event?.vesselTypeDescription
                        ? event?.vesselTypeDescription
                        : '',
                })
                setTime(event?.mission?.completedAt)
                setMissionData({
                    missionType: event?.mission?.missionType?.replaceAll(
                        '_',
                        ' ',
                    ),
                    description: event?.mission?.description,
                    operationOutcome:
                        event?.mission?.operationOutcome?.replaceAll('_', ' '),
                    currentLocationID: event?.mission?.currentLocation?.id,
                    operationDescription: event?.mission?.operationDescription,
                    lat: event?.mission?.currentLocation?.lat,
                    long: event?.mission?.currentLocation?.long,
                })
                setTimeline(event?.missionTimeline?.nodes)
                setCurrentLocation({
                    latitude: event?.mission?.currentLocation?.lat,
                    longitude: event?.mission?.currentLocation?.long,
                })
                setCurrentMissionLocation({
                    latitude: event?.lat,
                    longitude: event?.long,
                })
                setLocationDescription(event?.locationDescription)
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleSetMemberList = (members: any) => {
        setMembers(
            members
                ?.filter(
                    (member: any) =>
                        member.archived == false && member.firstName != '',
                )
                ?.map((member: any) => ({
                    label: member.firstName + ' ' + member.surname,
                    value: member.id,
                })),
        )
    }

    getSeaLogsMembersList(handleSetMemberList, offline)

    const handleCommentTimeChange = (date: any) => {
        setCommentTime(dayjs(date).format('HH:mm'))
        // setCommentTime(date)
    }

    useEffect(() => {
        if (geoLocations) {
            setLocations(
                geoLocations.map((location: any) => ({
                    label: location.title,
                    value: location.id,
                    latitude: location.lat,
                    longitude: location.long,
                })),
            )
        }
    }, [geoLocations])

    const vesselTypes = [
        { label: 'Commercial', value: 'Commercial' },
        { label: 'Recreation', value: 'Recreation' },
        // { label: 'Power', value: 'Power' },
        { label: 'Sail', value: 'Sail' },
        { label: 'Paddle crafts', value: 'Paddle crafts' },
        { label: 'PWC', value: 'PWC' },
        { label: 'Other', value: 'Other' },
    ]

    const missions = [
        { label: 'To locate', value: 'To locate' },
        { label: 'To assist', value: 'To assist' },
        { label: 'To save', value: 'To save' },
        { label: 'To rescue', value: 'To rescue' },
        { label: 'To remove', value: 'To remove' },
    ]

    const operationOutcomes = [
        { label: 'Assisted by others', value: 'Assisted by others' },
        { label: 'Assisted on scene', value: 'Assisted on scene' },
        { label: 'Medical treatment', value: 'Medical treatment' },
        { label: 'Safe and well', value: 'Safe and well' },
        { label: 'Not located', value: 'Not located' },
        { label: 'Not recoverable', value: 'Not recoverable' },
        { label: 'Fatality', value: 'Fatality' },
        { label: 'Stood down', value: 'Stood down' },
        { label: 'Other', value: 'Other' },
    ]

    const commentTypes = [
        { label: 'General', value: 'General' },
        { label: 'Underway', value: 'Underway' },
        { label: 'On Scene', value: 'On Scene' },
    ]

    const operationType = [
        {
            label: 'Mechanical / equipment failure',
            value: 'Mechanical / equipment failure',
        },
        { label: 'Vessel adrift', value: 'Vessel adrift' },
        { label: 'Vessel aground', value: 'Vessel aground' },
        { label: 'Capsize', value: 'Capsize' },
        { label: 'Vessel requiring tow', value: 'Vessel requiring tow' },
        { label: 'Flare sighting', value: 'Flare sighting' },
        { label: 'Vessel sinking', value: 'Vessel sinking' },
        { label: 'Collision', value: 'Collision' },
        { label: 'Vessel overdue', value: 'Vessel overdue' },
        { label: 'Other', value: 'Other' },
    ]

    const handleSaveComments = async () => {
        if (rescueData?.missionID === undefined) {
            toast.error(
                'Please save the event first in order to create timeline!',
            )
            setOpenCommentsDialog(false)
            return
        }
        const variables = {
            input: {
                commentType: commentData?.commentType
                    ? commentData?.commentType
                    : 'General',
                description: content ? content : '',
                time: commentTime
                    ? dayjs().format('DD/MM/YYYY') + ' ' + commentTime
                    : dayjs().format('DD/MM/YYYY HH:mm'),
                authorID: commentData?.authorID,
                // missionID: rescueData?.missionID,
                vesselRescueID: currentRescueID,
            },
        }

        if (commentData?.id > 0) {
            if (offline) {
                await missionTimelineModel.save({
                    id: commentData?.id,
                    ...variables.input,
                })
                toast.success('Mission timeline updated')
                setOpenCommentsDialog(false)
                setDeleteCommentsDialog(false)
                getCurrentEvent(currentRescueID)
            } else {
                updateMissionTimeline({
                    variables: {
                        input: {
                            id: commentData?.id,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                await missionTimelineModel.save({
                    id: generateUniqueId(),
                    ...variables.input,
                })
                toast.success('Mission timeline created')
                setOpenCommentsDialog(false)
                setDeleteCommentsDialog(false)
                await getCurrentEvent(currentRescueID)
            } else {
                createMissionTimeline({
                    variables: {
                        input: {
                            ...variables.input,
                        },
                    },
                })
            }
        }
        setOpenCommentsDialog(false)
    }

    const [createMissionTimeline] = useMutation(CreateMissionTimeline, {
        onCompleted: (response) => {
            toast.success('Mission timeline created')
            setOpenCommentsDialog(false)
            setDeleteCommentsDialog(false)
            getCurrentEvent(currentRescueID)
        },
        onError: (error) => {
            console.error('Error creating mission timeline', error)
        },
    })

    const [updateMissionTimeline] = useMutation(UpdateMissionTimeline, {
        onCompleted: (response) => {
            toast.success('Mission timeline updated')
            setOpenCommentsDialog(false)
            setDeleteCommentsDialog(false)
            getCurrentEvent(currentRescueID)
        },
        onError: (error) => {
            console.error('Error updating mission timeline', error)
        },
    })

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const handleSave = async () => {
        const variables = {
            input: {
                vesselName: rescueData.vesselName,
                callSign: rescueData.callSign,
                pob: +rescueData.pob,
                latitude:
                    rescueData.latitude > 0
                        ? rescueData.latitude?.toString()
                        : currentLocation.latitude?.toString(),

                longitude:
                    rescueData.longitude > 0
                        ? rescueData.longitude?.toString()
                        : currentLocation.longitude?.toString(),
                locationDescription: rescueData.locationDescription,
                vesselLength: +rescueData.vesselLength,
                vesselType: rescueData.vesselType,
                makeAndModel: rescueData.makeAndModel,
                color: rescueData.color,
                ownerName: rescueData.ownerName,
                phone: rescueData.phone,
                email: rescueData.email,
                address: rescueData.address,
                ownerOnBoard: rescueData.ownerOnBoard,
                cgMembershipType: 'cgnz',
                cgMembership: rescueData.cgMembership,
                missionID: rescueData.missionID,
                vesselLocationID:
                    rescueData.locationID > 0
                        ? rescueData.locationID
                        : eventCurrentLocation.geoLocationID,
                operationType: rescueData.operationType
                    ?.map((type: any) => type.value)
                    .join(','),
                operationDescription: rescueData.operationDescription,
                vesselTypeDescription: rescueData.vesselTypeDescription,
            },
        }

        if (currentRescueID > 0) {
            if (offline) {
                const data = await vesselRescueModel.save({
                    id: +currentRescueID,
                    ...variables.input,
                })
                if (rescueData.missionID > 0) {
                    await cgEventMissionModel.save({
                        id: rescueData.missionID,
                        missionType: missionData.missionType,
                        description: missionData.description,
                        operationDescription: missionData.operationDescription,
                        operationOutcome: missionData.operationOutcome,
                        completedAt: time,
                        currentLocationID: rescueData.locationID,
                        eventID: +data?.id,
                        eventType: 'VesselRescue',
                        lat: currentMissionLocation.latitude?.toString(),
                        long: currentMissionLocation.longitude?.toString(),
                    })
                } else {
                    await cgEventMissionModel.save({
                        id: generateUniqueId(),
                        missionType: missionData.missionType,
                        description: missionData.description,
                        operationDescription: missionData.operationDescription,
                        operationOutcome: missionData.operationOutcome,
                        completedAt: time,
                        currentLocationID: rescueData.currentLocationID,
                        eventID: +data?.id,
                        eventType: 'VesselRescue',
                        lat:
                            currentMissionLocation.latitude?.toString() ?? null,
                        long:
                            currentMissionLocation.longitude?.toString() ??
                            null,
                    })
                }
                handleSaveParent(+currentRescueID, 0)
            } else {
                updateEventType_VesselRescue({
                    variables: {
                        input: {
                            id: +currentRescueID,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                const data = await vesselRescueModel.save({
                    id: generateUniqueId(),
                    vesselName: rescueData.vesselName,
                    callSign: rescueData.callSign,
                    pob: +rescueData.pob,
                    latitude:
                        rescueData.latitude > 0
                            ? rescueData.latitude?.toString()
                            : currentLocation.latitude?.toString(),

                    longitude:
                        rescueData.longitude > 0
                            ? rescueData.longitude?.toString()
                            : currentLocation.longitude?.toString(),
                    locationDescription: rescueData.locationDescription,
                    vesselLength: +rescueData.vesselLength,
                    vesselType: rescueData.vesselType,
                    makeAndModel: rescueData.makeAndModel,
                    color: rescueData.color,
                    ownerName: rescueData.ownerName,
                    phone: rescueData.phone,
                    email: rescueData.email,
                    address: rescueData.address,
                    ownerOnBoard: rescueData.ownerOnBoard,
                    cgMembershipType: 'cgnz',
                    cgMembership: rescueData.cgMembership,
                    missionID: rescueData.missionID,
                    vesselLocationID: rescueData.locationID
                        ? rescueData.locationID
                        : eventCurrentLocation.geoLocationID,
                    operationType: rescueData.operationType
                        ?.map((type: any) => type.value)
                        .join(','),
                    operationDescription: rescueData.operationDescription,
                    vesselTypeDescription: rescueData.vesselTypeDescription,
                })
                await cgEventMissionModel.save({
                    id: generateUniqueId(),
                    missionType: missionData.missionType,
                    description: missionData.description,
                    operationDescription: missionData.operationDescription,
                    operationOutcome: missionData.operationOutcome,
                    completedAt: time,
                    currentLocationID: rescueData.locationID
                        ? rescueData.locationID
                        : eventCurrentLocation.geoLocationID,
                    eventID: +data?.id,
                    eventType: 'VesselRescue',
                })
                handleSaveParent(+data?.id, 0)
                closeModal()
            } else {
                createEventType_VesselRescue({
                    variables: {
                        input: {
                            vesselName: rescueData.vesselName,
                            callSign: rescueData.callSign,
                            pob: +rescueData.pob,
                            latitude:
                                rescueData.latitude > 0
                                    ? rescueData.latitude?.toString()
                                    : currentLocation.latitude?.toString(),

                            longitude:
                                rescueData.longitude > 0
                                    ? rescueData.longitude?.toString()
                                    : currentLocation.longitude?.toString(),
                            locationDescription: rescueData.locationDescription,
                            vesselLength: +rescueData.vesselLength,
                            vesselType: rescueData.vesselType,
                            makeAndModel: rescueData.makeAndModel,
                            color: rescueData.color,
                            ownerName: rescueData.ownerName,
                            phone: rescueData.phone,
                            email: rescueData.email,
                            address: rescueData.address,
                            ownerOnBoard: rescueData.ownerOnBoard,
                            cgMembershipType: 'cgnz',
                            cgMembership: rescueData.cgMembership,
                            missionID: rescueData.missionID,
                            vesselLocationID: rescueData.locationID
                                ? rescueData.locationID
                                : eventCurrentLocation.geoLocationID,
                            operationType: rescueData.operationType
                                ?.map((type: any) => type.value)
                                .join(','),
                            operationDescription:
                                rescueData.operationDescription,
                            vesselTypeDescription:
                                rescueData.vesselTypeDescription,
                        },
                    },
                })
            }
        }
    }

    const [createEventType_VesselRescue] = useMutation(
        CreateEventType_VesselRescue,
        {
            onCompleted: (response) => {
                const data = response.createEventType_VesselRescue
                createCGEventMission({
                    variables: {
                        input: {
                            missionType: missionData.missionType,
                            description: missionData.description,
                            operationDescription:
                                missionData.operationDescription,
                            operationOutcome: missionData.operationOutcome,
                            completedAt: time,
                            currentLocationID: rescueData.locationID
                                ? rescueData.locationID
                                : eventCurrentLocation.geoLocationID,
                            eventID: +data?.id,
                            eventType: 'VesselRescue',
                        },
                    },
                })
                handleSaveParent(+data?.id, 0)
                closeModal()
            },
            onError: (error) => {
                console.error('Error creating vessel rescue', error)
            },
        },
    )

    const [updateEventType_VesselRescue] = useMutation(
        UpdateEventType_VesselRescue,
        {
            onCompleted: (response) => {
                const data = response.updateEventType_VesselRescue
                if (rescueData.missionID > 0) {
                    updateCGEventMission({
                        variables: {
                            input: {
                                id: rescueData.missionID,
                                missionType: missionData.missionType,
                                description: missionData.description,
                                operationDescription:
                                    missionData.operationDescription,
                                operationOutcome: missionData.operationOutcome,
                                completedAt: time,
                                currentLocationID: rescueData.locationID,
                                eventID: +data?.id,
                                eventType: 'VesselRescue',
                                lat: currentMissionLocation.latitude?.toString(),
                                long: currentMissionLocation.longitude?.toString(),
                            },
                        },
                    })
                } else {
                    createCGEventMission({
                        variables: {
                            input: {
                                missionType: missionData.missionType,
                                description: missionData.description,
                                operationDescription:
                                    missionData.operationDescription,
                                operationOutcome: missionData.operationOutcome,
                                completedAt: time,
                                currentLocationID: rescueData.currentLocationID,
                                eventID: +data?.id,
                                eventType: 'VesselRescue',
                                lat: currentMissionLocation.latitude.toString(),
                                long: currentMissionLocation.longitude.toString(),
                            },
                        },
                    })
                }
                handleSaveParent(+currentRescueID, 0)
            },
            onError: (error) => {
                console.error('Error updating vessel rescue', error)
            },
        },
    )

    const [createCGEventMission] = useMutation(CreateCGEventMission, {
        onCompleted: (response) => {},
        onError: (error) => {
            console.error('Error creating CG Event Mission', error)
        },
    })

    const [updateCGEventMission] = useMutation(UpdateCGEventMission, {
        onCompleted: (response) => {},
        onError: (error) => {
            console.error('Error updating CG Event Mission', error)
        },
    })

    const handleLocationChange = (value: any) => {
        // If value is null or undefined, return early
        if (!value) return

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setRescueData((prev: any) => {
                return {
                    ...prev,
                    locationID: +value.value,
                    latitude: null,
                    longitude: null,
                }
            })
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setRescueData((prev: any) => {
                return {
                    ...prev,
                    locationID: 0, // Reset geoLocationID when using direct coordinates
                    latitude: value.latitude,
                    longitude: value.longitude,
                }
            })
        }
    }

    const handleMissionLocationChange = (value: any) => {
        // If value is null or undefined, return early
        if (!value) return

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setMissionData({
                ...missionData,
                currentLocationID: +value.value,
                lat: null,
                long: null,
            })
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setMissionData({
                ...missionData,
                currentLocationID: 0, // Reset currentLocationID when using direct coordinates
                lat: value.latitude,
                long: value.longitude,
            })
        }
    }

    const handleCreateComment = () => {
        if (selectedEvent) {
            setOpenCommentsDialog(true)
            handleEditorChange('')
            setCommentData(false)
        } else {
            toast.error(
                'Please save the event first in order to create timeline!',
            )
        }
    }

    const handleEditComment = (comment: any) => {
        setOpenCommentsDialog(true)
        setCommentData(comment)
        handleEditorChange(comment.description)
        setCommentTime(dayjs(comment.time).format('HH:mm'))
    }

    const handleDeleteComment = (commentId: any) => {
        const comment = timeline?.find((c: any) => c.id === commentId)
        if (comment) {
            setDeleteCommentsDialog(true)
            setCommentData(comment)
        }
    }

    const handleDeleteComments = async () => {
        if (offline) {
            await missionTimelineModel.save({
                id: commentData?.id,
                archived: true,
            })
            setOpenCommentsDialog(false)
            setDeleteCommentsDialog(false)
            getCurrentEvent(currentRescueID)
            setDeleteCommentsDialog(false)
        } else {
            updateMissionTimeline({
                variables: {
                    input: {
                        id: commentData?.id,
                        archived: true,
                    },
                },
            })
            setDeleteCommentsDialog(false)
        }
    }
    const offlineGetSeaLogsMembersList = async () => {
        // getSeaLogsMembersList(handleSetMemberList)
        const members = await memberModel.getAll()
        handleSetMemberList(members)
    }
    useEffect(() => {
        if (offline) {
            offlineGetSeaLogsMembersList()
        }
    }, [offline])

    getVesselByID(+vesselID, setVessel, offline)

    const [getSectionCrewMembers_LogBookEntrySection] = useLazyQuery(
        CrewMembers_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                let data = response.readCrewMembers_LogBookEntrySections.nodes
                setCrewMembers(data)
            },
            onError: (error: any) => {
                console.error('CrewMembers_LogBookEntrySection error', error)
            },
        },
    )

    const handleSetLogbook = async (logbook: any) => {
        setLogbook(logbook)
        const sectionTypes = Array.from(
            new Set(
                logbook.logBookEntrySections.nodes.map(
                    (sec: any) => sec.className,
                ),
            ),
        ).map((type) => ({
            className: type,
            ids: logbook.logBookEntrySections.nodes
                .filter((sec: any) => sec.className === type)
                .map((sec: any) => sec.id),
        }))
        sectionTypes.forEach(async (section: any) => {
            if (
                section.className === 'SeaLogs\\CrewMembers_LogBookEntrySection'
            ) {
                if (offline) {
                    const data = await cmlbsModel.getByIds(section.ids)
                    setCrewMembers(data)
                } else {
                    const searchFilter: SearchFilter = {}
                    searchFilter.id = { in: section.ids }
                    getSectionCrewMembers_LogBookEntrySection({
                        variables: {
                            filter: searchFilter,
                        },
                    })
                }
            }
        })
    }

    const getLogBookEntryByID = async (id: number) => {
        if (offline) {
            const data = await logbookModel.getById(id)
            if (data) {
                handleSetLogbook(data)
            }
        } else {
            queryLogBookEntry({
                variables: {
                    logbookEntryId: +id,
                },
            })
        }
    }

    const [queryLogBookEntry] = useLazyQuery(GET_LOGBOOK_ENTRY_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOneLogBookEntry
            if (data) {
                handleSetLogbook(data)
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookEntry error', error)
        },
    })

    useEffect(() => {
        getLogBookEntryByID(+logentryID)
    }, [])

    const [queryVesselCrews] = useLazyQuery(CREW_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readSeaLogsMembers
            if (data) {
                const allMembers = data.nodes
                    .filter((item: any) => {
                        return +item.id !== +masterID
                    })
                    .map((member: any) => {
                        // const crewWithTraining = GetCrewListWithTrainingStatus(
                        //     [member],
                        //     [vessel],
                        // )[0]
                        return {
                            label: `${member.firstName || ''} ${member.surname || ''}`.trim(),
                            value: member.id,
                            // data: crewWithTraining,
                            profile: {
                                firstName: member.firstName,
                                surname: member.surname,
                                avatar: member.profileImage,
                            },
                        }
                    })

                setAllMembers(allMembers)

                const members = allMembers.filter((member: any) => {
                    if (!crewMembers) {
                        return true
                    }
                    return (
                        !Array.isArray(crewMembers) ||
                        !crewMembers.some(
                            (section: any) =>
                                section &&
                                section.crewMember &&
                                section.crewMember.id === member.value &&
                                section.punchOut === null,
                        )
                    )
                })

                const memberOptions = members.filter(
                    (member: any) =>
                        !crewMembersList ||
                        !Array.isArray(crewMembersList) ||
                        !crewMembersList.includes(+member.value),
                )

                setCrewMemberOptions(memberOptions)
            }
        },
        onError: (error: any) => {
            console.error('queryVesselCrews error', error)
        },
    })

    const loadVesselCrews = async () => {
        if (offline) {
            const data = await seaLogsMemberModel.getByVesselId(vesselID)
            setAllVesselCrews(data)
            if (data) {
                const members = data
                    .filter((item: any) => +item.id !== +logbook.master.id)
                    .map((member: any) => {
                        const crewWithTraining = GetCrewListWithTrainingStatus(
                            [member],
                            [vessel],
                        )[0]
                        return {
                            label: `${member.firstName || ''} ${member.surname || ''}`.trim(),
                            value: member.id,
                            data: crewWithTraining,
                            profile: {
                                firstName: member.firstName,
                                surname: member.surname,
                                avatar: member.profileImage,
                            },
                        }
                    })
                setCrewMemberOptions(members)
            }
        } else {
            await queryVesselCrews({
                variables: {
                    filter: {
                        vehicles: { id: { eq: vesselID } },
                        isArchived: { eq: false },
                    },
                },
            })
        }
    }

    useEffect(() => {
        if (isLoading) {
            loadVesselCrews()
            setIsLoading(false)
        }
    }, [isLoading])

    return (
        <div className={`${locked ? 'pointer-events-none' : ''} pt-0`}>
            {type === 'TaskingComplete' && (
                <>
                    <div className="grid grid-cols-3 gap-6 pb-0 pt-0 px-4">
                        <div className="my-0  col-span-3 md:col-span-1">
                            <H3>Mission complete</H3>
                            <P>
                                Record the operation outcome, location and time
                                of completion
                            </P>
                        </div>
                        <div className="col-span-3 md:col-span-2">
                            <div className="my-0">
                                <Combobox
                                    options={operationOutcomes}
                                    value={operationOutcomes?.find(
                                        (outcome: any) =>
                                            outcome.value ==
                                            missionData?.operationOutcome,
                                    )}
                                    onChange={(value: any) => {
                                        setMissionData({
                                            ...missionData,
                                            operationOutcome: value?.value,
                                        })
                                    }}
                                    placeholder="Operation outcome"
                                />
                            </div>
                            {missionData?.operationOutcome == 'Other' && (
                                <div className="my-4">
                                    <Textarea
                                        id={`operation-outcome-description`}
                                        rows={4}
                                        className={''}
                                        placeholder="Description"
                                        value={
                                            missionData?.operationDescription
                                                ? missionData?.operationDescription
                                                : ''
                                        }
                                        onChange={() => {
                                            setMissionData({
                                                ...missionData,
                                                operationDescription: (
                                                    document.getElementById(
                                                        'operation-outcome-description',
                                                    ) as HTMLInputElement
                                                ).value,
                                            })
                                        }}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                </>
            )}
            <div className="grid gap-6 pb-4 pt-0">
                <div className="my-4">
                    <div className="space-y-2.5">
                        {timeline &&
                            timeline?.map((comment: any) => (
                                <RecordCard
                                    key={`${comment.id}-record-${comment.time || ''}`}
                                    record={comment}
                                    onEdit={handleEditComment}
                                    onDelete={handleDeleteComment}
                                />
                            ))}
                        <div className="flex flex-row gap-3">
                            <Button
                                onClick={() => setShowInputDetailsPanel(true)}>
                                Vessel details
                            </Button>

                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Button
                                        onClick={handleCreateComment}
                                        disabled={!selectedEvent}>
                                        Add notes/comments
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent hidden={selectedEvent?.id > 0}>
                                    Please save the event first in order to
                                    create timeline!
                                </TooltipContent>
                            </Tooltip>
                        </div>
                    </div>
                </div>
            </div>
            <div className="flex justify-end gap-2">
                <Button
                    variant="back"
                    iconLeft={ArrowLeft}
                    onClick={() => closeModal()}>
                    Cancel
                </Button>
                <Button onClick={handleSave}>Save</Button>
            </div>
            <AlertDialogNew
                openDialog={openCommentsDialog}
                setOpenDialog={setOpenCommentsDialog}
                size="xl"
                handleCreate={handleSaveComments}
                title={
                    commentData?.id > 0 ? 'Update Comment' : 'Create Comment'
                }
                actionText={commentData?.id > 0 ? 'Update' : 'Create Comment'}>
                <div className="space-y-6">
                    <Label
                        htmlFor="comment-type"
                        label="Comment Type"
                        className="text-sm font-medium">
                        <Combobox
                            id="comment-type"
                            options={commentTypes}
                            value={commentTypes?.find(
                                (type: any) =>
                                    type.value ==
                                    commentData?.commentType?.replaceAll(
                                        '_',
                                        ' ',
                                    ),
                            )}
                            onChange={(value: any) =>
                                setCommentData({
                                    ...commentData,
                                    commentType: value?.value,
                                })
                            }
                            placeholder="Select comment type"
                        />
                    </Label>

                    <div className="space-y-2">
                        <Label
                            htmlFor="comment_time"
                            className="flex items-center gap-2">
                            Time of Completion
                        </Label>
                        <TimeField
                            time={commentTime}
                            handleTimeChange={(date: any) => {
                                handleCommentTimeChange(date)
                            }}
                            timeID="comment_time"
                            fieldName="comment_time"
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="comment">Comment Content</Label>
                        <Editor
                            id="comment"
                            placeholder="Write your comment here..."
                            className="w-full min-h-[150px] bg-secondary-foreground"
                            content={content}
                            handleEditorChange={handleEditorChange}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label
                            htmlFor="author"
                            className="flex items-center gap-2">
                            Author
                        </Label>
                        {members && (
                            <Combobox
                                id="author"
                                options={crewMemberOptions}
                                value={crewMemberOptions?.find(
                                    (member: any) =>
                                        member.value == commentData?.author?.id,
                                )}
                                onChange={(value: any) =>
                                    setCommentData({
                                        ...commentData,
                                        authorID: value?.value,
                                    })
                                }
                                placeholder="Select crew"
                            />
                        )}
                    </div>
                </div>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={deleteCommentsDialog}
                setOpenDialog={setDeleteCommentsDialog}
                handleCreate={handleDeleteComments}
                title="Delete Comment"
                variant='warning'
                actionText="Confirm delete">
                    Are you sure you want to delete this comment? This action
                    cannot be undone and all associated data will be permanently
                    removed from the system.
            </AlertDialogNew>
            <Sheet
                open={showInputDetailsP}
                onOpenChange={(open) => setShowInputDetailsPanel(open)}>
                <SheetContent
                    side="right"
                    className="w-full max-w-md sm:max-w-xl bg-background phablet:bg-muted">
                    <SheetHeader></SheetHeader>
                    <div className="flex flex-col h-full min-h-[400px] overflow-auto">
                        <ScrollArea>
                            <div className="flex-grow space-y-6 py-4 mx-3">
                                {/* Vessel Details Section */}
                                <Card>
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center gap-2">
                                            <div className="my-4 text-sm font-semibold uppercase">
                                                Target Vessel Details
                                            </div>
                                        </div>
                                        <P>
                                            Record vessel name, callsign and
                                            number of people on board
                                        </P>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="vessel-name">
                                                Vessel Name
                                            </Label>
                                            <Input
                                                id="vessel-name"
                                                type="text"
                                                placeholder="Enter vessel name"
                                                value={
                                                    rescueData?.vesselName || ''
                                                }
                                                onChange={(e) => {
                                                    setRescueData({
                                                        ...rescueData,
                                                        vesselName:
                                                            e.target.value,
                                                    })
                                                }}
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="call-sign">
                                                Call Sign
                                            </Label>
                                            <Input
                                                id="call-sign"
                                                type="text"
                                                placeholder="Enter call sign"
                                                value={
                                                    rescueData?.callSign || ''
                                                }
                                                onChange={(e) => {
                                                    setRescueData({
                                                        ...rescueData,
                                                        callSign:
                                                            e.target.value,
                                                    })
                                                }}
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="pob">
                                                People On Board (POB)
                                            </Label>
                                            <Input
                                                id="pob"
                                                type="number"
                                                placeholder="Enter number of people"
                                                min={1}
                                                value={rescueData?.pob || 0}
                                                onChange={(e) => {
                                                    setRescueData({
                                                        ...rescueData,
                                                        pob: e.target.value,
                                                    })
                                                }}
                                            />
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Vessel Description Section */}
                                <Card>
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center gap-2">
                                            <div className="my-4 text-sm font-semibold uppercase">
                                                Vessel Description
                                            </div>
                                        </div>
                                        <P>
                                            Include details of vessel type, make
                                            and descriptors
                                        </P>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="vessel-length">
                                                Number of Vessels
                                            </Label>
                                            <Input
                                                id="vessel-length"
                                                type="number"
                                                placeholder="Enter vessel length"
                                                min={1}
                                                value={
                                                    rescueData?.vesselLength ||
                                                    0
                                                }
                                                onChange={(e) => {
                                                    setRescueData({
                                                        ...rescueData,
                                                        vesselLength:
                                                            e.target.value,
                                                    })
                                                }}
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="vessel-type">
                                                Vessel type
                                            </Label>
                                            <Combobox
                                                options={vesselTypes}
                                                value={vesselTypes?.find(
                                                    (type: any) =>
                                                        type.value ==
                                                        rescueData?.vesselType,
                                                )}
                                                onChange={(value: any) => {
                                                    setRescueData({
                                                        ...rescueData,
                                                        vesselType:
                                                            value?.value,
                                                    })
                                                }}
                                                placeholder="Select vessel type"
                                            />
                                        </div>

                                        {rescueData?.vesselType == 'Other' && (
                                            <div className="space-y-2">
                                                <Label htmlFor="vessel-type-description">
                                                    Vessel type description
                                                </Label>
                                                <Textarea
                                                    id="vessel-type-description"
                                                    rows={3}
                                                    placeholder="Describe the vessel type"
                                                    value={
                                                        rescueData?.vesselTypeDescription ||
                                                        ''
                                                    }
                                                    onChange={(e) => {
                                                        setRescueData({
                                                            ...rescueData,
                                                            vesselTypeDescription:
                                                                e.target.value,
                                                        })
                                                    }}
                                                />
                                            </div>
                                        )}

                                        <div className="space-y-2">
                                            <Label htmlFor="make">
                                                Make and odel
                                            </Label>
                                            <Input
                                                id="make"
                                                type="text"
                                                placeholder="Enter make and model"
                                                value={
                                                    rescueData?.makeAndModel ||
                                                    ''
                                                }
                                                onChange={(e) => {
                                                    setRescueData({
                                                        ...rescueData,
                                                        makeAndModel:
                                                            e.target.value,
                                                    })
                                                }}
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="color">Color</Label>
                                            <Input
                                                id="color"
                                                type="text"
                                                placeholder="Enter vessel color"
                                                value={rescueData?.color || ''}
                                                onChange={(e) => {
                                                    setRescueData({
                                                        ...rescueData,
                                                        color: e.target.value,
                                                    })
                                                }}
                                            />
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Owner's Details Section */}
                                <Card>
                                    <CardHeader className="pb-3">
                                        <div className="flex items-center gap-2">
                                            <div className="my-4 text-sm font-semibold uppercase">
                                                Owner's Details
                                            </div>
                                        </div>
                                        <P>
                                            Record vessel owner's details and
                                            membership number if applicable
                                        </P>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="owner-name">
                                                    Owner's Name
                                                </Label>
                                                <Input
                                                    id="owner-name"
                                                    type="text"
                                                    placeholder="Enter owner's name"
                                                    value={
                                                        rescueData?.ownerName ||
                                                        ''
                                                    }
                                                    onChange={(e) => {
                                                        setRescueData({
                                                            ...rescueData,
                                                            ownerName:
                                                                e.target.value,
                                                        })
                                                    }}
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="owner-phone">
                                                    Phone Number
                                                </Label>
                                                <Input
                                                    id="owner-phone"
                                                    type="text"
                                                    placeholder="Enter phone number"
                                                    value={
                                                        rescueData?.phone || ''
                                                    }
                                                    onChange={(e) => {
                                                        setRescueData({
                                                            ...rescueData,
                                                            phone: e.target
                                                                .value,
                                                        })
                                                    }}
                                                />
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="cgnz">
                                                    Coastguard NZ Membership
                                                </Label>
                                                <Input
                                                    id="cgnz"
                                                    type="text"
                                                    placeholder="Enter membership number"
                                                    value={
                                                        rescueData?.cgMembership ||
                                                        ''
                                                    }
                                                    onChange={(e) => {
                                                        setRescueData({
                                                            ...rescueData,
                                                            cgMembership:
                                                                e.target.value,
                                                        })
                                                    }}
                                                />
                                            </div>

                                            <div className="space-y-2">
                                                <Label htmlFor="owner-email">
                                                    Email Address
                                                </Label>
                                                <Input
                                                    id="owner-email"
                                                    type="text"
                                                    placeholder="Enter email address"
                                                    value={
                                                        rescueData?.email || ''
                                                    }
                                                    onChange={(e) => {
                                                        setRescueData({
                                                            ...rescueData,
                                                            email: e.target
                                                                .value,
                                                        })
                                                    }}
                                                />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="owner-address">
                                                Owner's Address
                                            </Label>
                                            <Textarea
                                                id="owner-address"
                                                rows={3}
                                                placeholder="Enter owner's address"
                                                value={
                                                    rescueData?.address || ''
                                                }
                                                onChange={(e) => {
                                                    setRescueData({
                                                        ...rescueData,
                                                        address: e.target.value,
                                                    })
                                                }}
                                            />
                                        </div>

                                        <div className="flex items-center space-x-2 pt-2">
                                            <Label
                                                htmlFor="owner-onboard"
                                                className="cursor-pointer"
                                                label="Is the owner on-board?"
                                                leftContent={
                                                    <Checkbox
                                                        id="owner-onboard"
                                                        checked={
                                                            rescueData?.ownerOnBoard ||
                                                            false
                                                        }
                                                        size="lg"
                                                        isRadioStyle
                                                        onCheckedChange={(
                                                            checked,
                                                        ) => {
                                                            setRescueData({
                                                                ...rescueData,
                                                                ownerOnBoard:
                                                                    checked ===
                                                                    true,
                                                            })
                                                        }}
                                                    />
                                                }
                                            />
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </ScrollArea>

                        <div className="flex justify-end p-4">
                            {/* {Button ? ( */}
                            <Button
                                onClick={() => setShowInputDetailsPanel(false)}>
                                Save Changes
                            </Button>
                            {/* ) : (
                                <Button
                                    onClick={() =>
                                        setShowInputDetailsPanel(false)
                                    }>
                                    <Check className="mr-2 h-4 w-4" />
                                    Save Changes
                                </Button>
                            )} */}
                        </div>
                    </div>
                </SheetContent>
            </Sheet>
        </div>
    )
}
