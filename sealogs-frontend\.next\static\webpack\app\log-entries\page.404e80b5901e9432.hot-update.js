"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/log-entry/log-entry.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/logbook/log-entry/log-entry.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LogBookEntry; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _log_date__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../log-date */ \"(app-pages-browser)/./src/app/ui/logbook/log-date.tsx\");\n/* harmony import */ var _master__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../master */ \"(app-pages-browser)/./src/app/ui/logbook/master.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _open_previous_comments__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../open-previous-comments */ \"(app-pages-browser)/./src/app/ui/logbook/open-previous-comments.tsx\");\n/* harmony import */ var _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/vessel */ \"(app-pages-browser)/./src/app/offline/models/vessel.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_customisedLogBookConfig__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/customisedLogBookConfig */ \"(app-pages-browser)/./src/app/offline/models/customisedLogBookConfig.js\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_models_sectionMemberComment__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/models/sectionMemberComment */ \"(app-pages-browser)/./src/app/offline/models/sectionMemberComment.js\");\n/* harmony import */ var _app_offline_models_assetReporting_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/offline/models/assetReporting_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/assetReporting_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_fuel_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/offline/models/fuel_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/fuel_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_ports_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/offline/models/ports_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/ports_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_supernumerary_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/offline/models/supernumerary_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/supernumerary_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_vesselDailyCheck_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/offline/models/vesselDailyCheck_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/vesselDailyCheck_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_voyageSummary_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/app/offline/models/voyageSummary_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/voyageSummary_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_crewWelfare_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/app/offline/models/crewWelfare_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewWelfare_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_logBookSignOff_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/app/offline/models/logBookSignOff_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/logBookSignOff_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_vehiclePosition__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/vehiclePosition */ \"(app-pages-browser)/./src/app/offline/models/vehiclePosition.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _app_offline_models_eventType_Supernumerary__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/app/offline/models/eventType_Supernumerary */ \"(app-pages-browser)/./src/app/offline/models/eventType_Supernumerary.js\");\n/* harmony import */ var _app_offline_models_eventType_PassengerDropFacility__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/app/offline/models/eventType_PassengerDropFacility */ \"(app-pages-browser)/./src/app/offline/models/eventType_PassengerDropFacility.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_eventType_BarCrossing__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/app/offline/models/eventType_BarCrossing */ \"(app-pages-browser)/./src/app/offline/models/eventType_BarCrossing.js\");\n/* harmony import */ var _app_offline_models_eventType_RestrictedVisibility__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @/app/offline/models/eventType_RestrictedVisibility */ \"(app-pages-browser)/./src/app/offline/models/eventType_RestrictedVisibility.js\");\n/* harmony import */ var _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @/app/offline/models/eventType_Tasking */ \"(app-pages-browser)/./src/app/offline/models/eventType_Tasking.js\");\n/* harmony import */ var _app_offline_models_infringementNotice__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @/app/offline/models/infringementNotice */ \"(app-pages-browser)/./src/app/offline/models/infringementNotice.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_53__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_54__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_helpers_logBookHelper__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! @/app/helpers/logBookHelper */ \"(app-pages-browser)/./src/app/helpers/logBookHelper.ts\");\n/* harmony import */ var _app_offline_models_logBookEntryOldConfigs__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! @/app/offline/models/logBookEntryOldConfigs */ \"(app-pages-browser)/./src/app/offline/models/logBookEntryOldConfigs.js\");\n/* harmony import */ var _app_lib_graphQL_query_offline_GetLogBookEntryOldConfigs__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! @/app/lib/graphQL/query/offline/GetLogBookEntryOldConfigs */ \"(app-pages-browser)/./src/app/lib/graphQL/query/offline/GetLogBookEntryOldConfigs.ts\");\n/* harmony import */ var _log_entry_main_content_log_entry_main_content__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! ../log-entry-main-content/log-entry-main-content */ \"(app-pages-browser)/./src/app/ui/logbook/log-entry-main-content/log-entry-main-content.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_44__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_45__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_46__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_File_UnlockIcon_lucide_react__WEBPACK_IMPORTED_MODULE_56__ = __webpack_require__(/*! __barrel_optimize__?names=File,UnlockIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/lock-open.js\");\n/* harmony import */ var _barrel_optimize_names_File_UnlockIcon_lucide_react__WEBPACK_IMPORTED_MODULE_57__ = __webpack_require__(/*! __barrel_optimize__?names=File,UnlockIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_55__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_logbook_dropdown__WEBPACK_IMPORTED_MODULE_47__ = __webpack_require__(/*! ../components/logbook-dropdown */ \"(app-pages-browser)/./src/app/ui/logbook/components/logbook-dropdown.tsx\");\n/* harmony import */ var _vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_48__ = __webpack_require__(/*! ../../vessels/vesel-icon */ \"(app-pages-browser)/./src/app/ui/vessels/vesel-icon.tsx\");\n/* harmony import */ var _components_ui_list_header__WEBPACK_IMPORTED_MODULE_49__ = __webpack_require__(/*! @/components/ui/list-header */ \"(app-pages-browser)/./src/components/ui/list-header.tsx\");\n/* harmony import */ var _radio_logs__WEBPACK_IMPORTED_MODULE_50__ = __webpack_require__(/*! ../radio-logs */ \"(app-pages-browser)/./src/app/ui/logbook/radio-logs.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_51__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_52__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/app/ui/logbook/log-entry/queries.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LogBookEntry(param) {\n    let { vesselID, logentryID } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)() // \"/log-entries\"\n    ;\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams)() // URLSearchParams\n    ;\n    const [isAdmin, setIsAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [loaded, setLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [lbeVersions, setLbeVersions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [logBookConfig, setLogBookConfig] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [locked, setLocked] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [logEntrySections, setLogEntrySections] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [fuel, setFuel] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [fuelLogs, setFuelLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [vesselDailyCheck, setVesselDailyCheck] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [signOff, setSignOff] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [crewWelfare, setCrewWelfare] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [tripReport, setTripReport] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [crewMembers, setCrewMembers] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [crewMembersList, setCrewMembersList] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    // const [crewTraining, setCrewTraining] = useState<any>()\n    const [supernumerary, setSupernumerary] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const [openLogEntries, setOpenLogEntries] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const [masterID, setMasterID] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [prevComments, setPrevComments] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [deleteConfirmation, setDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [lastSnapshot, setLastSnapshot] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const offline = false;\n    const cmlbsModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const logbookModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const clbcModel = new _app_offline_models_customisedLogBookConfig__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const slmModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const smcModel = new _app_offline_models_sectionMemberComment__WEBPACK_IMPORTED_MODULE_14__[\"default\"]();\n    const arlbesModel = new _app_offline_models_assetReporting_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_15__[\"default\"]();\n    const fuelModel = new _app_offline_models_fuel_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_16__[\"default\"]();\n    const portModel = new _app_offline_models_ports_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_17__[\"default\"]();\n    const supernumeraryModel = new _app_offline_models_supernumerary_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_18__[\"default\"]();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_19__[\"default\"]();\n    const vesselDailyCheckModel = new _app_offline_models_vesselDailyCheck_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_20__[\"default\"]();\n    const voyageSummaryModel = new _app_offline_models_voyageSummary_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_21__[\"default\"]();\n    const crewWelfareModel = new _app_offline_models_crewWelfare_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_22__[\"default\"]();\n    const signOffModel = new _app_offline_models_logBookSignOff_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_23__[\"default\"]();\n    const vehiclePositionModel = new _app_offline_models_vehiclePosition__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n    const geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_26__[\"default\"]();\n    const vesselModel = new _app_offline_models_vessel__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const supernumeraryEventModel = new _app_offline_models_eventType_Supernumerary__WEBPACK_IMPORTED_MODULE_28__[\"default\"]();\n    const passengerDropFacilityModel = new _app_offline_models_eventType_PassengerDropFacility__WEBPACK_IMPORTED_MODULE_29__[\"default\"]();\n    const dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_30__[\"default\"]();\n    const tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_31__[\"default\"]();\n    const barCrossingModel = new _app_offline_models_eventType_BarCrossing__WEBPACK_IMPORTED_MODULE_32__[\"default\"]();\n    const restrictedVisibilityModel = new _app_offline_models_eventType_RestrictedVisibility__WEBPACK_IMPORTED_MODULE_33__[\"default\"]();\n    const taskingModel = new _app_offline_models_eventType_Tasking__WEBPACK_IMPORTED_MODULE_34__[\"default\"]();\n    const infringementNoticeModel = new _app_offline_models_infringementNotice__WEBPACK_IMPORTED_MODULE_35__[\"default\"]();\n    const logBookEntryOldConfigsModel = new _app_offline_models_logBookEntryOldConfigs__WEBPACK_IMPORTED_MODULE_40__[\"default\"]();\n    const handleSetLogbooks = (logbooks)=>{\n        setOpenLogEntries(logbooks.filter((entry)=>entry.state !== \"Locked\"));\n        // Sort logbook entries\n        const sortedLogbooks = logbooks.sort((a, b)=>parseInt(b.id) - parseInt(a.id));\n        // Get previous log entries\n        const prevLogbooks = sortedLogbooks.filter((logbook)=>parseInt(logbook.id) < logentryID && logbook.state == \"Locked\");\n        if (prevLogbooks.length > 0) {\n            handleSetPrevLogbooks(prevLogbooks);\n        }\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_36__.getLogBookEntries)(vesselID, handleSetLogbooks, offline);\n    // const getLogBookEntries = async () => {\n    //     const crew = await cmlbsModel.getAll()\n    //     const entries = await logbookModel.getByVesselId(vesselID)\n    //     const data = entries.map((entry: any) => {\n    //         const crewData = crew.filter(\n    //             (crewMember: any) => crewMember.logBookEntryID === entry.id,\n    //         )\n    //         return {\n    //             ...entry,\n    //             crew: crewData,\n    //         }\n    //     })\n    //     if (data) {\n    //         handleSetLogbooks(data)\n    //     }\n    // }\n    // const [getSectionEngine_LogBookEntrySection] = useLazyQuery(\n    //     ReadEngine_LogBookEntrySections,\n    //     {\n    //         fetchPolicy: 'cache-and-network',\n    //         onCompleted: (response: any) => {\n    //             const data = response.readEngine_LogBookEntrySections.nodes\n    //             setEngine(data)\n    //         },\n    //         onError: (error: any) => {\n    //             console.error('Engine_LogBookEntrySection error', error)\n    //         },\n    //     },\n    // )\n    const [getSectionFuel_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_52__.ReadFuel_LogBookEntrySections, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readFuel_LogBookEntrySections.nodes;\n            setFuel(data);\n        },\n        onError: (error)=>{\n            console.error(\"Fuel_LogBookEntrySection error\", error);\n        }\n    });\n    // const [getSectionPorts_LogBookEntrySection] = useLazyQuery(\n    //     Ports_LogBookEntrySection,\n    //     {\n    //         fetchPolicy: 'cache-and-network',\n    //         onCompleted: (response: any) => {\n    //             const data = response.readPorts_LogBookEntrySections.nodes\n    //             setPorts(data)\n    //         },\n    //         onError: (error: any) => {\n    //             console.error('Ports_LogBookEntrySection error', error)\n    //         },\n    //     },\n    // )\n    const [getSectionVesselDailyCheck_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_52__.ReadVesselDailyCheck_LogBookEntrySections, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readVesselDailyCheck_LogBookEntrySections.nodes;\n            setVesselDailyCheck(data);\n        },\n        onError: (error)=>{\n            console.error(\"VesselDailyCheck_LogBookEntrySection error\", error);\n        }\n    });\n    const [getLogBookSignOff_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_52__.ReadLogBookSignOff_LogBookEntrySections, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readLogBookSignOff_LogBookEntrySections.nodes;\n            setSignOff(data);\n        },\n        onError: (error)=>{\n            console.error(\"LogBookSignOff_LogBookEntrySection error\", error);\n        }\n    });\n    // const [getSectionVoyageSummary_LogBookEntrySection] = useLazyQuery(\n    //     VoyageSummary_LogBookEntrySection,\n    //     {\n    //         fetchPolicy: 'cache-and-network',\n    //         onCompleted: (response: any) => {\n    //             const data =\n    //                 response.readVoyageSummary_LogBookEntrySections.nodes\n    //             setVoyageSummary(data)\n    //         },\n    //         onError: (error: any) => {\n    //             console.error('VoyageSummary_LogBookEntrySection error', error)\n    //         },\n    //     },\n    // )\n    const [getSectionTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_52__.ReadTripReport_LogBookEntrySections, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTripReport_LogBookEntrySections.nodes;\n            setTripReport(data);\n            refreshVesselPosition(data);\n        },\n        onError: (error)=>{\n            console.error(\"TripReport_LogBookEntrySection error\", error);\n        }\n    });\n    const [createVesselPosition] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_54__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_37__.CREATE_VESSEL_POSITION, {\n        onError: (error)=>{\n            console.error(\"Logbook entry update error\", error);\n        }\n    });\n    const refreshVesselPosition = async (data)=>{\n        var lat = 0, long = 0, locationID = 0;\n        data.forEach((item)=>{\n            if (item.toLat != 0 && item.toLong != 0 || item.toLocationID > 0) {\n                lat = item.toLat;\n                long = item.toLong;\n                locationID = item.toLocationID;\n            }\n            if (item.fromLat != 0 && item.fromLong != 0 || item.fromLocationID > 0) {\n                lat = item.fromLat;\n                long = item.fromLong;\n                locationID = item.fromLocationID;\n            }\n        });\n        if (lat != 0 && long != 0 || locationID && +locationID > 0) {\n            if (vessel) {\n                var _vessel_vehiclePositions_nodes_, _vessel_vehiclePositions_nodes, _vessel_vehiclePositions_nodes_1, _vessel_vehiclePositions_nodes1, _vessel_vehiclePositions_nodes__geolocation, _vessel_vehiclePositions_nodes_2, _vessel_vehiclePositions_nodes2;\n                if (((_vessel_vehiclePositions_nodes = vessel.vehiclePositions.nodes) === null || _vessel_vehiclePositions_nodes === void 0 ? void 0 : (_vessel_vehiclePositions_nodes_ = _vessel_vehiclePositions_nodes[0]) === null || _vessel_vehiclePositions_nodes_ === void 0 ? void 0 : _vessel_vehiclePositions_nodes_.lat) != lat || ((_vessel_vehiclePositions_nodes1 = vessel.vehiclePositions.nodes) === null || _vessel_vehiclePositions_nodes1 === void 0 ? void 0 : (_vessel_vehiclePositions_nodes_1 = _vessel_vehiclePositions_nodes1[0]) === null || _vessel_vehiclePositions_nodes_1 === void 0 ? void 0 : _vessel_vehiclePositions_nodes_1.long) != long || ((_vessel_vehiclePositions_nodes2 = vessel.vehiclePositions.nodes) === null || _vessel_vehiclePositions_nodes2 === void 0 ? void 0 : (_vessel_vehiclePositions_nodes_2 = _vessel_vehiclePositions_nodes2[0]) === null || _vessel_vehiclePositions_nodes_2 === void 0 ? void 0 : (_vessel_vehiclePositions_nodes__geolocation = _vessel_vehiclePositions_nodes_2.geolocation) === null || _vessel_vehiclePositions_nodes__geolocation === void 0 ? void 0 : _vessel_vehiclePositions_nodes__geolocation.id) != locationID) {\n                    if (offline) {\n                        const id = (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_25__.generateUniqueId)();\n                        let geoLocation = null;\n                        if (locationID) {\n                            geoLocation = await geoLocationModel.getById(locationID);\n                        }\n                        const vehicle = await vesselModel.getById(vessel.id);\n                        const data = {\n                            id: \"\".concat(id),\n                            vehicleID: \"\".concat(vessel.id),\n                            vehicle: vehicle,\n                            lat: lat ? lat : null,\n                            long: long ? long : null,\n                            geoLocationID: locationID ? \"\".concat(locationID) : null,\n                            geoLocation: geoLocation\n                        };\n                        await vehiclePositionModel.save(data);\n                    }\n                } else {\n                    createVesselPosition({\n                        variables: {\n                            input: {\n                                vehicleID: vessel.id,\n                                lat: lat ? lat : null,\n                                long: long ? long : null,\n                                geoLocationID: locationID ? +locationID : null\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    const [getSectionCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_52__.ReadCrewMembers_LogBookEntrySections, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            setCrewMembers(data);\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetCrewMembers = async (crewMembers)=>{\n        //why this variable not used?\n        const crewMemberList = crewMembers.filter((item)=>{\n            var _logEntrySections_filter_flatMap, _logEntrySections_filter;\n            return !(logEntrySections === null || logEntrySections === void 0 ? void 0 : (_logEntrySections_filter = logEntrySections.filter((item)=>item.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\")) === null || _logEntrySections_filter === void 0 ? void 0 : (_logEntrySections_filter_flatMap = _logEntrySections_filter.flatMap((item)=>+item.ids)) === null || _logEntrySections_filter_flatMap === void 0 ? void 0 : _logEntrySections_filter_flatMap.includes(item));\n        });\n    };\n    const [getSectionCrewWelfare_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_52__.ReadCrewWelfare_LogBookEntrySections, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readCrewWelfare_LogBookEntrySections.nodes;\n            setCrewWelfare(data[0]);\n        },\n        onError: (error)=>{\n            console.error(\"CrewWelfare_LogBookEntrySection error\", error);\n        }\n    });\n    const [queryLogBookConfig] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_52__.ReadOneCustomisedLogBookConfig, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const responseData = response.readOneCustomisedLogBookConfig;\n            if (responseData) {\n                const uniqueComponents = (0,_app_helpers_logBookHelper__WEBPACK_IMPORTED_MODULE_39__.uniqueLogbookComponents)(responseData);\n                setLogBookConfig({\n                    ...responseData,\n                    customisedLogBookComponents: {\n                        nodes: uniqueComponents\n                    }\n                });\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBookConfig error\", error);\n        }\n    });\n    const [queryLogBookEntryOldConfigs] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_38__.Get_AllLogBookEntryOldConfigs, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readLogBookEntryOldConfigss.nodes;\n            if (data) {\n                setLbeVersions(data.slice(1));\n                if (data.length > 0) {\n                    const lastSnapshot = data[data.length - 1].id;\n                    setLastSnapshot(lastSnapshot);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBookEntryOldConfigs error\", error);\n        }\n    });\n    const [getLogBookEntryOldConfig] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_app_lib_graphQL_query_offline_GetLogBookEntryOldConfigs__WEBPACK_IMPORTED_MODULE_41__.GetLogBookEntryOldConfigs, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneLogBookEntryOldConfigs;\n            if (data) {\n                setLogBookConfig(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"getLogBookEntryOldConfig error\", error);\n        }\n    });\n    const offlineQueryLogBookEntryOldConfigs = async ()=>{\n        const data = await logBookEntryOldConfigsModel.getByLogBookEntryID(logentryID);\n        if (data) {\n            setLbeVersions(data.slice(1));\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        var _logbook_logBook;\n        if (locked) {\n            getLogBookConfig();\n        } else if ((logbook === null || logbook === void 0 ? void 0 : (_logbook_logBook = logbook.logBook) === null || _logbook_logBook === void 0 ? void 0 : _logbook_logBook.id) > 0) {\n            if (offline) {\n                const responseData = clbcModel.getByCustomisedLogBookId(logbook.logBook.id);\n                if (responseData) {\n                    const uniqueComponents = (0,_app_helpers_logBookHelper__WEBPACK_IMPORTED_MODULE_39__.uniqueLogbookComponents)(responseData);\n                    setLogBookConfig({\n                        ...responseData,\n                        customisedLogBookComponents: {\n                            nodes: uniqueComponents\n                        }\n                    });\n                }\n            } else {\n                queryLogBookConfig({\n                    variables: {\n                        id: logbook.logBook.id\n                    }\n                });\n            }\n        }\n        if (offline) {\n            offlineQueryLogBookEntryOldConfigs();\n        } else {\n            queryLogBookEntryOldConfigs({\n                variables: {\n                    id: +logentryID\n                }\n            });\n        }\n    }, [\n        locked,\n        logbook\n    ]);\n    // const [getSectionCrewTraining_LogBookEntrySection] = useLazyQuery(\n    //     ReadCrewTraining_LogBookEntrySections,\n    //     {\n    //         fetchPolicy: 'cache-and-network',\n    //         onCompleted: (response: any) => {\n    //             const data =\n    //                 response.readCrewTraining_LogBookEntrySections.nodes\n    //             setCrewTraining(data)\n    //         },\n    //         onError: (error: any) => {\n    //             console.error('CrewTraining_LogBookEntrySection error', error)\n    //         },\n    //     },\n    // )\n    const [getSectionSupernumerary_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_52__.ReadSupernumerary_LogBookEntrySections, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSupernumerary_LogBookEntrySections.nodes;\n            setSupernumerary(data);\n        },\n        onError: (error)=>{\n            console.error(\"Supernumerary_LogBookEntrySection error\", error);\n        }\n    });\n    // const [getSectionEngineer_LogBookEntrySection] = useLazyQuery(\n    //     Engineer_LogBookEntrySection,\n    //     {\n    //         fetchPolicy: 'cache-and-network',\n    //         onCompleted: (response: any) => {\n    //             const data = response.readEngineer_LogBookEntrySections.nodes\n    //             setEngineer(data)\n    //         },\n    //         onError: (error: any) => {\n    //             console.error('Engineer_LogBookEntrySection error', error)\n    //         },\n    //     },\n    // )\n    // const [getSectionAssetReporting_LogBookEntrySection] = useLazyQuery(\n    //     ReadOneAssetReporting_LogBookEntrySection,\n    //     {\n    //         fetchPolicy: 'cache-and-network',\n    //         onCompleted: (response: any) => {\n    //             const data = response.readOneAssetReporting_LogBookEntrySection\n    //             setAssetReporting(data)\n    //         },\n    //         onError: (error: any) => {\n    //             console.error('AssetReporting_LogBookEntrySection error', error)\n    //         },\n    //     },\n    // )\n    const getLogBookConfig = ()=>{\n        if (lastSnapshot) {\n            getLogBookEntryOldConfig({\n                variables: {\n                    id: lastSnapshot\n                }\n            });\n        }\n    };\n    const handleSetLogbook = async (logbook)=>{\n        setFuelLogs(logbook.fuelLog.nodes);\n        if (+logbook.logBookID > 0) {\n            if (offline) {\n                const data = await clbcModel.getByCustomisedLogBookId(logbook.logBookID);\n                setLogBookConfig(data);\n            } else {\n                locked ? getLogBookConfig() : queryLogBookConfig({\n                    variables: {\n                        id: logbook.logBook.id\n                    }\n                });\n            }\n        }\n        setLogbook(logbook);\n        setStartDate(logbook.startDate);\n        setMasterID(logbook.masterID);\n        logbook.state === \"Locked\" ? setLocked(true) : setLocked(false);\n        const sectionTypes = Array.from(new Set(logbook.logBookEntrySections.nodes.map((sec)=>sec.className))).map((type)=>({\n                className: type,\n                ids: logbook.logBookEntrySections.nodes.filter((sec)=>sec.className === type).map((sec)=>sec.id)\n            }));\n        setLogEntrySections(sectionTypes);\n        sectionTypes.forEach(async (section)=>{\n            // if (section.className === 'SeaLogs\\\\Engine_LogBookEntrySection') {\n            //     if (offline) {\n            //         const data = await engineModel.getByIds(section.ids)\n            //         setEngine(data)\n            //     } else {\n            //         getSectionEngine_LogBookEntrySection({\n            //             variables: {\n            //                 id: section.ids,\n            //             },\n            //         })\n            //     }\n            // }\n            if (section.className === \"SeaLogs\\\\Fuel_LogBookEntrySection\") {\n                if (offline) {\n                    const data = await fuelModel.getByIds(section.ids);\n                    setFuel(data);\n                } else {\n                    getSectionFuel_LogBookEntrySection({\n                        variables: {\n                            id: section.ids\n                        }\n                    });\n                }\n            }\n            // if (section.className === 'SeaLogs\\\\Ports_LogBookEntrySection') {\n            //     if (offline) {\n            //         const data = await portModel.getByIds(section.ids)\n            //         setPorts(data)\n            //     } else {\n            //         getSectionPorts_LogBookEntrySection({\n            //             variables: {\n            //                 id: section.ids,\n            //             },\n            //         })\n            //     }\n            // }\n            if (section.className === \"SeaLogs\\\\VesselDailyCheck_LogBookEntrySection\") {\n                if (offline) {\n                    const data = await vesselDailyCheckModel.getByIds(section.ids);\n                    setVesselDailyCheck(data);\n                } else {\n                    getSectionVesselDailyCheck_LogBookEntrySection({\n                        variables: {\n                            id: section.ids\n                        }\n                    });\n                }\n            }\n            if (section.className === \"SeaLogs\\\\LogBookSignOff_LogBookEntrySection\") {\n                if (offline) {\n                    const data = await signOffModel.getByIds(section.ids);\n                    setSignOff(data);\n                } else {\n                    getLogBookSignOff_LogBookEntrySection({\n                        variables: {\n                            id: section.ids\n                        }\n                    });\n                }\n            }\n            if (section.className === \"SeaLogs\\\\CrewWelfare_LogBookEntrySection\") {\n                if (offline) {\n                    const data = await crewWelfareModel.getByIds(section.ids);\n                    setCrewWelfare(data[0]);\n                } else {\n                    getSectionCrewWelfare_LogBookEntrySection({\n                        variables: {\n                            id: section.ids\n                        }\n                    });\n                }\n            }\n            // if (\n            //     section.className ===\n            //     'SeaLogs\\\\VoyageSummary_LogBookEntrySection'\n            // ) {\n            //     if (offline) {\n            //         const data = await voyageSummaryModel.getByIds(section.ids)\n            //         setVoyageSummary(data)\n            //     } else {\n            //         getSectionVoyageSummary_LogBookEntrySection({\n            //             variables: {\n            //                 id: section.ids,\n            //             },\n            //         })\n            //     }\n            // }\n            if (section.className === \"SeaLogs\\\\TripReport_LogBookEntrySection\") {\n                if (offline) {\n                    let data = await tripReportModel.getByIds(section.ids);\n                    data = await addTripEventRelationships(data);\n                    setTripReport(data);\n                    await refreshVesselPosition(data);\n                } else {\n                    getSectionTripReport_LogBookEntrySection({\n                        variables: {\n                            id: section.ids\n                        }\n                    });\n                }\n            }\n            if (section.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\") {\n                if (offline) {\n                    const data = await cmlbsModel.getByIds(section.ids);\n                    setCrewMembers(data);\n                } else {\n                    const searchFilter = {};\n                    searchFilter.id = {\n                        in: section.ids\n                    };\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: searchFilter\n                        }\n                    });\n                }\n            }\n            // if (\n            //     section.className ===\n            //     'SeaLogs\\\\CrewTraining_LogBookEntrySection'\n            // ) {\n            //     if (offline) {\n            //         const data = await ctlbesModel.getByIds(section.ids)\n            //         setCrewTraining(data)\n            //     } else {\n            //         getSectionCrewTraining_LogBookEntrySection({\n            //             variables: {\n            //                 id: section.ids,\n            //             },\n            //         })\n            //     }\n            // }\n            if (section.className === \"SeaLogs\\\\Supernumerary_LogBookEntrySection\") {\n                if (offline) {\n                    const data = await supernumeraryModel.getByIds(section.ids);\n                    setSupernumerary(data);\n                } else {\n                    getSectionSupernumerary_LogBookEntrySection({\n                        variables: {\n                            id: section.ids\n                        }\n                    });\n                }\n            }\n        // if (section.className === 'SeaLogs\\\\Engineer_LogBookEntrySection') {\n        //     if (offline) {\n        //         const data = await elbesModel.getByIds(section.ids)\n        //         setEngineer(data)\n        //     } else {\n        //         getSectionEngineer_LogBookEntrySection({\n        //             variables: {\n        //                 id: section.ids,\n        //             },\n        //         })\n        //     }\n        // }\n        // if (\n        //     section.className ===\n        //     'SeaLogs\\\\AssetReporting_LogBookEntrySection'\n        // ) {\n        //     if (offline) {\n        //         const data = await arlbesModel.getByIds(section.ids)\n        //         setAssetReporting(data)\n        //     } else {\n        //         getSectionAssetReporting_LogBookEntrySection({\n        //             variables: {\n        //                 id: section.ids,\n        //             },\n        //         })\n        //     }\n        // }\n        });\n        setLoaded(true);\n    };\n    const getLogBookEntryByID = async (id)=>{\n        if (offline) {\n            const data = await logbookModel.getById(id);\n            if (data) {\n                handleSetLogbook(data);\n            }\n        } else {\n            queryLogBookEntry({\n                variables: {\n                    logbookEntryId: +id\n                }\n            });\n        }\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_36__.getVesselByID)(vesselID, setVessel, offline);\n    /* const getVesselByID = async () => {\r\n        const vessel = await vesselModel.getById(vesselID.toString())\r\n        if (vessel) {\r\n            setVessel(vessel)\r\n        }\r\n    } */ (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_36__.getOneClient)(setClient, offline);\n    /* const getOneClient = async () => {\r\n        const c = await cModel.getById(localStorage.getItem('clientId') ?? 0)\r\n        setClient(c)\r\n    } */ (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_36__.getSeaLogsMembersList)(setCrew, offline);\n    // const getSeaLogsMembersList = async () => {\n    //     const crew = await slmModel.getAll()\n    //     if (crew) {\n    //         setCrew(crew)\n    //     }\n    // }\n    const [queryPrevSectionMemberComments] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_52__.ReadSectionMemberComments, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSectionMemberComments.nodes;\n            if (data) {\n                // Set previous comments\n                if (logbook.state !== \"Locked\") {\n                    setPrevComments(data);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryPrevSectionMemberComments error\", error);\n        }\n    });\n    const handleSetPrevLogbooks = async (prevLogbooks)=>{\n        const sectionIDs = prevLogbooks.flatMap((prevLogbook)=>{\n            const sections = prevLogbook.logBookEntrySections.nodes.filter((item)=>item.className === \"SeaLogs\\\\LogBookSignOff_LogBookEntrySection\");\n            if (sections && sections.length > 0) {\n                return sections.map((section)=>section.id);\n            }\n            return [];\n        });\n        if (vessel === null || vessel === void 0 ? void 0 : vessel.displayLogbookComments) {\n            if (offline) {\n                const data = await smcModel.getPreviousComments(sectionIDs);\n                if (data) {\n                    // Set previous comments\n                    if (logbook.state !== \"Locked\") {\n                        setPrevComments(data);\n                    }\n                }\n            } else {\n                await queryPrevSectionMemberComments({\n                    variables: {\n                        filter: {\n                            logBookEntrySectionID: {\n                                in: sectionIDs\n                            },\n                            hideComment: {\n                                eq: false\n                            },\n                            commentType: {\n                                eq: \"Section\"\n                            },\n                            comment: {\n                                ne: null\n                            }\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const date_params = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(()=>{\n        var _logbook_endDate;\n        return {\n            disable: false,\n            startLabel: \"Start date\",\n            endLabel: \"End date\",\n            startDate: logbook === null || logbook === void 0 ? void 0 : logbook.startDate,\n            endDate: logbook === null || logbook === void 0 ? void 0 : logbook.endDate,\n            handleStartDateChange: false,\n            handleEndDateChange: false,\n            showOvernightCheckbox: false,\n            showEndDate: (_logbook_endDate = logbook === null || logbook === void 0 ? void 0 : logbook.endDate) !== null && _logbook_endDate !== void 0 ? _logbook_endDate : false,\n            handleShowEndDat: false\n        };\n    }, [\n        logbook\n    ]);\n    const [updateLogbookEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_54__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_37__.UPDATE_LOGBOOK_ENTRY, {\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_44__.toast.error(\"Failed to update logbook entry\");\n        }\n    });\n    const handleSetStartDate = async (date)=>{\n        setStartDate(date);\n        if (offline) {\n            const data = await logbookModel.save({\n                id: logbook.id,\n                startDate: dayjs__WEBPACK_IMPORTED_MODULE_6___default()(date).format(\"YYYY-MM-DD\")\n            });\n            setLogbook(data);\n        } else {\n            updateLogbookEntry({\n                variables: {\n                    input: {\n                        id: logentryID,\n                        startDate: date\n                    }\n                }\n            });\n        }\n    };\n    const handleSetEndDate = async (date)=>{\n        // setEndDate(date)\n        if (offline) {\n            const data = await logbookModel.save({\n                id: logbook.id,\n                endDate: dayjs__WEBPACK_IMPORTED_MODULE_6___default()(date).format(\"YYYY-MM-DD\")\n            });\n            setLogbook(data);\n        } else {\n            updateLogbookEntry({\n                variables: {\n                    input: {\n                        id: logentryID,\n                        endDate: date\n                    }\n                }\n            });\n        }\n    };\n    const [updateReloadLogbookEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_54__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_37__.UPDATE_LOGBOOK_ENTRY, {\n        onCompleted: (response)=>{\n            // Force a fresh fetch from the server to get updated master data\n            queryLogBookEntry({\n                variables: {\n                    logbookEntryId: +logentryID\n                },\n                fetchPolicy: \"no-cache\"\n            });\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_44__.toast.error(\"Failed to update logbook entry\");\n            console.error(\"Logbook entry update error\", error);\n        }\n    });\n    const handleSetMaster = async (master)=>{\n        // setMaster(master)\n        const masterID = master ? master.value : null;\n        setMasterID(masterID);\n        if (offline) {\n            if (master) {\n                const member = await slmModel.getById(master.value);\n                const data = await logbookModel.save({\n                    id: logbook.id,\n                    masterID: master.value,\n                    master: member\n                });\n                setLogbook(data);\n            } else {\n                // Clear master\n                const data = await logbookModel.save({\n                    id: logbook.id,\n                    masterID: null,\n                    master: null\n                });\n                setLogbook(data);\n            }\n        } else {\n            updateReloadLogbookEntry({\n                variables: {\n                    input: {\n                        id: logentryID,\n                        masterID: masterID\n                    }\n                }\n            });\n        }\n    };\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [edit_logBookEntry, setEdit_logBookEntry] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(process.env.EDIT_LOGBOOKENTRY || \"EDIT_LOGBOOKENTRY\", permissions)) {\n                setEdit_logBookEntry(true);\n            } else {\n                setEdit_logBookEntry(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        init_permissions();\n    }, [\n        permissions\n    ]);\n    const releaseLockState = async ()=>{\n        if (!edit_logBookEntry) {\n            sonner__WEBPACK_IMPORTED_MODULE_44__.toast.error(\"You do not have permission to unlock this log entry\");\n            return;\n        }\n        if ((openLogEntries === null || openLogEntries === void 0 ? void 0 : openLogEntries.length) > 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_44__.toast.error(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    \"Please close log entry for\",\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        href: \"/log-entries?vesselID=\".concat(vesselID, \"&logentryID=\").concat(openLogEntries[0].id),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"underline\",\n                            children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_27__.formatDate)(openLogEntries[0].startDate)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                            lineNumber: 1030,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                        lineNumber: 1028,\n                        columnNumber: 21\n                    }, this),\n                    \" \",\n                    \"before unlocking.\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                lineNumber: 1026,\n                columnNumber: 17\n            }, this));\n        } else {\n            setLocked(false);\n            if (offline) {\n                const data = await logbookModel.save({\n                    id: logbook.id,\n                    state: \"Reopened\"\n                });\n                setLogbook(data);\n            } else {\n                updateLogbookEntry({\n                    variables: {\n                        input: {\n                            id: logentryID,\n                            state: \"Reopened\"\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const updateSignOff = async (signOff)=>{\n        if (offline) {\n            const data = await signOffModel.getByIds([\n                signOff.id\n            ]);\n            setSignOff(data);\n        } else {\n            getLogBookSignOff_LogBookEntrySection({\n                variables: {\n                    id: [\n                        signOff.id\n                    ]\n                }\n            });\n        }\n    };\n    const [createAppNotification] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_54__.useMutation)(_queries__WEBPACK_IMPORTED_MODULE_52__.CreateAppNotification, {\n        onCompleted: (response)=>{\n        // console.log('createAppNotification', response)\n        },\n        onError: (error)=>{\n        // console.error('createAppNotification error', error)\n        }\n    });\n    const updateTripReport = async (tripReportData)=>{\n        if (offline) {\n            let data = await tripReportModel.getByIds(tripReportData.id);\n            data = await addTripEventRelationships(data);\n            setTripReport(data);\n            await refreshVesselPosition(data);\n        } else {\n            if (tripReportData.id.length > 0) {\n                getSectionTripReport_LogBookEntrySection({\n                    variables: {\n                        id: tripReportData.id\n                    }\n                });\n            } else {\n                setTripReport([]);\n                refreshVesselPosition([]);\n            }\n        }\n        if (tripReportData.key == \"dangerousGoodsChecklistID\") {\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        dangerousGoodsChecklist: {\n                            id: tripReportData.value\n                        }\n                    };\n                }\n                return trip;\n            });\n            setTripReport(updatedTripReport);\n        }\n        if (tripReportData.key == \"departTime\") {\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        departTime: tripReportData.value\n                    };\n                }\n                return trip;\n            });\n            // Save vessel departure notification\n            const currentTrip = updatedTripReport.find((trip)=>trip.id == tripReportData.currentTripID);\n            if (currentTrip) {\n                const relativeUrl = \"\".concat(pathname, \"?\").concat(searchParams.toString());\n                const input = {\n                    title: \"Vessel Departure\",\n                    message: currentTrip.fromLocation && currentTrip.fromLocation.title ? \"Vessel \".concat(vessel.title, \" departed from \").concat(currentTrip.fromLocation.title, \" at \").concat(currentTrip.departTime, \".\") : \"Vessel \".concat(vessel.title, \" departed at \").concat(currentTrip.departTime, \".\"),\n                    moduleName: \"TripReport_LogBookEntrySection\",\n                    moduleID: +currentTrip.id,\n                    targetLink: relativeUrl,\n                    notificationType: \"vesselDeparture\",\n                    deliveryMethods: \"app\"\n                };\n                createAppNotification({\n                    variables: {\n                        input\n                    }\n                });\n            }\n            setTripReport(updatedTripReport);\n        }\n        if (tripReportData.key == \"totalVehiclesCarried\") {\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        totalVehiclesCarried: tripReportData.value\n                    };\n                }\n                return trip;\n            });\n            setTripReport(updatedTripReport);\n        }\n        if (tripReportData.key == \"arriveTime\") {\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        arriveTime: tripReportData.value\n                    };\n                }\n                return trip;\n            });\n            // Save late vessel arrival notification\n            const currentTrip = updatedTripReport.find((trip)=>trip.id == tripReportData.currentTripID);\n            if (currentTrip && (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_27__.isVesselArrivalLate)(currentTrip.arriveTime, currentTrip.arrive)) {\n                const relativeUrl = \"\".concat(pathname, \"?\").concat(searchParams.toString());\n                const input = {\n                    title: \"Late Vessel Arrival\",\n                    message: currentTrip.toLocation && currentTrip.toLocation.title ? \"Vessel \".concat(vessel.title, \" arrived late at \").concat(currentTrip.toLocation.title, \". The actual arrival time was \").concat(currentTrip.arrive, \" and the expected arrival time was \").concat(currentTrip.arriveTime, \".\") : \"Vessel \".concat(vessel.title, \" arrived late. The actual arrival time was \").concat(currentTrip.arrive, \" and the expected arrival time was \").concat(currentTrip.arriveTime, \".\"),\n                    moduleName: \"TripReport_LogBookEntrySection\",\n                    moduleID: +currentTrip.id,\n                    targetLink: relativeUrl,\n                    notificationType: \"lateVesselArrival\",\n                    deliveryMethods: \"app\"\n                };\n                createAppNotification({\n                    variables: {\n                        input\n                    }\n                });\n            }\n            setTripReport(updatedTripReport);\n        }\n        if (tripReportData.key == \"arrive\") {\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        arrive: tripReportData.value\n                    };\n                }\n                return trip;\n            });\n            // Save late vessel arrival notification\n            const currentTrip = updatedTripReport.find((trip)=>trip.id == tripReportData.currentTripID);\n            if (currentTrip && (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_27__.isVesselArrivalLate)(currentTrip.arriveTime, currentTrip.arrive)) {\n                const relativeUrl = \"\".concat(pathname, \"?\").concat(searchParams.toString());\n                const input = {\n                    title: \"Late Vessel Arrival\",\n                    message: currentTrip.toLocation && currentTrip.toLocation.title ? \"Vessel \".concat(vessel.title, \" arrived late at \").concat(currentTrip.toLocation.title, \". The actual arrival time was \").concat(currentTrip.arrive, \" and the expected arrival time was \").concat(currentTrip.arriveTime, \".\") : \"Vessel \".concat(vessel.title, \" arrived late. The actual arrival time was \").concat(currentTrip.arrive, \" and the expected arrival time was \").concat(currentTrip.arriveTime, \".\"),\n                    moduleName: \"TripReport_LogBookEntrySection\",\n                    moduleID: +currentTrip.id,\n                    targetLink: relativeUrl,\n                    notificationType: \"lateVesselArrival\",\n                    deliveryMethods: \"app\"\n                };\n                createAppNotification({\n                    variables: {\n                        input\n                    }\n                });\n            }\n            setTripReport(updatedTripReport);\n        }\n        if (tripReportData.key == \"pob\") {\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        pob: tripReportData.value\n                    };\n                }\n                return trip;\n            });\n            setTripReport(updatedTripReport);\n        }\n        if (tripReportData.key == \"comment\") {\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        comment: tripReportData.value\n                    };\n                }\n                return trip;\n            });\n            setTripReport(updatedTripReport);\n        }\n        if (tripReportData.key == \"fromLocationID\") {\n            let fromLocation = {\n                title: tripReportData.label,\n                id: tripReportData.value\n            };\n            if (offline) {\n                fromLocation = await geoLocationModel.getById(tripReportData.value);\n            }\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        fromLocationID: tripReportData.value,\n                        fromLocation: fromLocation\n                    };\n                }\n                return trip;\n            });\n            setTripReport(updatedTripReport);\n        }\n        if (tripReportData.key == \"fromLocation\") {\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        fromLat: tripReportData.latitude,\n                        fromLong: tripReportData.longitude,\n                        fromLocationID: 0\n                    };\n                }\n                return trip;\n            });\n            setTripReport(updatedTripReport);\n        }\n        if (tripReportData.key == \"toLocationID\") {\n            let toLocation = {\n                title: tripReportData.label,\n                id: tripReportData.value\n            };\n            if (offline) {\n                toLocation = await geoLocationModel.getById(tripReportData.value);\n            }\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        toLocationID: tripReportData.value,\n                        toLocation: toLocation\n                    };\n                }\n                return trip;\n            });\n            setTripReport(updatedTripReport);\n        }\n        if (tripReportData.key == \"toLocation\") {\n            const updatedTripReport = tripReport.map((trip)=>{\n                if (trip.id == tripReportData.currentTripID) {\n                    return {\n                        ...trip,\n                        toLat: tripReportData.latitude,\n                        toLong: tripReportData.longitude,\n                        toLocationID: 0\n                    };\n                }\n                return trip;\n            });\n            setTripReport(updatedTripReport);\n        }\n    };\n    const updateFuel = async (fuel)=>{\n        if (offline) {\n            const data = await fuelModel.getByIds([\n                fuel.id\n            ]);\n            setFuel(data);\n        } else {\n            getSectionFuel_LogBookEntrySection({\n                variables: {\n                    id: [\n                        fuel.id\n                    ]\n                }\n            });\n        }\n    };\n    const updateCrewWelfare = async (crewWelfare)=>{\n        if (offline) {\n            const data = await crewWelfareModel.getById(crewWelfare.id);\n            setCrewWelfare(data);\n        } else {\n            getSectionCrewWelfare_LogBookEntrySection({\n                variables: {\n                    id: [\n                        crewWelfare.id\n                    ]\n                }\n            });\n        }\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_36__.GetLogBookEntriesMembers)(handleSetCrewMembers, offline);\n    const [queryLogBookEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_52__.ReadOneLogBookEntry, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneLogBookEntry;\n            if (data) {\n                handleSetLogbook(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBookEntry error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        getLogBookEntryByID(logentryID);\n    }, []);\n    const addTripEventRelationships = async (trips)=>{\n        const newTripReport = await Promise.all(trips.map(async (trip)=>{\n            let events = trip.tripEvents.nodes;\n            events = await Promise.all(events.map(async (event)=>{\n                if (+event.supernumeraryID > 0) {\n                    // supernumerary\n                    event.supernumerary = await supernumeraryEventModel.getById(event.supernumeraryID) || {};\n                } else if (+event.eventType_PassengerDropFacilityID > 0) {\n                    // eventType_PassengerDropFacility\n                    event.eventType_PassengerDropFacility = await passengerDropFacilityModel.getById(event.eventType_PassengerDropFacilityID) || {};\n                } else if (+event.eventType_BarCrossingID > 0) {\n                    // eventType_BarCrossing\n                    event.eventType_BarCrossing = await barCrossingModel.getById(event.eventType_BarCrossingID) || {};\n                } else if (+event.eventType_RestrictedVisibilityID > 0) {\n                    // eventType_RestrictedVisibility\n                    event.eventType_RestrictedVisibility = await restrictedVisibilityModel.getById(event.eventType_RestrictedVisibilityID) || {};\n                } else if (+event.eventType_TaskingID > 0) {\n                    // eventType_Tasking\n                    event.eventCategory = \"Tasking\";\n                    event.eventType_Tasking = await taskingModel.getById(event.eventType_TaskingID) || {};\n                } else if (+event.infringementNoticeID > 0) {\n                    // infringementNotice\n                    event.infringementNotice = await infringementNoticeModel.getById(event.infringementNoticeID) || {};\n                }\n                return event;\n            }));\n            // dangerousGoodsChecklist\n            let dgcl = trip.dangerousGoodsChecklist;\n            if (+trip.dangerousGoodsChecklistID > 0) {\n                dgcl = await dangerousGoodsChecklistModel.getById(trip.dangerousGoodsChecklistID);\n            }\n            // tripReport_Stops\n            const tripReport_Stops = await tripReport_StopModel.getByFieldID(\"logBookEntrySectionID\", trip.id);\n            return {\n                ...trip,\n                tripEvents: {\n                    nodes: events\n                },\n                dangerousGoodsChecklist: dgcl,\n                tripReport_Stops: {\n                    nodes: tripReport_Stops\n                }\n            };\n        }));\n        return newTripReport;\n    };\n    const [deleteLogBookEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_54__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_37__.DELETE_LOGBOOK_ENTRY, {\n        onCompleted: (response)=>{\n            router.push(\"/vessel/info?id=\".concat(vesselID));\n        },\n        onError: (error)=>{\n            console.error(\"Logbook entry delete error\", error);\n        }\n    });\n    const handleDeleteLBE = async ()=>{\n        deleteLogBookEntry({\n            variables: {\n                ids: [\n                    +logentryID\n                ]\n            }\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if ( true && typeof window.localStorage !== \"undefined\") {\n            const result = localStorage.getItem(\"admin\");\n            const admin = result === \"true\";\n            setIsAdmin(admin);\n        }\n    }, []);\n    const menuItems = [\n        {\n            label: \"Crew manifest\",\n            value: \"crew\"\n        },\n        {\n            label: \"Pre-departure checks\",\n            value: \"pre-departure-checks\"\n        },\n        {\n            label: \"Weather\",\n            value: \"weather\"\n        },\n        {\n            label: \"Trip logs\",\n            value: \"trip-log\"\n        },\n        {\n            label: \"Complete log entry\",\n            value: \"complete-logbook\"\n        }\n    ];\n    var _logbook_master, _client_masterTerm, _logbook_startDate;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_list_header__WEBPACK_IMPORTED_MODULE_49__.ListHeader, {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_vesel_icon__WEBPACK_IMPORTED_MODULE_48__[\"default\"], {\n                    vessel: vessel\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                    lineNumber: 1500,\n                    columnNumber: 23\n                }, void 0),\n                title: vessel && (vessel === null || vessel === void 0 ? void 0 : vessel.title),\n                titleClassName: \" leading-none\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_logbook_dropdown__WEBPACK_IMPORTED_MODULE_47__.LogbookActionMenu, {\n                    items: menuItems,\n                    onBack: ()=>router.back(),\n                    onDistructAction: ()=>setDeleteConfirmation(true),\n                    ShowDistructive: isAdmin && permissions && !locked && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_7__.hasPermission)(\"DELETE_LOGBOOKENTRY\", permissions),\n                    setOpen: setOpen,\n                    logBookConfig: logBookConfig,\n                    disTructLabel: \"Delete logbook entry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                    lineNumber: 1504,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                lineNumber: 1499,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-16 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            logbook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_51__.cn)(\"grid gap-8 md:grid-cols-2\", locked ? \"opacity-90 pointer-events-none\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_log_date__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            log_params: date_params,\n                                            setStartDate: handleSetStartDate,\n                                            setEndDate: handleSetEndDate,\n                                            edit_logBookEntry: edit_logBookEntry\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                            lineNumber: 1531,\n                                            columnNumber: 33\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_master__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            offline: offline,\n                                            master: (_logbook_master = logbook === null || logbook === void 0 ? void 0 : logbook.master) !== null && _logbook_master !== void 0 ? _logbook_master : {},\n                                            masterTerm: (_client_masterTerm = client === null || client === void 0 ? void 0 : client.masterTerm) !== null && _client_masterTerm !== void 0 ? _client_masterTerm : \"Master\",\n                                            setMaster: handleSetMaster,\n                                            crewMembers: crewMembers,\n                                            edit_logBookEntry: edit_logBookEntry\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                            lineNumber: 1537,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                    lineNumber: 1524,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false),\n                            locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex flex-wrap gap-2 justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                                lineNumber: 1552,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: (logbook === null || logbook === void 0 ? void 0 : logbook.lockedDate) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_55__.format)(logbook === null || logbook === void 0 ? void 0 : logbook.lockedDate, \"PPpp\") : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                                lineNumber: 1553,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                        lineNumber: 1551,\n                                        columnNumber: 29\n                                    }, this),\n                                    !offline && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            loaded && locked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_46__.Button, {\n                                                iconLeft: _barrel_optimize_names_File_UnlockIcon_lucide_react__WEBPACK_IMPORTED_MODULE_56__[\"default\"],\n                                                onClick: releaseLockState,\n                                                variant: \"secondary\",\n                                                children: \"Unlock\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                                lineNumber: 1563,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_46__.Button, {\n                                                iconLeft: _barrel_optimize_names_File_UnlockIcon_lucide_react__WEBPACK_IMPORTED_MODULE_57__[\"default\"],\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/log-entries/pdf?vesselID=\".concat(vesselID, \"&logentryID=\").concat(logentryID, \"&pdf\"),\n                                                    children: [\n                                                        \"PDF (Latest)\",\n                                                        \" \"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                                    lineNumber: 1572,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                                lineNumber: 1571,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                        lineNumber: 1561,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                lineNumber: 1550,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                        lineNumber: 1521,\n                        columnNumber: 17\n                    }, this),\n                    lbeVersions && lbeVersions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_43__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_43__.CardHeader, {\n                                className: \"pb-2 \",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_43__.CardTitle, {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Previous Versions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                    lineNumber: 1586,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                lineNumber: 1585,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_43__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: lbeVersions.map((version)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_46__.Button, {\n                                            variant: \"secondary\",\n                                            size: \"sm\",\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                href: \"/log-entries/oldEntry?vesselID=\".concat(vesselID, \"&logentryID=\").concat(logentryID, \"&pdf&snapID=\").concat(version.id),\n                                                children: dayjs__WEBPACK_IMPORTED_MODULE_6___default()(version.created).format(\"DD/MM/YYYY H:m:s\")\n                                            }, version.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                                lineNumber: 1597,\n                                                columnNumber: 41\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                            lineNumber: 1593,\n                                            columnNumber: 37\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                    lineNumber: 1591,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                                lineNumber: 1590,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                        lineNumber: 1584,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_open_previous_comments__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        prevComments: prevComments,\n                        onDismiss: (coms)=>{\n                            setPrevComments(coms);\n                        },\n                        onDismissAll: ()=>setPrevComments([])\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                        lineNumber: 1611,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                lineNumber: 1520,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_log_entry_main_content_log_entry_main_content__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                    offline: offline,\n                    logbook: logbook,\n                    vessel: vessel,\n                    loaded: loaded,\n                    locked: locked,\n                    logBookConfig: logBookConfig,\n                    fuel: fuel,\n                    updateFuel: updateFuel,\n                    logentryID: logentryID,\n                    logEntrySections: logEntrySections,\n                    edit_logBookEntry: edit_logBookEntry,\n                    supernumerary: supernumerary,\n                    setSupernumerary: setSupernumerary,\n                    crewMembers: crewMembers,\n                    setCrewMembers: setCrewMembers,\n                    crew: crew,\n                    crewWelfare: crewWelfare,\n                    updateCrewWelfare: updateCrewWelfare,\n                    crewMembersList: crewMembersList,\n                    masterID: masterID,\n                    vesselDailyCheck: vesselDailyCheck,\n                    setVesselDailyCheck: setVesselDailyCheck,\n                    fuelLogs: fuelLogs,\n                    signOff: signOff,\n                    tripReport: tripReport,\n                    updateSignOff: updateSignOff,\n                    updateTripReport: updateTripReport,\n                    client: client,\n                    prevComments: prevComments,\n                    setPrevComments: setPrevComments,\n                    logBookStartDate: (_logbook_startDate = logbook === null || logbook === void 0 ? void 0 : logbook.startDate) !== null && _logbook_startDate !== void 0 ? _logbook_startDate : startDate\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                    lineNumber: 1620,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                lineNumber: 1619,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_45__.AlertDialogNew, {\n                openDialog: deleteConfirmation,\n                setOpenDialog: setDeleteConfirmation,\n                handleCreate: handleDeleteLBE,\n                title: \"Delete Logbook Entry\",\n                description: \"Are you sure you want to delete this logbook entry? This action cannot be undone.\",\n                cancelText: \"Cancel\",\n                destructiveActionText: \"Delete\",\n                handleDestructiveAction: handleDeleteLBE,\n                showDestructiveAction: true,\n                variant: \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                lineNumber: 1654,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radio_logs__WEBPACK_IMPORTED_MODULE_50__[\"default\"], {\n                open: open,\n                setOpen: setOpen,\n                logentryID: logentryID\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\log-entry\\\\log-entry.tsx\",\n                lineNumber: 1666,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(LogBookEntry, \"pHOjl+3nQz/wOrpKXixmvOVxRic=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_54__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_54__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_54__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_54__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_53__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_54__.useMutation\n    ];\n});\n_c = LogBookEntry;\nvar _c;\n$RefreshReg$(_c, \"LogBookEntry\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/log-entry/log-entry.tsx\n"));

/***/ })

});