'use client'

import React, { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import { GetRiskFactors } from '@/app/lib/graphQL/query'
import Editor from '../editor'
import { UpdateMitigationStrategy } from '@/app/lib/graphQL/mutation'
import TableWrapper from '@/components/ui/table-wrapper'
import {
    AlertDialogNew,
    Button,
    Card,
    CardContent,
    ListHeader,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui'
import { H3 } from '@/components/ui/typography'
import {
    CollapsibleDataTable,
    createColumns,
} from '@/components/collapsible-data-table'

interface IMitigationStrategy {
    id: number
    strategy: string
}

interface IRiskFactor {
    id: number
    title?: string
    type: string
    impact: string
    probability: number
    mitigationStrategy: {
        nodes: IMitigationStrategy[]
    }
}

export default function RiskStrategies() {
    const [riskFactors, setRiskFactors] = useState<IRiskFactor[]>([])
    const [openStrategyDialog, setOpenStrategyDialog] = useState<boolean>(false)
    const [currentStrategy, setCurrentStrategy] =
        useState<IMitigationStrategy>()
    const [content, setContent] = useState<string>('')

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    useEffect(() => {
        getRiskFactors({
            variables: {
                filter: { type: { ne: 'RiskFactor' } },
            },
        })
    }, [])

    const [getRiskFactors] = useLazyQuery(GetRiskFactors, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            console.info(data.readRiskFactors.nodes)
            setRiskFactors(data.readRiskFactors.nodes)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const handleUpdateStrategy = () => {
        if (content && currentStrategy) {
            updateMitigationStrategy({
                variables: {
                    input: {
                        id: currentStrategy.id,
                        strategy: content,
                    },
                },
            })
        }
        setOpenStrategyDialog(false)
    }

    const [updateMitigationStrategy] = useMutation(UpdateMitigationStrategy, {
        onCompleted: () => {
            getRiskFactors({
                variables: {
                    filter: { type: { ne: 'RiskFactor' } },
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const columns = createColumns<IRiskFactor>([
        {
            accessorKey: 'title',
            header: 'Risk strategy',
            cellAlignment: 'left',
            cell({ row }) {
                const riskFactor = row.original
                return (
                    <>
                        {riskFactor.title || '-'} (
                        {riskFactor.type === 'TowingChecklist' &&
                            'Towing Checklist'}
                        {riskFactor.type === 'DangerousGoods' &&
                            'Dangerous Goods'}
                        {riskFactor.type === 'BarCrossingChecklist' &&
                            'Bar Crossing Checklist'}
                        )
                    </>
                )
            },
        },
        {
            accessorKey: 'impact',
            header: 'Impact',
            cellAlignment: 'left',
        },
        {
            accessorKey: 'probability',
            header: 'Probability',
            cellAlignment: 'left',
            cell({ getValue }) {
                return `${getValue()}/10`
            },
        },
    ])

    return (
        <>
            <ListHeader title="Risk Strategies" />
            <div className="mt-8">
                {riskFactors && riskFactors.length > 0 && (
                    <CollapsibleDataTable
                        data={riskFactors}
                        columns={columns}
                        showToolbar={false}
                        collapsible={true}
                        pageSize={20}
                        renderExpandedContent={(rowData) => {
                            const strategies = rowData.mitigationStrategy.nodes
                            return (
                                <div className="flex flex-col gap-4">
                                    <H3 className="text-xl">
                                        Mitigation Strategies
                                    </H3>
                                    {strategies.length == 0 && (
                                        <div className="py-4">
                                            No strategies available
                                        </div>
                                    )}
                                    {strategies.length > 0 && (
                                        <Table>
                                            <TableBody>
                                                {strategies.map((item) => (
                                                    <TableRow
                                                        key={item.id}
                                                        onClick={() => {
                                                            setContent(
                                                                item.strategy,
                                                            )
                                                            setOpenStrategyDialog(
                                                                true,
                                                            )
                                                            setCurrentStrategy(
                                                                item,
                                                            )
                                                        }}>
                                                        <TableCell>
                                                            <div
                                                                dangerouslySetInnerHTML={{
                                                                    __html: item.strategy,
                                                                }}
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    )}
                                </div>
                            )
                        }}
                        canExpand={(_) => true}
                        showPageSizeSelector={false}
                    />
                )}
            </div>
            <AlertDialogNew
                openDialog={openStrategyDialog}
                setOpenDialog={setOpenStrategyDialog}
                handleCreate={handleUpdateStrategy}
                actionText="Update"
                title="Mitigation Strategy"
                size="xl"
                description="Edit the mitigation strategy content below">
                <Editor
                    id="strategy"
                    placeholder="Mitigation strategy"
                    className="w-full"
                    content={content}
                    handleEditorChange={handleEditorChange}
                />
            </AlertDialogNew>
        </>
    )
}
