"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/logbook/forms/vessel-rescue-fields.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VesselRescueFields; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_models_eventType_VesselRescue__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/eventType_VesselRescue */ \"(app-pages-browser)/./src/app/offline/models/eventType_VesselRescue.js\");\n/* harmony import */ var _app_offline_models_cgEventMission__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/cgEventMission */ \"(app-pages-browser)/./src/app/offline/models/cgEventMission.js\");\n/* harmony import */ var _app_offline_models_missionTimeline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/missionTimeline */ \"(app-pages-browser)/./src/app/offline/models/missionTimeline.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_ui_maintenance_task_task__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/app/ui/maintenance/task/task */ \"(app-pages-browser)/./src/app/ui/maintenance/task/task.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction VesselRescueFields(param) {\n    let { geoLocations, selectedEvent = false, closeModal, handleSaveParent, currentRescueID, type, eventCurrentLocation, locationDescription, setLocationDescription, offline = false, locked = false } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_23__.useSearchParams)();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const [openCommentsDialog, setOpenCommentsDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [commentTime, setCommentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [rescueData, setRescueData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [missionData, setMissionData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [commentData, setCommentData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [deleteCommentsDialog, setDeleteCommentsDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [allVesselCrews, setAllVesselCrews] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [masterID, setMasterID] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [crewMemberOptions, setCrewMemberOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [allMembers, setAllMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewMembers, setCrewMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [crewMembersList, setCrewMembersList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentMissionLocation, setCurrentMissionLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [showInputDetailsP, setShowInputDetailsPanel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_28__.useMediaQuery)(\"(min-width: 640px)\");\n    const memberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const vesselRescueModel = new _app_offline_models_eventType_VesselRescue__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const cgEventMissionModel = new _app_offline_models_cgEventMission__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const missionTimelineModel = new _app_offline_models_missionTimeline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const cmlbsModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n    const logbookModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_25__[\"default\"]();\n    const handleTimeChange = (date)=>{\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const logentryID = (_searchParams_get1 = searchParams.get(\"logentryID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setRescueData(false);\n        if (currentRescueID) {\n            getCurrentEvent(currentRescueID);\n        }\n    }, [\n        currentRescueID\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setCurrentLocation(eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.currentLocation);\n        handleLocationChange({\n            value: eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID\n        });\n    }, [\n        eventCurrentLocation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (rescueData) {\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationDescription\n                };\n            });\n        }\n    }, [\n        locationDescription\n    ]);\n    const getCurrentEvent = async (currentRescueID)=>{\n        if (currentRescueID > 0) {\n            if (offline) {\n                const event = await vesselRescueModel.getById(currentRescueID);\n                if (event) {\n                    var _event_mission, _event_mission1, _event_mission2, _event_mission_missionType, _event_mission3, _event_mission4, _event_mission_operationOutcome, _event_mission5, _event_mission_currentLocation, _event_mission6, _event_mission7, _event_mission_currentLocation1, _event_mission8, _event_mission_currentLocation2, _event_mission9, _event_missionTimeline, _event_mission_currentLocation3, _event_mission10, _event_mission_currentLocation4, _event_mission11;\n                    setRescueData({\n                        vesselName: (event === null || event === void 0 ? void 0 : event.vesselName) ? event === null || event === void 0 ? void 0 : event.vesselName : \"\",\n                        callSign: (event === null || event === void 0 ? void 0 : event.callSign) ? event === null || event === void 0 ? void 0 : event.callSign : \"\",\n                        pob: (event === null || event === void 0 ? void 0 : event.pob) ? event === null || event === void 0 ? void 0 : event.pob : \"\",\n                        latitude: (event === null || event === void 0 ? void 0 : event.latitude) ? event === null || event === void 0 ? void 0 : event.latitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.latitude,\n                        longitude: (event === null || event === void 0 ? void 0 : event.longitude) ? event === null || event === void 0 ? void 0 : event.longitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.longitude,\n                        locationDescription: (event === null || event === void 0 ? void 0 : event.locationDescription) ? event === null || event === void 0 ? void 0 : event.locationDescription : \"\",\n                        vesselLength: (event === null || event === void 0 ? void 0 : event.vesselLength) ? event === null || event === void 0 ? void 0 : event.vesselLength : \"\",\n                        vesselType: (event === null || event === void 0 ? void 0 : event.vesselType) ? event === null || event === void 0 ? void 0 : event.vesselType : \"\",\n                        makeAndModel: (event === null || event === void 0 ? void 0 : event.makeAndModel) ? event === null || event === void 0 ? void 0 : event.makeAndModel : \"\",\n                        color: (event === null || event === void 0 ? void 0 : event.color) ? event === null || event === void 0 ? void 0 : event.color : \"\",\n                        ownerName: (event === null || event === void 0 ? void 0 : event.ownerName) ? event === null || event === void 0 ? void 0 : event.ownerName : \"\",\n                        phone: (event === null || event === void 0 ? void 0 : event.phone) ? event === null || event === void 0 ? void 0 : event.phone : \"\",\n                        email: (event === null || event === void 0 ? void 0 : event.email) ? event === null || event === void 0 ? void 0 : event.email : \"\",\n                        address: (event === null || event === void 0 ? void 0 : event.address) ? event === null || event === void 0 ? void 0 : event.address : \"\",\n                        ownerOnBoard: (event === null || event === void 0 ? void 0 : event.ownerOnBoard) ? event === null || event === void 0 ? void 0 : event.ownerOnBoard : false,\n                        cgMembership: (event === null || event === void 0 ? void 0 : event.cgMembership) ? event === null || event === void 0 ? void 0 : event.cgMembership : \"\",\n                        locationID: (event === null || event === void 0 ? void 0 : event.vesselLocationID) ? event === null || event === void 0 ? void 0 : event.vesselLocationID : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID,\n                        missionID: (event === null || event === void 0 ? void 0 : (_event_mission = event.mission) === null || _event_mission === void 0 ? void 0 : _event_mission.id) ? event === null || event === void 0 ? void 0 : (_event_mission1 = event.mission) === null || _event_mission1 === void 0 ? void 0 : _event_mission1.id : \"\",\n                        operationType: (event === null || event === void 0 ? void 0 : event.operationType) ? operationType.filter((operation)=>event === null || event === void 0 ? void 0 : event.operationType.split(\",\").includes(operation.value)) : [],\n                        operationDescription: (event === null || event === void 0 ? void 0 : event.operationDescription) ? event === null || event === void 0 ? void 0 : event.operationDescription : \"\",\n                        vesselTypeDescription: (event === null || event === void 0 ? void 0 : event.vesselTypeDescription) ? event === null || event === void 0 ? void 0 : event.vesselTypeDescription : \"\"\n                    });\n                    setTime(event === null || event === void 0 ? void 0 : (_event_mission2 = event.mission) === null || _event_mission2 === void 0 ? void 0 : _event_mission2.completedAt);\n                    setMissionData({\n                        missionType: event === null || event === void 0 ? void 0 : (_event_mission3 = event.mission) === null || _event_mission3 === void 0 ? void 0 : (_event_mission_missionType = _event_mission3.missionType) === null || _event_mission_missionType === void 0 ? void 0 : _event_mission_missionType.replaceAll(\"_\", \" \"),\n                        description: event === null || event === void 0 ? void 0 : (_event_mission4 = event.mission) === null || _event_mission4 === void 0 ? void 0 : _event_mission4.description,\n                        operationOutcome: event === null || event === void 0 ? void 0 : (_event_mission5 = event.mission) === null || _event_mission5 === void 0 ? void 0 : (_event_mission_operationOutcome = _event_mission5.operationOutcome) === null || _event_mission_operationOutcome === void 0 ? void 0 : _event_mission_operationOutcome.replaceAll(\"_\", \" \"),\n                        currentLocationID: event === null || event === void 0 ? void 0 : (_event_mission6 = event.mission) === null || _event_mission6 === void 0 ? void 0 : (_event_mission_currentLocation = _event_mission6.currentLocation) === null || _event_mission_currentLocation === void 0 ? void 0 : _event_mission_currentLocation.id,\n                        operationDescription: event === null || event === void 0 ? void 0 : (_event_mission7 = event.mission) === null || _event_mission7 === void 0 ? void 0 : _event_mission7.operationDescription,\n                        lat: event === null || event === void 0 ? void 0 : (_event_mission8 = event.mission) === null || _event_mission8 === void 0 ? void 0 : (_event_mission_currentLocation1 = _event_mission8.currentLocation) === null || _event_mission_currentLocation1 === void 0 ? void 0 : _event_mission_currentLocation1.lat,\n                        long: event === null || event === void 0 ? void 0 : (_event_mission9 = event.mission) === null || _event_mission9 === void 0 ? void 0 : (_event_mission_currentLocation2 = _event_mission9.currentLocation) === null || _event_mission_currentLocation2 === void 0 ? void 0 : _event_mission_currentLocation2.long\n                    });\n                    setTimeline(event === null || event === void 0 ? void 0 : (_event_missionTimeline = event.missionTimeline) === null || _event_missionTimeline === void 0 ? void 0 : _event_missionTimeline.nodes);\n                    setCurrentLocation({\n                        latitude: event === null || event === void 0 ? void 0 : (_event_mission10 = event.mission) === null || _event_mission10 === void 0 ? void 0 : (_event_mission_currentLocation3 = _event_mission10.currentLocation) === null || _event_mission_currentLocation3 === void 0 ? void 0 : _event_mission_currentLocation3.lat,\n                        longitude: event === null || event === void 0 ? void 0 : (_event_mission11 = event.mission) === null || _event_mission11 === void 0 ? void 0 : (_event_mission_currentLocation4 = _event_mission11.currentLocation) === null || _event_mission_currentLocation4 === void 0 ? void 0 : _event_mission_currentLocation4.long\n                    });\n                    setCurrentMissionLocation({\n                        latitude: event === null || event === void 0 ? void 0 : event.lat,\n                        longitude: event === null || event === void 0 ? void 0 : event.long\n                    });\n                    setLocationDescription(event === null || event === void 0 ? void 0 : event.locationDescription);\n                }\n            } else {\n                getTripEvent({\n                    variables: {\n                        id: +currentRescueID\n                    }\n                });\n            }\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GetTripEvent_VesselRescue, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneEventType_VesselRescue;\n            if (event) {\n                var _event_mission, _event_mission1, _event_mission2, _event_mission_missionType, _event_mission3, _event_mission4, _event_mission_operationOutcome, _event_mission5, _event_mission_currentLocation, _event_mission6, _event_mission7, _event_mission_currentLocation1, _event_mission8, _event_mission_currentLocation2, _event_mission9, _event_missionTimeline, _event_mission_currentLocation3, _event_mission10, _event_mission_currentLocation4, _event_mission11;\n                setRescueData({\n                    vesselName: (event === null || event === void 0 ? void 0 : event.vesselName) ? event === null || event === void 0 ? void 0 : event.vesselName : \"\",\n                    callSign: (event === null || event === void 0 ? void 0 : event.callSign) ? event === null || event === void 0 ? void 0 : event.callSign : \"\",\n                    pob: (event === null || event === void 0 ? void 0 : event.pob) ? event === null || event === void 0 ? void 0 : event.pob : \"\",\n                    latitude: (event === null || event === void 0 ? void 0 : event.latitude) ? event === null || event === void 0 ? void 0 : event.latitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.latitude,\n                    longitude: (event === null || event === void 0 ? void 0 : event.longitude) ? event === null || event === void 0 ? void 0 : event.longitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.longitude,\n                    locationDescription: (event === null || event === void 0 ? void 0 : event.locationDescription) ? event === null || event === void 0 ? void 0 : event.locationDescription : \"\",\n                    vesselLength: (event === null || event === void 0 ? void 0 : event.vesselLength) ? event === null || event === void 0 ? void 0 : event.vesselLength : \"\",\n                    vesselType: (event === null || event === void 0 ? void 0 : event.vesselType) ? event === null || event === void 0 ? void 0 : event.vesselType : \"\",\n                    makeAndModel: (event === null || event === void 0 ? void 0 : event.makeAndModel) ? event === null || event === void 0 ? void 0 : event.makeAndModel : \"\",\n                    color: (event === null || event === void 0 ? void 0 : event.color) ? event === null || event === void 0 ? void 0 : event.color : \"\",\n                    ownerName: (event === null || event === void 0 ? void 0 : event.ownerName) ? event === null || event === void 0 ? void 0 : event.ownerName : \"\",\n                    phone: (event === null || event === void 0 ? void 0 : event.phone) ? event === null || event === void 0 ? void 0 : event.phone : \"\",\n                    email: (event === null || event === void 0 ? void 0 : event.email) ? event === null || event === void 0 ? void 0 : event.email : \"\",\n                    address: (event === null || event === void 0 ? void 0 : event.address) ? event === null || event === void 0 ? void 0 : event.address : \"\",\n                    ownerOnBoard: (event === null || event === void 0 ? void 0 : event.ownerOnBoard) ? event === null || event === void 0 ? void 0 : event.ownerOnBoard : false,\n                    cgMembership: (event === null || event === void 0 ? void 0 : event.cgMembership) ? event === null || event === void 0 ? void 0 : event.cgMembership : \"\",\n                    locationID: (event === null || event === void 0 ? void 0 : event.vesselLocationID) ? event === null || event === void 0 ? void 0 : event.vesselLocationID : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID,\n                    missionID: (event === null || event === void 0 ? void 0 : (_event_mission = event.mission) === null || _event_mission === void 0 ? void 0 : _event_mission.id) ? event === null || event === void 0 ? void 0 : (_event_mission1 = event.mission) === null || _event_mission1 === void 0 ? void 0 : _event_mission1.id : \"\",\n                    operationType: (event === null || event === void 0 ? void 0 : event.operationType) ? operationType.filter((operation)=>event === null || event === void 0 ? void 0 : event.operationType.split(\",\").includes(operation.value)) : [],\n                    operationDescription: (event === null || event === void 0 ? void 0 : event.operationDescription) ? event === null || event === void 0 ? void 0 : event.operationDescription : \"\",\n                    vesselTypeDescription: (event === null || event === void 0 ? void 0 : event.vesselTypeDescription) ? event === null || event === void 0 ? void 0 : event.vesselTypeDescription : \"\"\n                });\n                setTime(event === null || event === void 0 ? void 0 : (_event_mission2 = event.mission) === null || _event_mission2 === void 0 ? void 0 : _event_mission2.completedAt);\n                setMissionData({\n                    missionType: event === null || event === void 0 ? void 0 : (_event_mission3 = event.mission) === null || _event_mission3 === void 0 ? void 0 : (_event_mission_missionType = _event_mission3.missionType) === null || _event_mission_missionType === void 0 ? void 0 : _event_mission_missionType.replaceAll(\"_\", \" \"),\n                    description: event === null || event === void 0 ? void 0 : (_event_mission4 = event.mission) === null || _event_mission4 === void 0 ? void 0 : _event_mission4.description,\n                    operationOutcome: event === null || event === void 0 ? void 0 : (_event_mission5 = event.mission) === null || _event_mission5 === void 0 ? void 0 : (_event_mission_operationOutcome = _event_mission5.operationOutcome) === null || _event_mission_operationOutcome === void 0 ? void 0 : _event_mission_operationOutcome.replaceAll(\"_\", \" \"),\n                    currentLocationID: event === null || event === void 0 ? void 0 : (_event_mission6 = event.mission) === null || _event_mission6 === void 0 ? void 0 : (_event_mission_currentLocation = _event_mission6.currentLocation) === null || _event_mission_currentLocation === void 0 ? void 0 : _event_mission_currentLocation.id,\n                    operationDescription: event === null || event === void 0 ? void 0 : (_event_mission7 = event.mission) === null || _event_mission7 === void 0 ? void 0 : _event_mission7.operationDescription,\n                    lat: event === null || event === void 0 ? void 0 : (_event_mission8 = event.mission) === null || _event_mission8 === void 0 ? void 0 : (_event_mission_currentLocation1 = _event_mission8.currentLocation) === null || _event_mission_currentLocation1 === void 0 ? void 0 : _event_mission_currentLocation1.lat,\n                    long: event === null || event === void 0 ? void 0 : (_event_mission9 = event.mission) === null || _event_mission9 === void 0 ? void 0 : (_event_mission_currentLocation2 = _event_mission9.currentLocation) === null || _event_mission_currentLocation2 === void 0 ? void 0 : _event_mission_currentLocation2.long\n                });\n                setTimeline(event === null || event === void 0 ? void 0 : (_event_missionTimeline = event.missionTimeline) === null || _event_missionTimeline === void 0 ? void 0 : _event_missionTimeline.nodes);\n                setCurrentLocation({\n                    latitude: event === null || event === void 0 ? void 0 : (_event_mission10 = event.mission) === null || _event_mission10 === void 0 ? void 0 : (_event_mission_currentLocation3 = _event_mission10.currentLocation) === null || _event_mission_currentLocation3 === void 0 ? void 0 : _event_mission_currentLocation3.lat,\n                    longitude: event === null || event === void 0 ? void 0 : (_event_mission11 = event.mission) === null || _event_mission11 === void 0 ? void 0 : (_event_mission_currentLocation4 = _event_mission11.currentLocation) === null || _event_mission_currentLocation4 === void 0 ? void 0 : _event_mission_currentLocation4.long\n                });\n                setCurrentMissionLocation({\n                    latitude: event === null || event === void 0 ? void 0 : event.lat,\n                    longitude: event === null || event === void 0 ? void 0 : event.long\n                });\n                setLocationDescription(event === null || event === void 0 ? void 0 : event.locationDescription);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const handleSetMemberList = (members)=>{\n        var _members_filter;\n        setMembers(members === null || members === void 0 ? void 0 : (_members_filter = members.filter((member)=>member.archived == false && member.firstName != \"\")) === null || _members_filter === void 0 ? void 0 : _members_filter.map((member)=>({\n                label: member.firstName + \" \" + member.surname,\n                value: member.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getSeaLogsMembersList)(handleSetMemberList, offline);\n    const handleCommentTimeChange = (date)=>{\n        setCommentTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    // setCommentTime(date)\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (geoLocations) {\n            setLocations(geoLocations.map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                })));\n        }\n    }, [\n        geoLocations\n    ]);\n    const vesselTypes = [\n        {\n            label: \"Commercial\",\n            value: \"Commercial\"\n        },\n        {\n            label: \"Recreation\",\n            value: \"Recreation\"\n        },\n        // { label: 'Power', value: 'Power' },\n        {\n            label: \"Sail\",\n            value: \"Sail\"\n        },\n        {\n            label: \"Paddle crafts\",\n            value: \"Paddle crafts\"\n        },\n        {\n            label: \"PWC\",\n            value: \"PWC\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const missions = [\n        {\n            label: \"To locate\",\n            value: \"To locate\"\n        },\n        {\n            label: \"To assist\",\n            value: \"To assist\"\n        },\n        {\n            label: \"To save\",\n            value: \"To save\"\n        },\n        {\n            label: \"To rescue\",\n            value: \"To rescue\"\n        },\n        {\n            label: \"To remove\",\n            value: \"To remove\"\n        }\n    ];\n    const operationOutcomes = [\n        {\n            label: \"Assisted by others\",\n            value: \"Assisted by others\"\n        },\n        {\n            label: \"Assisted on scene\",\n            value: \"Assisted on scene\"\n        },\n        {\n            label: \"Medical treatment\",\n            value: \"Medical treatment\"\n        },\n        {\n            label: \"Safe and well\",\n            value: \"Safe and well\"\n        },\n        {\n            label: \"Not located\",\n            value: \"Not located\"\n        },\n        {\n            label: \"Not recoverable\",\n            value: \"Not recoverable\"\n        },\n        {\n            label: \"Fatality\",\n            value: \"Fatality\"\n        },\n        {\n            label: \"Stood down\",\n            value: \"Stood down\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const commentTypes = [\n        {\n            label: \"General\",\n            value: \"General\"\n        },\n        {\n            label: \"Underway\",\n            value: \"Underway\"\n        },\n        {\n            label: \"On Scene\",\n            value: \"On Scene\"\n        }\n    ];\n    const operationType = [\n        {\n            label: \"Mechanical / equipment failure\",\n            value: \"Mechanical / equipment failure\"\n        },\n        {\n            label: \"Vessel adrift\",\n            value: \"Vessel adrift\"\n        },\n        {\n            label: \"Vessel aground\",\n            value: \"Vessel aground\"\n        },\n        {\n            label: \"Capsize\",\n            value: \"Capsize\"\n        },\n        {\n            label: \"Vessel requiring tow\",\n            value: \"Vessel requiring tow\"\n        },\n        {\n            label: \"Flare sighting\",\n            value: \"Flare sighting\"\n        },\n        {\n            label: \"Vessel sinking\",\n            value: \"Vessel sinking\"\n        },\n        {\n            label: \"Collision\",\n            value: \"Collision\"\n        },\n        {\n            label: \"Vessel overdue\",\n            value: \"Vessel overdue\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const handleSaveComments = async ()=>{\n        if ((rescueData === null || rescueData === void 0 ? void 0 : rescueData.missionID) === undefined) {\n            sonner__WEBPACK_IMPORTED_MODULE_18__.toast.error(\"Please save the event first in order to create timeline!\");\n            setOpenCommentsDialog(false);\n            return;\n        }\n        const variables = {\n            input: {\n                commentType: (commentData === null || commentData === void 0 ? void 0 : commentData.commentType) ? commentData === null || commentData === void 0 ? void 0 : commentData.commentType : \"General\",\n                description: content ? content : \"\",\n                time: commentTime ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"DD/MM/YYYY\") + \" \" + commentTime : dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"DD/MM/YYYY HH:mm\"),\n                authorID: commentData === null || commentData === void 0 ? void 0 : commentData.authorID,\n                // missionID: rescueData?.missionID,\n                vesselRescueID: currentRescueID\n            }\n        };\n        if ((commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0) {\n            if (offline) {\n                await missionTimelineModel.save({\n                    id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                    ...variables.input\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_18__.toast.success(\"Mission timeline updated\");\n                setOpenCommentsDialog(false);\n                setDeleteCommentsDialog(false);\n                getCurrentEvent(currentRescueID);\n            } else {\n                updateMissionTimeline({\n                    variables: {\n                        input: {\n                            id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                await missionTimelineModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    ...variables.input\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_18__.toast.success(\"Mission timeline created\");\n                setOpenCommentsDialog(false);\n                setDeleteCommentsDialog(false);\n                await getCurrentEvent(currentRescueID);\n            } else {\n                createMissionTimeline({\n                    variables: {\n                        input: {\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        }\n        setOpenCommentsDialog(false);\n    };\n    const [createMissionTimeline] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateMissionTimeline, {\n        onCompleted: (response)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_18__.toast.success(\"Mission timeline created\");\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating mission timeline\", error);\n        }\n    });\n    const [updateMissionTimeline] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateMissionTimeline, {\n        onCompleted: (response)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_18__.toast.success(\"Mission timeline updated\");\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating mission timeline\", error);\n        }\n    });\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleSave = async ()=>{\n        var _rescueData_latitude, _currentLocation_latitude, _rescueData_longitude, _currentLocation_longitude, _rescueData_operationType;\n        const variables = {\n            input: {\n                vesselName: rescueData.vesselName,\n                callSign: rescueData.callSign,\n                pob: +rescueData.pob,\n                latitude: rescueData.latitude > 0 ? (_rescueData_latitude = rescueData.latitude) === null || _rescueData_latitude === void 0 ? void 0 : _rescueData_latitude.toString() : (_currentLocation_latitude = currentLocation.latitude) === null || _currentLocation_latitude === void 0 ? void 0 : _currentLocation_latitude.toString(),\n                longitude: rescueData.longitude > 0 ? (_rescueData_longitude = rescueData.longitude) === null || _rescueData_longitude === void 0 ? void 0 : _rescueData_longitude.toString() : (_currentLocation_longitude = currentLocation.longitude) === null || _currentLocation_longitude === void 0 ? void 0 : _currentLocation_longitude.toString(),\n                locationDescription: rescueData.locationDescription,\n                vesselLength: +rescueData.vesselLength,\n                vesselType: rescueData.vesselType,\n                makeAndModel: rescueData.makeAndModel,\n                color: rescueData.color,\n                ownerName: rescueData.ownerName,\n                phone: rescueData.phone,\n                email: rescueData.email,\n                address: rescueData.address,\n                ownerOnBoard: rescueData.ownerOnBoard,\n                cgMembershipType: \"cgnz\",\n                cgMembership: rescueData.cgMembership,\n                missionID: rescueData.missionID,\n                vesselLocationID: rescueData.locationID > 0 ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                operationType: (_rescueData_operationType = rescueData.operationType) === null || _rescueData_operationType === void 0 ? void 0 : _rescueData_operationType.map((type)=>type.value).join(\",\"),\n                operationDescription: rescueData.operationDescription,\n                vesselTypeDescription: rescueData.vesselTypeDescription\n            }\n        };\n        if (currentRescueID > 0) {\n            if (offline) {\n                const data = await vesselRescueModel.save({\n                    id: +currentRescueID,\n                    ...variables.input\n                });\n                if (rescueData.missionID > 0) {\n                    var _currentMissionLocation_latitude, _currentMissionLocation_longitude;\n                    await cgEventMissionModel.save({\n                        id: rescueData.missionID,\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.locationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\",\n                        lat: (_currentMissionLocation_latitude = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude === void 0 ? void 0 : _currentMissionLocation_latitude.toString(),\n                        long: (_currentMissionLocation_longitude = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude === void 0 ? void 0 : _currentMissionLocation_longitude.toString()\n                    });\n                } else {\n                    var _currentMissionLocation_latitude1, _currentMissionLocation_longitude1;\n                    var _currentMissionLocation_latitude_toString, _currentMissionLocation_longitude_toString;\n                    await cgEventMissionModel.save({\n                        id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.currentLocationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\",\n                        lat: (_currentMissionLocation_latitude_toString = (_currentMissionLocation_latitude1 = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude1 === void 0 ? void 0 : _currentMissionLocation_latitude1.toString()) !== null && _currentMissionLocation_latitude_toString !== void 0 ? _currentMissionLocation_latitude_toString : null,\n                        long: (_currentMissionLocation_longitude_toString = (_currentMissionLocation_longitude1 = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude1 === void 0 ? void 0 : _currentMissionLocation_longitude1.toString()) !== null && _currentMissionLocation_longitude_toString !== void 0 ? _currentMissionLocation_longitude_toString : null\n                    });\n                }\n                handleSaveParent(+currentRescueID, 0);\n            } else {\n                updateEventType_VesselRescue({\n                    variables: {\n                        input: {\n                            id: +currentRescueID,\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                var _rescueData_latitude1, _currentLocation_latitude1, _rescueData_longitude1, _currentLocation_longitude1, _rescueData_operationType1;\n                const data = await vesselRescueModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    vesselName: rescueData.vesselName,\n                    callSign: rescueData.callSign,\n                    pob: +rescueData.pob,\n                    latitude: rescueData.latitude > 0 ? (_rescueData_latitude1 = rescueData.latitude) === null || _rescueData_latitude1 === void 0 ? void 0 : _rescueData_latitude1.toString() : (_currentLocation_latitude1 = currentLocation.latitude) === null || _currentLocation_latitude1 === void 0 ? void 0 : _currentLocation_latitude1.toString(),\n                    longitude: rescueData.longitude > 0 ? (_rescueData_longitude1 = rescueData.longitude) === null || _rescueData_longitude1 === void 0 ? void 0 : _rescueData_longitude1.toString() : (_currentLocation_longitude1 = currentLocation.longitude) === null || _currentLocation_longitude1 === void 0 ? void 0 : _currentLocation_longitude1.toString(),\n                    locationDescription: rescueData.locationDescription,\n                    vesselLength: +rescueData.vesselLength,\n                    vesselType: rescueData.vesselType,\n                    makeAndModel: rescueData.makeAndModel,\n                    color: rescueData.color,\n                    ownerName: rescueData.ownerName,\n                    phone: rescueData.phone,\n                    email: rescueData.email,\n                    address: rescueData.address,\n                    ownerOnBoard: rescueData.ownerOnBoard,\n                    cgMembershipType: \"cgnz\",\n                    cgMembership: rescueData.cgMembership,\n                    missionID: rescueData.missionID,\n                    vesselLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                    operationType: (_rescueData_operationType1 = rescueData.operationType) === null || _rescueData_operationType1 === void 0 ? void 0 : _rescueData_operationType1.map((type)=>type.value).join(\",\"),\n                    operationDescription: rescueData.operationDescription,\n                    vesselTypeDescription: rescueData.vesselTypeDescription\n                });\n                await cgEventMissionModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    missionType: missionData.missionType,\n                    description: missionData.description,\n                    operationDescription: missionData.operationDescription,\n                    operationOutcome: missionData.operationOutcome,\n                    completedAt: time,\n                    currentLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                    eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                    eventType: \"VesselRescue\"\n                });\n                handleSaveParent(+(data === null || data === void 0 ? void 0 : data.id), 0);\n                closeModal();\n            } else {\n                var _rescueData_latitude2, _currentLocation_latitude2, _rescueData_longitude2, _currentLocation_longitude2, _rescueData_operationType2;\n                createEventType_VesselRescue({\n                    variables: {\n                        input: {\n                            vesselName: rescueData.vesselName,\n                            callSign: rescueData.callSign,\n                            pob: +rescueData.pob,\n                            latitude: rescueData.latitude > 0 ? (_rescueData_latitude2 = rescueData.latitude) === null || _rescueData_latitude2 === void 0 ? void 0 : _rescueData_latitude2.toString() : (_currentLocation_latitude2 = currentLocation.latitude) === null || _currentLocation_latitude2 === void 0 ? void 0 : _currentLocation_latitude2.toString(),\n                            longitude: rescueData.longitude > 0 ? (_rescueData_longitude2 = rescueData.longitude) === null || _rescueData_longitude2 === void 0 ? void 0 : _rescueData_longitude2.toString() : (_currentLocation_longitude2 = currentLocation.longitude) === null || _currentLocation_longitude2 === void 0 ? void 0 : _currentLocation_longitude2.toString(),\n                            locationDescription: rescueData.locationDescription,\n                            vesselLength: +rescueData.vesselLength,\n                            vesselType: rescueData.vesselType,\n                            makeAndModel: rescueData.makeAndModel,\n                            color: rescueData.color,\n                            ownerName: rescueData.ownerName,\n                            phone: rescueData.phone,\n                            email: rescueData.email,\n                            address: rescueData.address,\n                            ownerOnBoard: rescueData.ownerOnBoard,\n                            cgMembershipType: \"cgnz\",\n                            cgMembership: rescueData.cgMembership,\n                            missionID: rescueData.missionID,\n                            vesselLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                            operationType: (_rescueData_operationType2 = rescueData.operationType) === null || _rescueData_operationType2 === void 0 ? void 0 : _rescueData_operationType2.map((type)=>type.value).join(\",\"),\n                            operationDescription: rescueData.operationDescription,\n                            vesselTypeDescription: rescueData.vesselTypeDescription\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createEventType_VesselRescue] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateEventType_VesselRescue, {\n        onCompleted: (response)=>{\n            const data = response.createEventType_VesselRescue;\n            createCGEventMission({\n                variables: {\n                    input: {\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\"\n                    }\n                }\n            });\n            handleSaveParent(+(data === null || data === void 0 ? void 0 : data.id), 0);\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating vessel rescue\", error);\n        }\n    });\n    const [updateEventType_VesselRescue] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateEventType_VesselRescue, {\n        onCompleted: (response)=>{\n            const data = response.updateEventType_VesselRescue;\n            if (rescueData.missionID > 0) {\n                var _currentMissionLocation_latitude, _currentMissionLocation_longitude;\n                updateCGEventMission({\n                    variables: {\n                        input: {\n                            id: rescueData.missionID,\n                            missionType: missionData.missionType,\n                            description: missionData.description,\n                            operationDescription: missionData.operationDescription,\n                            operationOutcome: missionData.operationOutcome,\n                            completedAt: time,\n                            currentLocationID: rescueData.locationID,\n                            eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                            eventType: \"VesselRescue\",\n                            lat: (_currentMissionLocation_latitude = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude === void 0 ? void 0 : _currentMissionLocation_latitude.toString(),\n                            long: (_currentMissionLocation_longitude = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude === void 0 ? void 0 : _currentMissionLocation_longitude.toString()\n                        }\n                    }\n                });\n            } else {\n                createCGEventMission({\n                    variables: {\n                        input: {\n                            missionType: missionData.missionType,\n                            description: missionData.description,\n                            operationDescription: missionData.operationDescription,\n                            operationOutcome: missionData.operationOutcome,\n                            completedAt: time,\n                            currentLocationID: rescueData.currentLocationID,\n                            eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                            eventType: \"VesselRescue\",\n                            lat: currentMissionLocation.latitude.toString(),\n                            long: currentMissionLocation.longitude.toString()\n                        }\n                    }\n                });\n            }\n            handleSaveParent(+currentRescueID, 0);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating vessel rescue\", error);\n        }\n    });\n    const [createCGEventMission] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateCGEventMission, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"Error creating CG Event Mission\", error);\n        }\n    });\n    const [updateCGEventMission] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateCGEventMission, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"Error updating CG Event Mission\", error);\n        }\n    });\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationID: +value.value,\n                    latitude: null,\n                    longitude: null\n                };\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationID: 0,\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                };\n            });\n        }\n    };\n    const handleMissionLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setMissionData({\n                ...missionData,\n                currentLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setMissionData({\n                ...missionData,\n                currentLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n        }\n    };\n    const handleCreateComment = ()=>{\n        if (selectedEvent) {\n            setOpenCommentsDialog(true);\n            handleEditorChange(\"\");\n            setCommentData(false);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_18__.toast.error(\"Please save the event first in order to create timeline!\");\n        }\n    };\n    const handleEditComment = (comment)=>{\n        setOpenCommentsDialog(true);\n        setCommentData(comment);\n        handleEditorChange(comment.description);\n        setCommentTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(comment.time).format(\"HH:mm\"));\n    };\n    const handleDeleteComment = (commentId)=>{\n        const comment = timeline === null || timeline === void 0 ? void 0 : timeline.find((c)=>c.id === commentId);\n        if (comment) {\n            setDeleteCommentsDialog(true);\n            setCommentData(comment);\n        }\n    };\n    const handleDeleteComments = async ()=>{\n        if (offline) {\n            await missionTimelineModel.save({\n                id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                archived: true\n            });\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n            setDeleteCommentsDialog(false);\n        } else {\n            updateMissionTimeline({\n                variables: {\n                    input: {\n                        id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                        archived: true\n                    }\n                }\n            });\n            setDeleteCommentsDialog(false);\n        }\n    };\n    const offlineGetSeaLogsMembersList = async ()=>{\n        // getSeaLogsMembersList(handleSetMemberList)\n        const members = await memberModel.getAll();\n        handleSetMemberList(members);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (offline) {\n            offlineGetSeaLogsMembersList();\n        }\n    }, [\n        offline\n    ]);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getVesselByID)(+vesselID, setVessel, offline);\n    const [getSectionCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            setCrewMembers(data);\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        const sectionTypes = Array.from(new Set(logbook.logBookEntrySections.nodes.map((sec)=>sec.className))).map((type)=>({\n                className: type,\n                ids: logbook.logBookEntrySections.nodes.filter((sec)=>sec.className === type).map((sec)=>sec.id)\n            }));\n        sectionTypes.forEach(async (section)=>{\n            if (section.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\") {\n                if (offline) {\n                    const data = await cmlbsModel.getByIds(section.ids);\n                    setCrewMembers(data);\n                } else {\n                    const searchFilter = {};\n                    searchFilter.id = {\n                        in: section.ids\n                    };\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: searchFilter\n                        }\n                    });\n                }\n            }\n        });\n    };\n    const getLogBookEntryByID = async (id)=>{\n        if (offline) {\n            const data = await logbookModel.getById(id);\n            if (data) {\n                handleSetLogbook(data);\n            }\n        } else {\n            queryLogBookEntry({\n                variables: {\n                    logbookEntryId: +id\n                }\n            });\n        }\n    };\n    const [queryLogBookEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_LOGBOOK_ENTRY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneLogBookEntry;\n            if (data) {\n                handleSetLogbook(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBookEntry error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getLogBookEntryByID(+logentryID);\n    }, []);\n    const [queryVesselCrews] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.CREW_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers;\n            if (data) {\n                const allMembers = data.nodes.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    // const crewWithTraining = GetCrewListWithTrainingStatus(\n                    //     [member],\n                    //     [vessel],\n                    // )[0]\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        // data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setAllMembers(allMembers);\n                const members = allMembers.filter((member)=>{\n                    if (!crewMembers) {\n                        return true;\n                    }\n                    return !Array.isArray(crewMembers) || !crewMembers.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                const memberOptions = members.filter((member)=>!crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n                setCrewMemberOptions(memberOptions);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVesselCrews error\", error);\n        }\n    });\n    const loadVesselCrews = async ()=>{\n        if (offline) {\n            const data = await seaLogsMemberModel.getByVesselId(vesselID);\n            setAllVesselCrews(data);\n            if (data) {\n                const members = data.filter((item)=>+item.id !== +logbook.master.id).map((member)=>{\n                    const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.GetCrewListWithTrainingStatus)([\n                        member\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setCrewMemberOptions(members);\n            }\n        } else {\n            await queryVesselCrews({\n                variables: {\n                    filter: {\n                        vehicles: {\n                            id: {\n                                eq: vesselID\n                            }\n                        },\n                        isArchived: {\n                            eq: false\n                        }\n                    }\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            loadVesselCrews();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" pt-0\"),\n        children: [\n            type === \"TaskingComplete\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-6 pb-0 pt-0 px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-0  col-span-3 md:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                                    children: \"Mission complete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1112,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                    children: \"Record the operation outcome, location and time of completion\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1113,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1111,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-3 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                        options: operationOutcomes,\n                                        value: operationOutcomes === null || operationOutcomes === void 0 ? void 0 : operationOutcomes.find((outcome)=>outcome.value == (missionData === null || missionData === void 0 ? void 0 : missionData.operationOutcome)),\n                                        onChange: (value)=>{\n                                            setMissionData({\n                                                ...missionData,\n                                                operationOutcome: value === null || value === void 0 ? void 0 : value.value\n                                            });\n                                        },\n                                        placeholder: \"Operation outcome\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1120,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1119,\n                                    columnNumber: 29\n                                }, this),\n                                (missionData === null || missionData === void 0 ? void 0 : missionData.operationOutcome) == \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                        id: \"operation-outcome-description\",\n                                        rows: 4,\n                                        className: \"\",\n                                        placeholder: \"Description\",\n                                        value: (missionData === null || missionData === void 0 ? void 0 : missionData.operationDescription) ? missionData === null || missionData === void 0 ? void 0 : missionData.operationDescription : \"\",\n                                        onChange: ()=>{\n                                            setMissionData({\n                                                ...missionData,\n                                                operationDescription: document.getElementById(\"operation-outcome-description\").value\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1137,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1118,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1110,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 pb-4 pt-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2.5\",\n                        children: [\n                            timeline && (timeline === null || timeline === void 0 ? void 0 : timeline.map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_task_task__WEBPACK_IMPORTED_MODULE_27__.RecordCard, {\n                                    record: comment,\n                                    onEdit: handleEditComment,\n                                    onDelete: handleDeleteComment\n                                }, \"\".concat(comment.id, \"-record-\").concat(comment.time || \"\"), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1170,\n                                    columnNumber: 33\n                                }, this))),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                        onClick: ()=>setShowInputDetailsPanel(true),\n                                        children: \"Vessel details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1178,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.TooltipTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                                    onClick: handleCreateComment,\n                                                    disabled: !selectedEvent,\n                                                    children: \"Add notes/comments\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                    lineNumber: 1185,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1184,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.TooltipContent, {\n                                                hidden: (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id) > 0,\n                                                children: \"Please save the event first in order to create timeline!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1183,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                lineNumber: 1177,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1167,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1166,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1165,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1201,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                        onClick: handleSave,\n                        children: \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1207,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1200,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_17__.AlertDialogNew, {\n                openDialog: openCommentsDialog,\n                setOpenDialog: setOpenCommentsDialog,\n                size: \"xl\",\n                handleCreate: handleSaveComments,\n                title: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Update Comment\" : \"Create Comment\",\n                actionText: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Update\" : \"Create Comment\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                            htmlFor: \"comment-type\",\n                            label: \"Comment Type\",\n                            className: \"text-sm font-medium\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                id: \"comment-type\",\n                                options: commentTypes,\n                                value: commentTypes === null || commentTypes === void 0 ? void 0 : commentTypes.find((type)=>{\n                                    var _commentData_commentType;\n                                    return type.value == (commentData === null || commentData === void 0 ? void 0 : (_commentData_commentType = commentData.commentType) === null || _commentData_commentType === void 0 ? void 0 : _commentData_commentType.replaceAll(\"_\", \" \"));\n                                }),\n                                onChange: (value)=>setCommentData({\n                                        ...commentData,\n                                        commentType: value === null || value === void 0 ? void 0 : value.value\n                                    }),\n                                placeholder: \"Select comment type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                lineNumber: 1223,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1219,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                    htmlFor: \"comment_time\",\n                                    className: \"flex items-center gap-2\",\n                                    children: \"Time of Completion\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1245,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    time: commentTime,\n                                    handleTimeChange: (date)=>{\n                                        handleCommentTimeChange(date);\n                                    },\n                                    timeID: \"comment_time\",\n                                    fieldName: \"comment_time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1244,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                    htmlFor: \"comment\",\n                                    children: \"Comment Content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1261,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    id: \"comment\",\n                                    placeholder: \"Write your comment here...\",\n                                    className: \"w-full min-h-[150px] bg-secondary-foreground\",\n                                    content: content,\n                                    handleEditorChange: handleEditorChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1262,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1260,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                    htmlFor: \"author\",\n                                    className: \"flex items-center gap-2\",\n                                    children: \"Author\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1272,\n                                    columnNumber: 25\n                                }, this),\n                                members && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                    id: \"author\",\n                                    options: crewMemberOptions,\n                                    value: crewMemberOptions === null || crewMemberOptions === void 0 ? void 0 : crewMemberOptions.find((member)=>{\n                                        var _commentData_author;\n                                        return member.value == (commentData === null || commentData === void 0 ? void 0 : (_commentData_author = commentData.author) === null || _commentData_author === void 0 ? void 0 : _commentData_author.id);\n                                    }),\n                                    onChange: (value)=>setCommentData({\n                                            ...commentData,\n                                            authorID: value === null || value === void 0 ? void 0 : value.value\n                                        }),\n                                    placeholder: \"Select crew\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1278,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1271,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1218,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1209,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_17__.AlertDialogNew, {\n                openDialog: deleteCommentsDialog,\n                setOpenDialog: setDeleteCommentsDialog,\n                handleCreate: handleDeleteComments,\n                title: \"Delete Comment\",\n                variant: \"danger\",\n                actionText: \"Confirm delete\",\n                children: \"Are you sure you want to delete this comment? This action cannot be undone and all associated data will be permanently removed from the system.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1297,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.Sheet, {\n                open: showInputDetailsP,\n                onOpenChange: (open)=>setShowInputDetailsPanel(open),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-full max-w-md sm:max-w-xl bg-background phablet:bg-muted\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetHeader, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1314,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col h-full min-h-[400px] overflow-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-grow space-y-6 py-4 mx-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Target Vessel Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1322,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1321,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                                                children: \"Record vessel name, callsign and number of people on board\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1326,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1320,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"vessel-name\",\n                                                                        children: \"Vessel Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1333,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"vessel-name\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter vessel name\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselName) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselName: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1336,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1332,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"call-sign\",\n                                                                        children: \"Call Sign\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1354,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"call-sign\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter call sign\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.callSign) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                callSign: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1357,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1353,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"pob\",\n                                                                        children: \"People On Board (POB)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1375,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"pob\",\n                                                                        type: \"number\",\n                                                                        placeholder: \"Enter number of people\",\n                                                                        min: 1,\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.pob) || 0,\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                pob: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1378,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1374,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1331,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1319,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Vessel Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1399,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1398,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                                                children: \"Include details of vessel type, make and descriptors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1403,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1397,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"vessel-length\",\n                                                                        children: \"Number of Vessels\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1410,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"vessel-length\",\n                                                                        type: \"number\",\n                                                                        placeholder: \"Enter vessel length\",\n                                                                        min: 1,\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselLength) || 0,\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselLength: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1413,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1409,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"vessel-type\",\n                                                                        children: \"Vessel type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1433,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                                                        options: vesselTypes,\n                                                                        value: vesselTypes === null || vesselTypes === void 0 ? void 0 : vesselTypes.find((type)=>type.value == (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselType)),\n                                                                        onChange: (value)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselType: value === null || value === void 0 ? void 0 : value.value\n                                                                            });\n                                                                        },\n                                                                        placeholder: \"Select vessel type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1436,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1432,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselType) == \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"vessel-type-description\",\n                                                                        children: \"Vessel type description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1456,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                                                        id: \"vessel-type-description\",\n                                                                        rows: 3,\n                                                                        placeholder: \"Describe the vessel type\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselTypeDescription) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselTypeDescription: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1459,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1455,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"make\",\n                                                                        children: \"Make and odel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1479,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"make\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter make and model\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.makeAndModel) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                makeAndModel: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1482,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1478,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"color\",\n                                                                        children: \"Color\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1501,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"color\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter vessel color\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.color) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                color: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1502,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1500,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1408,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1396,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Owner's Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1522,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1521,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                                                children: \"Record vessel owner's details and membership number if applicable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1526,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1520,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                                htmlFor: \"owner-name\",\n                                                                                children: \"Owner's Name\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1534,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                                id: \"owner-name\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter owner's name\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.ownerName) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        ownerName: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1537,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1533,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                                htmlFor: \"owner-phone\",\n                                                                                children: \"Phone Number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1556,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                                id: \"owner-phone\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter phone number\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.phone) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        phone: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1559,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1555,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1532,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                                htmlFor: \"cgnz\",\n                                                                                children: \"Coastguard NZ Membership\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1579,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                                id: \"cgnz\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter membership number\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.cgMembership) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        cgMembership: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1582,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1578,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                                htmlFor: \"owner-email\",\n                                                                                children: \"Email Address\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1601,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                                id: \"owner-email\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter email address\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.email) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        email: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1604,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1600,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1577,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"owner-address\",\n                                                                        children: \"Owner's Address\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1623,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                                                        id: \"owner-address\",\n                                                                        rows: 3,\n                                                                        placeholder: \"Enter owner's address\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.address) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                address: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1626,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1622,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 pt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                    htmlFor: \"owner-onboard\",\n                                                                    className: \"cursor-pointer\",\n                                                                    label: \"Is the owner on-board?\",\n                                                                    leftContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_22__.Checkbox, {\n                                                                        id: \"owner-onboard\",\n                                                                        checked: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.ownerOnBoard) || false,\n                                                                        size: \"lg\",\n                                                                        isRadioStyle: true,\n                                                                        onCheckedChange: (checked)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                ownerOnBoard: checked === true\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1648,\n                                                                        columnNumber: 53\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1643,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1642,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1531,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1519,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1317,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1316,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                        onClick: ()=>setShowInputDetailsPanel(false),\n                                        children: \"Save Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1677,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1675,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1315,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1311,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1308,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n        lineNumber: 1107,\n        columnNumber: 9\n    }, this);\n}\n_s(VesselRescueFields, \"NdmyJLvu/06md10blforhEP93qE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_23__.useSearchParams,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_28__.useMediaQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery\n    ];\n});\n_c = VesselRescueFields;\nvar _c;\n$RefreshReg$(_c, \"VesselRescueFields\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx\n"));

/***/ })

});