"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/logbook/forms/vessel-rescue.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VesselRescue; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Pencil_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Pencil!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Pencil_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Pencil!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/dateHelper */ \"(app-pages-browser)/./src/app/helpers/dateHelper.ts\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _app_offline_models_eventType_VesselRescue__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/eventType_VesselRescue */ \"(app-pages-browser)/./src/app/offline/models/eventType_VesselRescue.js\");\n/* harmony import */ var _app_offline_models_cgEventMission__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/cgEventMission */ \"(app-pages-browser)/./src/app/offline/models/cgEventMission.js\");\n/* harmony import */ var _app_offline_models_missionTimeline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/missionTimeline */ \"(app-pages-browser)/./src/app/offline/models/missionTimeline.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction VesselRescue(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, locked, offline = false } = param;\n    var _rescueData_operationType;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [openCommentsDialog, setOpenCommentsDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [commentTime, setCommentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [rescueData, setRescueData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [missionData, setMissionData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [commentData, setCommentData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const memberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const vesselRescueModel = new _app_offline_models_eventType_VesselRescue__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const cgEventMissionModel = new _app_offline_models_cgEventMission__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n    const missionTimelineModel = new _app_offline_models_missionTimeline__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n    const handleTimeChange = (date)=>{\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setRescueData(false);\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setRescueData(false);\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentEvent = async (id)=>{\n        if (offline) {\n            const event = await tripEventModel.getById(id);\n            if (event) {\n                var _event_eventType_VesselRescue, _event_eventType_VesselRescue1, _event_eventType_VesselRescue2, _event_eventType_VesselRescue3, _event_eventType_VesselRescue4, _event_eventType_VesselRescue5, _event_eventType_VesselRescue6, _event_eventType_VesselRescue7, _event_eventType_VesselRescue8, _event_eventType_VesselRescue9, _event_eventType_VesselRescue10, _event_eventType_VesselRescue11, _event_eventType_VesselRescue12, _event_eventType_VesselRescue13, _event_eventType_VesselRescue14, _event_eventType_VesselRescue15, _event_eventType_VesselRescue16, _event_eventType_VesselRescue_mission, _event_eventType_VesselRescue17, _event_eventType_VesselRescue18, _event_eventType_VesselRescue19, _event_eventType_VesselRescue20, _event_eventType_VesselRescue_mission1, _event_eventType_VesselRescue21, _event_eventType_VesselRescue_mission_missionType, _event_eventType_VesselRescue_mission2, _event_eventType_VesselRescue22, _event_eventType_VesselRescue_mission3, _event_eventType_VesselRescue23, _event_eventType_VesselRescue_mission_operationOutcome, _event_eventType_VesselRescue_mission4, _event_eventType_VesselRescue24, _event_eventType_VesselRescue_mission_currentLocation, _event_eventType_VesselRescue_mission5, _event_eventType_VesselRescue25, _event_eventType_VesselRescue_mission6, _event_eventType_VesselRescue26, _event_eventType_VesselRescue_mission_missionTimeline, _event_eventType_VesselRescue_mission7, _event_eventType_VesselRescue27;\n                setRescueData({\n                    vesselName: (_event_eventType_VesselRescue = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue === void 0 ? void 0 : _event_eventType_VesselRescue.vesselName,\n                    callSign: (_event_eventType_VesselRescue1 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue1 === void 0 ? void 0 : _event_eventType_VesselRescue1.callSign,\n                    pob: (_event_eventType_VesselRescue2 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue2 === void 0 ? void 0 : _event_eventType_VesselRescue2.pob,\n                    latitude: (_event_eventType_VesselRescue3 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue3 === void 0 ? void 0 : _event_eventType_VesselRescue3.latitude,\n                    longitude: (_event_eventType_VesselRescue4 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue4 === void 0 ? void 0 : _event_eventType_VesselRescue4.longitude,\n                    locationDescription: (_event_eventType_VesselRescue5 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue5 === void 0 ? void 0 : _event_eventType_VesselRescue5.locationDescription,\n                    vesselLength: (_event_eventType_VesselRescue6 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue6 === void 0 ? void 0 : _event_eventType_VesselRescue6.vesselLength,\n                    vesselType: (_event_eventType_VesselRescue7 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue7 === void 0 ? void 0 : _event_eventType_VesselRescue7.vesselType,\n                    makeAndModel: (_event_eventType_VesselRescue8 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue8 === void 0 ? void 0 : _event_eventType_VesselRescue8.makeAndModel,\n                    color: (_event_eventType_VesselRescue9 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue9 === void 0 ? void 0 : _event_eventType_VesselRescue9.color,\n                    ownerName: (_event_eventType_VesselRescue10 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue10 === void 0 ? void 0 : _event_eventType_VesselRescue10.ownerName,\n                    phone: (_event_eventType_VesselRescue11 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue11 === void 0 ? void 0 : _event_eventType_VesselRescue11.phone,\n                    email: (_event_eventType_VesselRescue12 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue12 === void 0 ? void 0 : _event_eventType_VesselRescue12.email,\n                    address: (_event_eventType_VesselRescue13 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue13 === void 0 ? void 0 : _event_eventType_VesselRescue13.address,\n                    ownerOnBoard: (_event_eventType_VesselRescue14 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue14 === void 0 ? void 0 : _event_eventType_VesselRescue14.ownerOnBoard,\n                    cgMembership: (_event_eventType_VesselRescue15 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue15 === void 0 ? void 0 : _event_eventType_VesselRescue15.cgMembership,\n                    locationID: (_event_eventType_VesselRescue16 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue16 === void 0 ? void 0 : _event_eventType_VesselRescue16.vesselLocationID,\n                    missionID: (_event_eventType_VesselRescue17 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue17 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission = _event_eventType_VesselRescue17.mission) === null || _event_eventType_VesselRescue_mission === void 0 ? void 0 : _event_eventType_VesselRescue_mission.id,\n                    operationType: ((_event_eventType_VesselRescue18 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue18 === void 0 ? void 0 : _event_eventType_VesselRescue18.operationType) ? operationType.filter((operation)=>{\n                        var _event_eventType_VesselRescue;\n                        return (_event_eventType_VesselRescue = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue === void 0 ? void 0 : _event_eventType_VesselRescue.operationType.split(\",\").includes(operation.value);\n                    }) : [],\n                    operationDescription: (_event_eventType_VesselRescue19 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue19 === void 0 ? void 0 : _event_eventType_VesselRescue19.operationDescription,\n                    vesselTypeDescription: (_event_eventType_VesselRescue20 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue20 === void 0 ? void 0 : _event_eventType_VesselRescue20.vesselTypeDescription\n                });\n                // Handle completedAt time format safely\n                const completedAt = (_event_eventType_VesselRescue21 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue21 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission1 = _event_eventType_VesselRescue21.mission) === null || _event_eventType_VesselRescue_mission1 === void 0 ? void 0 : _event_eventType_VesselRescue_mission1.completedAt;\n                if (completedAt) {\n                    // If it's already in HH:mm format, use it directly\n                    if (typeof completedAt === \"string\" && completedAt.match(/^\\d{2}:\\d{2}$/)) {\n                        setTime(completedAt);\n                    } else {\n                        // Parse as datetime and extract time\n                        const parsedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(completedAt);\n                        if (parsedTime.isValid()) {\n                            setTime(parsedTime.format(\"HH:mm\"));\n                        } else {\n                            setTime(undefined);\n                        }\n                    }\n                } else {\n                    setTime(undefined);\n                }\n                setMissionData({\n                    missionType: (_event_eventType_VesselRescue22 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue22 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission2 = _event_eventType_VesselRescue22.mission) === null || _event_eventType_VesselRescue_mission2 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission_missionType = _event_eventType_VesselRescue_mission2.missionType) === null || _event_eventType_VesselRescue_mission_missionType === void 0 ? void 0 : _event_eventType_VesselRescue_mission_missionType.replaceAll(\"_\", \" \"),\n                    description: (_event_eventType_VesselRescue23 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue23 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission3 = _event_eventType_VesselRescue23.mission) === null || _event_eventType_VesselRescue_mission3 === void 0 ? void 0 : _event_eventType_VesselRescue_mission3.description,\n                    operationOutcome: (_event_eventType_VesselRescue24 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue24 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission4 = _event_eventType_VesselRescue24.mission) === null || _event_eventType_VesselRescue_mission4 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission_operationOutcome = _event_eventType_VesselRescue_mission4.operationOutcome) === null || _event_eventType_VesselRescue_mission_operationOutcome === void 0 ? void 0 : _event_eventType_VesselRescue_mission_operationOutcome.replaceAll(\"_\", \" \"),\n                    currentLocationID: (_event_eventType_VesselRescue25 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue25 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission5 = _event_eventType_VesselRescue25.mission) === null || _event_eventType_VesselRescue_mission5 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission_currentLocation = _event_eventType_VesselRescue_mission5.currentLocation) === null || _event_eventType_VesselRescue_mission_currentLocation === void 0 ? void 0 : _event_eventType_VesselRescue_mission_currentLocation.id,\n                    operationDescription: (_event_eventType_VesselRescue26 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue26 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission6 = _event_eventType_VesselRescue26.mission) === null || _event_eventType_VesselRescue_mission6 === void 0 ? void 0 : _event_eventType_VesselRescue_mission6.operationDescription\n                });\n                setTimeline((_event_eventType_VesselRescue27 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue27 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission7 = _event_eventType_VesselRescue27.mission) === null || _event_eventType_VesselRescue_mission7 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission_missionTimeline = _event_eventType_VesselRescue_mission7.missionTimeline) === null || _event_eventType_VesselRescue_mission_missionTimeline === void 0 ? void 0 : _event_eventType_VesselRescue_mission_missionTimeline.nodes);\n            }\n        } else {\n            getTripEvent({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_VesselRescue, _event_eventType_VesselRescue1, _event_eventType_VesselRescue2, _event_eventType_VesselRescue3, _event_eventType_VesselRescue4, _event_eventType_VesselRescue5, _event_eventType_VesselRescue6, _event_eventType_VesselRescue7, _event_eventType_VesselRescue8, _event_eventType_VesselRescue9, _event_eventType_VesselRescue10, _event_eventType_VesselRescue11, _event_eventType_VesselRescue12, _event_eventType_VesselRescue13, _event_eventType_VesselRescue14, _event_eventType_VesselRescue15, _event_eventType_VesselRescue16, _event_eventType_VesselRescue_mission, _event_eventType_VesselRescue17, _event_eventType_VesselRescue18, _event_eventType_VesselRescue19, _event_eventType_VesselRescue20, _event_eventType_VesselRescue_mission1, _event_eventType_VesselRescue21, _event_eventType_VesselRescue_mission_missionType, _event_eventType_VesselRescue_mission2, _event_eventType_VesselRescue22, _event_eventType_VesselRescue_mission3, _event_eventType_VesselRescue23, _event_eventType_VesselRescue_mission_operationOutcome, _event_eventType_VesselRescue_mission4, _event_eventType_VesselRescue24, _event_eventType_VesselRescue_mission_currentLocation, _event_eventType_VesselRescue_mission5, _event_eventType_VesselRescue25, _event_eventType_VesselRescue_mission6, _event_eventType_VesselRescue26, _event_eventType_VesselRescue_mission_missionTimeline, _event_eventType_VesselRescue_mission7, _event_eventType_VesselRescue27;\n                setRescueData({\n                    vesselName: (_event_eventType_VesselRescue = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue === void 0 ? void 0 : _event_eventType_VesselRescue.vesselName,\n                    callSign: (_event_eventType_VesselRescue1 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue1 === void 0 ? void 0 : _event_eventType_VesselRescue1.callSign,\n                    pob: (_event_eventType_VesselRescue2 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue2 === void 0 ? void 0 : _event_eventType_VesselRescue2.pob,\n                    latitude: (_event_eventType_VesselRescue3 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue3 === void 0 ? void 0 : _event_eventType_VesselRescue3.latitude,\n                    longitude: (_event_eventType_VesselRescue4 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue4 === void 0 ? void 0 : _event_eventType_VesselRescue4.longitude,\n                    locationDescription: (_event_eventType_VesselRescue5 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue5 === void 0 ? void 0 : _event_eventType_VesselRescue5.locationDescription,\n                    vesselLength: (_event_eventType_VesselRescue6 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue6 === void 0 ? void 0 : _event_eventType_VesselRescue6.vesselLength,\n                    vesselType: (_event_eventType_VesselRescue7 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue7 === void 0 ? void 0 : _event_eventType_VesselRescue7.vesselType,\n                    makeAndModel: (_event_eventType_VesselRescue8 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue8 === void 0 ? void 0 : _event_eventType_VesselRescue8.makeAndModel,\n                    color: (_event_eventType_VesselRescue9 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue9 === void 0 ? void 0 : _event_eventType_VesselRescue9.color,\n                    ownerName: (_event_eventType_VesselRescue10 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue10 === void 0 ? void 0 : _event_eventType_VesselRescue10.ownerName,\n                    phone: (_event_eventType_VesselRescue11 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue11 === void 0 ? void 0 : _event_eventType_VesselRescue11.phone,\n                    email: (_event_eventType_VesselRescue12 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue12 === void 0 ? void 0 : _event_eventType_VesselRescue12.email,\n                    address: (_event_eventType_VesselRescue13 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue13 === void 0 ? void 0 : _event_eventType_VesselRescue13.address,\n                    ownerOnBoard: (_event_eventType_VesselRescue14 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue14 === void 0 ? void 0 : _event_eventType_VesselRescue14.ownerOnBoard,\n                    cgMembership: (_event_eventType_VesselRescue15 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue15 === void 0 ? void 0 : _event_eventType_VesselRescue15.cgMembership,\n                    locationID: (_event_eventType_VesselRescue16 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue16 === void 0 ? void 0 : _event_eventType_VesselRescue16.vesselLocationID,\n                    missionID: (_event_eventType_VesselRescue17 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue17 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission = _event_eventType_VesselRescue17.mission) === null || _event_eventType_VesselRescue_mission === void 0 ? void 0 : _event_eventType_VesselRescue_mission.id,\n                    operationType: ((_event_eventType_VesselRescue18 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue18 === void 0 ? void 0 : _event_eventType_VesselRescue18.operationType) ? operationType.filter((operation)=>{\n                        var _event_eventType_VesselRescue;\n                        return (_event_eventType_VesselRescue = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue === void 0 ? void 0 : _event_eventType_VesselRescue.operationType.split(\",\").includes(operation.value);\n                    }) : [],\n                    operationDescription: (_event_eventType_VesselRescue19 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue19 === void 0 ? void 0 : _event_eventType_VesselRescue19.operationDescription,\n                    vesselTypeDescription: (_event_eventType_VesselRescue20 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue20 === void 0 ? void 0 : _event_eventType_VesselRescue20.vesselTypeDescription\n                });\n                // Handle completedAt time format safely\n                const completedAt = (_event_eventType_VesselRescue21 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue21 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission1 = _event_eventType_VesselRescue21.mission) === null || _event_eventType_VesselRescue_mission1 === void 0 ? void 0 : _event_eventType_VesselRescue_mission1.completedAt;\n                if (completedAt) {\n                    // If it's already in HH:mm format, use it directly\n                    if (typeof completedAt === \"string\" && completedAt.match(/^\\d{2}:\\d{2}$/)) {\n                        setTime(completedAt);\n                    } else {\n                        // Parse as datetime and extract time\n                        const parsedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(completedAt);\n                        if (parsedTime.isValid()) {\n                            setTime(parsedTime.format(\"HH:mm\"));\n                        } else {\n                            setTime(undefined);\n                        }\n                    }\n                } else {\n                    setTime(undefined);\n                }\n                setMissionData({\n                    missionType: (_event_eventType_VesselRescue22 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue22 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission2 = _event_eventType_VesselRescue22.mission) === null || _event_eventType_VesselRescue_mission2 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission_missionType = _event_eventType_VesselRescue_mission2.missionType) === null || _event_eventType_VesselRescue_mission_missionType === void 0 ? void 0 : _event_eventType_VesselRescue_mission_missionType.replaceAll(\"_\", \" \"),\n                    description: (_event_eventType_VesselRescue23 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue23 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission3 = _event_eventType_VesselRescue23.mission) === null || _event_eventType_VesselRescue_mission3 === void 0 ? void 0 : _event_eventType_VesselRescue_mission3.description,\n                    operationOutcome: (_event_eventType_VesselRescue24 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue24 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission4 = _event_eventType_VesselRescue24.mission) === null || _event_eventType_VesselRescue_mission4 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission_operationOutcome = _event_eventType_VesselRescue_mission4.operationOutcome) === null || _event_eventType_VesselRescue_mission_operationOutcome === void 0 ? void 0 : _event_eventType_VesselRescue_mission_operationOutcome.replaceAll(\"_\", \" \"),\n                    currentLocationID: (_event_eventType_VesselRescue25 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue25 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission5 = _event_eventType_VesselRescue25.mission) === null || _event_eventType_VesselRescue_mission5 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission_currentLocation = _event_eventType_VesselRescue_mission5.currentLocation) === null || _event_eventType_VesselRescue_mission_currentLocation === void 0 ? void 0 : _event_eventType_VesselRescue_mission_currentLocation.id,\n                    operationDescription: (_event_eventType_VesselRescue26 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue26 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission6 = _event_eventType_VesselRescue26.mission) === null || _event_eventType_VesselRescue_mission6 === void 0 ? void 0 : _event_eventType_VesselRescue_mission6.operationDescription\n                });\n                setTimeline((_event_eventType_VesselRescue27 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue27 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission7 = _event_eventType_VesselRescue27.mission) === null || _event_eventType_VesselRescue_mission7 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission_missionTimeline = _event_eventType_VesselRescue_mission7.missionTimeline) === null || _event_eventType_VesselRescue_mission_missionTimeline === void 0 ? void 0 : _event_eventType_VesselRescue_mission_missionTimeline.nodes);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const handleSetMemberList = (members)=>{\n        var _members_filter;\n        setMembers(members === null || members === void 0 ? void 0 : (_members_filter = members.filter((member)=>member.archived == false && member.firstName != \"\")) === null || _members_filter === void 0 ? void 0 : _members_filter.map((member)=>({\n                label: member.firstName + \" \" + member.surname,\n                value: member.id\n            })));\n    };\n    if (!offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getSeaLogsMembersList)(handleSetMemberList);\n    }\n    const offlineGetMembers = async ()=>{\n        const members = await memberModel.getAll();\n        handleSetMemberList(members);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (offline) {\n            offlineGetMembers();\n        }\n    }, [\n        offline\n    ]);\n    const handleCommentTimeChange = (date)=>{\n        setCommentTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"DD/MM/YYYY HH:mm\"));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (geoLocations) {\n            setLocations(geoLocations.map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                })));\n        }\n    }, [\n        geoLocations\n    ]);\n    const vesselTypes = [\n        {\n            label: \"Commercial\",\n            value: \"Commercial\"\n        },\n        {\n            label: \"Recreation\",\n            value: \"Recreation\"\n        },\n        // { label: 'Power', value: 'Power' },\n        {\n            label: \"Sail\",\n            value: \"Sail\"\n        },\n        {\n            label: \"Paddle crafts\",\n            value: \"Paddle crafts\"\n        },\n        {\n            label: \"PWC\",\n            value: \"PWC\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const missions = [\n        {\n            label: \"To locate\",\n            value: \"To locate\"\n        },\n        {\n            label: \"To assist\",\n            value: \"To assist\"\n        },\n        {\n            label: \"To save\",\n            value: \"To save\"\n        },\n        {\n            label: \"To rescue\",\n            value: \"To rescue\"\n        },\n        {\n            label: \"To remove\",\n            value: \"To remove\"\n        }\n    ];\n    const operationOutcomes = [\n        {\n            label: \"Assisted by others\",\n            value: \"Assisted by others\"\n        },\n        {\n            label: \"Assisted on scene\",\n            value: \"Assisted on scene\"\n        },\n        {\n            label: \"Medical treatment\",\n            value: \"Medical treatment\"\n        },\n        {\n            label: \"Safe and well\",\n            value: \"Safe and well\"\n        },\n        {\n            label: \"Not located\",\n            value: \"Not located\"\n        },\n        {\n            label: \"Not recoverable\",\n            value: \"Not recoverable\"\n        },\n        {\n            label: \"Fatality\",\n            value: \"Fatality\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const commentTypes = [\n        {\n            label: \"General\",\n            value: \"General\"\n        },\n        {\n            label: \"Underway\",\n            value: \"Underway\"\n        },\n        {\n            label: \"On Scene\",\n            value: \"On Scene\"\n        }\n    ];\n    const operationType = [\n        {\n            label: \"Mechanical / equipment failure\",\n            value: \"Mechanical / equipment failure\"\n        },\n        {\n            label: \"Vessel adrift\",\n            value: \"Vessel adrift\"\n        },\n        {\n            label: \"Vessel aground\",\n            value: \"Vessel aground\"\n        },\n        {\n            label: \"Capsize\",\n            value: \"Capsize\"\n        },\n        {\n            label: \"Vessel requiring tow\",\n            value: \"Vessel requiring tow\"\n        },\n        {\n            label: \"Flare sighting\",\n            value: \"Flare sighting\"\n        },\n        {\n            label: \"Vessel sinking\",\n            value: \"Vessel sinking\"\n        },\n        {\n            label: \"Collision\",\n            value: \"Collision\"\n        },\n        {\n            label: \"Vessel overdue\",\n            value: \"Vessel overdue\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const handleSaveComments = async ()=>{\n        if ((rescueData === null || rescueData === void 0 ? void 0 : rescueData.missionID) === undefined) {\n            sonner__WEBPACK_IMPORTED_MODULE_22__.toast.error(\"Please save the event first in order to create timeline!\");\n            setOpenCommentsDialog(false);\n            return;\n        }\n        const variables = {\n            input: {\n                commentType: commentData === null || commentData === void 0 ? void 0 : commentData.commentType,\n                description: content ? content : \"\",\n                time: commentTime ? commentTime : dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"DD/MM/YYYY HH:mm\"),\n                authorID: commentData === null || commentData === void 0 ? void 0 : commentData.authorID,\n                missionID: rescueData === null || rescueData === void 0 ? void 0 : rescueData.missionID\n            }\n        };\n        if ((commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0) {\n            if (offline) {\n                await missionTimelineModel.save({\n                    id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                    ...variables.input\n                });\n                setOpenCommentsDialog(false);\n                getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n            } else {\n                updateMissionTimeline({\n                    variables: {\n                        input: {\n                            id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                await missionTimelineModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)(),\n                    ...variables.input\n                });\n                setOpenCommentsDialog(false);\n                getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n            } else {\n                createMissionTimeline({\n                    variables: {\n                        input: {\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createMissionTimeline] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateMissionTimeline, {\n        onCompleted: (response)=>{\n            setOpenCommentsDialog(false);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating mission timeline\", error);\n        }\n    });\n    const [updateMissionTimeline] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateMissionTimeline, {\n        onCompleted: (response)=>{\n            setOpenCommentsDialog(false);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating mission timeline\", error);\n        }\n    });\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleSave = async ()=>{\n        var _rescueData_operationType;\n        const variables = {\n            input: {\n                vesselName: rescueData.vesselName,\n                callSign: rescueData.callSign,\n                pob: +rescueData.pob,\n                latitude: rescueData.latitude,\n                longitude: rescueData.longitude,\n                locationDescription: rescueData.locationDescription,\n                vesselLength: +rescueData.vesselLength,\n                vesselType: rescueData.vesselType,\n                makeAndModel: rescueData.makeAndModel,\n                color: rescueData.color,\n                ownerName: rescueData.ownerName,\n                phone: rescueData.phone,\n                email: rescueData.email,\n                address: rescueData.address,\n                ownerOnBoard: rescueData.ownerOnBoard,\n                cgMembershipType: \"cgnz\",\n                cgMembership: rescueData.cgMembership,\n                missionID: rescueData.missionID,\n                vesselLocationID: rescueData.locationID,\n                operationType: (_rescueData_operationType = rescueData.operationType) === null || _rescueData_operationType === void 0 ? void 0 : _rescueData_operationType.map((type)=>type.value).join(\",\"),\n                operationDescription: rescueData.operationDescription,\n                vesselTypeDescription: rescueData.vesselTypeDescription\n            }\n        };\n        const mission = {\n            input: {\n                missionType: \"VesselRescue\",\n                description: missionData.description,\n                operationDescription: missionData.operationDescription,\n                operationOutcome: missionData.operationOutcome,\n                completedAt: time,\n                currentLocationID: missionData.currentLocationID,\n                eventID: 0,\n                eventType: \"VesselRescue\",\n                missionTimeline: []\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                await tripEventModel.save({\n                    id: +currentEvent.id,\n                    eventCategory: \"VesselRescue\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            } else {\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +currentEvent.id,\n                            eventCategory: \"VesselRescue\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n            if (offline) {\n                const data = await vesselRescueModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_VesselRescueID),\n                    ...variables.input\n                });\n                const missionDataToSave = {\n                    missionType: missionData.missionType,\n                    description: missionData.description,\n                    operationDescription: missionData.operationDescription,\n                    operationOutcome: missionData.operationOutcome,\n                    completedAt: time,\n                    currentLocationID: missionData.currentLocationID,\n                    eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                    eventType: \"VesselRescue\"\n                };\n                const id = +rescueData.missionID > 0 ? rescueData.missionID : (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)();\n                await cgEventMissionModel.save({\n                    id,\n                    ...missionDataToSave\n                });\n                getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport(currentTrip);\n                if (+rescueData.missionID > 0) {\n                    updateTripReport({\n                        id: tripReport.map((trip)=>trip.id)\n                    });\n                } else {\n                    updateTripReport({\n                        id: [\n                            ...tripReport.map((trip)=>trip.id),\n                            currentTrip.id\n                        ]\n                    });\n                }\n            } else {\n                updateEventType_VesselRescue({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_VesselRescueID),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                var _rescueData_operationType1;\n                const tripEventData = await tripEventModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)(),\n                    eventCategory: \"VesselRescue\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                setCurrentEvent(tripEventData);\n                const vesselRescueData = await vesselRescueModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)(),\n                    vesselName: rescueData.vesselName,\n                    callSign: rescueData.callSign,\n                    pob: +rescueData.pob,\n                    latitude: rescueData.latitude,\n                    longitude: rescueData.longitude,\n                    locationDescription: rescueData.locationDescription,\n                    vesselLength: +rescueData.vesselLength,\n                    vesselType: rescueData.vesselType,\n                    makeAndModel: rescueData.makeAndModel,\n                    color: rescueData.color,\n                    ownerName: rescueData.ownerName,\n                    phone: rescueData.phone,\n                    email: rescueData.email,\n                    address: rescueData.address,\n                    ownerOnBoard: rescueData.ownerOnBoard,\n                    cgMembershipType: \"cgnz\",\n                    cgMembership: rescueData.cgMembership,\n                    missionID: rescueData.missionID,\n                    vesselLocationID: rescueData.locationID,\n                    tripEventID: tripEventData.id,\n                    operationType: (_rescueData_operationType1 = rescueData.operationType) === null || _rescueData_operationType1 === void 0 ? void 0 : _rescueData_operationType1.map((type)=>type.value).join(\",\"),\n                    operationDescription: rescueData.operationDescription,\n                    vesselTypeDescription: rescueData.vesselTypeDescription\n                });\n                await cgEventMissionModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_10__.generateUniqueId)(),\n                    missionType: missionData.missionType,\n                    description: missionData.description,\n                    operationDescription: missionData.operationDescription,\n                    operationOutcome: missionData.operationOutcome,\n                    completedAt: time,\n                    currentLocationID: missionData.currentLocationID,\n                    eventID: +(vesselRescueData === null || vesselRescueData === void 0 ? void 0 : vesselRescueData.id),\n                    eventType: \"VesselRescue\"\n                });\n                await getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport(currentTrip);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                await tripEventModel.save({\n                    id: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id,\n                    eventType_VesselRescueID: vesselRescueData.id\n                });\n                await getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                closeModal();\n            } else {\n                createTripEvent({\n                    variables: {\n                        input: {\n                            eventCategory: \"VesselRescue\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripEvent, {\n        onCompleted: (response)=>{\n            var _rescueData_operationType;\n            const data = response.createTripEvent;\n            setCurrentEvent(data);\n            createEventType_VesselRescue({\n                variables: {\n                    input: {\n                        vesselName: rescueData.vesselName,\n                        callSign: rescueData.callSign,\n                        pob: +rescueData.pob,\n                        latitude: rescueData.latitude,\n                        longitude: rescueData.longitude,\n                        locationDescription: rescueData.locationDescription,\n                        vesselLength: +rescueData.vesselLength,\n                        vesselType: rescueData.vesselType,\n                        makeAndModel: rescueData.makeAndModel,\n                        color: rescueData.color,\n                        ownerName: rescueData.ownerName,\n                        phone: rescueData.phone,\n                        email: rescueData.email,\n                        address: rescueData.address,\n                        ownerOnBoard: rescueData.ownerOnBoard,\n                        cgMembershipType: \"cgnz\",\n                        cgMembership: rescueData.cgMembership,\n                        missionID: rescueData.missionID,\n                        vesselLocationID: rescueData.locationID,\n                        tripEventID: data.id,\n                        operationType: (_rescueData_operationType = rescueData.operationType) === null || _rescueData_operationType === void 0 ? void 0 : _rescueData_operationType.map((type)=>type.value).join(\",\"),\n                        operationDescription: rescueData.operationDescription,\n                        vesselTypeDescription: rescueData.vesselTypeDescription\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip event\", error);\n        }\n    });\n    const [createEventType_VesselRescue] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateEventType_VesselRescue, {\n        onCompleted: (response)=>{\n            const data = response.createEventType_VesselRescue;\n            createCGEventMission({\n                variables: {\n                    input: {\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: missionData.currentLocationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\"\n                    }\n                }\n            });\n            updateTripEvent({\n                variables: {\n                    input: {\n                        id: currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id,\n                        eventType_VesselRescueID: data.id\n                    }\n                }\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating vessel rescue\", error);\n        }\n    });\n    const [updateEventType_VesselRescue] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateEventType_VesselRescue, {\n        onCompleted: (response)=>{\n            const data = response.updateEventType_VesselRescue;\n            if (rescueData.missionID > 0) {\n                updateCGEventMission({\n                    variables: {\n                        input: {\n                            id: rescueData.missionID,\n                            missionType: missionData.missionType,\n                            description: missionData.description,\n                            operationDescription: missionData.operationDescription,\n                            operationOutcome: missionData.operationOutcome,\n                            completedAt: time,\n                            currentLocationID: missionData.currentLocationID,\n                            eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                            eventType: \"VesselRescue\"\n                        }\n                    }\n                });\n            } else {\n                createCGEventMission({\n                    variables: {\n                        input: {\n                            missionType: missionData.missionType,\n                            description: missionData.description,\n                            operationDescription: missionData.operationDescription,\n                            operationOutcome: missionData.operationOutcome,\n                            completedAt: time,\n                            currentLocationID: missionData.currentLocationID,\n                            eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                            eventType: \"VesselRescue\"\n                        }\n                    }\n                });\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error updating vessel rescue\", error);\n        }\n    });\n    const [updateTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripEvent, {\n        onCompleted: (response)=>{\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating trip event\", error);\n        }\n    });\n    const [createCGEventMission] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateCGEventMission, {\n        onCompleted: (response)=>{\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            updateTripReport(currentTrip);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error creating CG Event Mission\", error);\n        }\n    });\n    const [updateCGEventMission] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateCGEventMission, {\n        onCompleted: (response)=>{\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            updateTripReport(currentTrip);\n            updateTripReport({\n                id: tripReport.map((trip)=>trip.id)\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating CG Event Mission\", error);\n        }\n    });\n    var _rescueData_operationDescription, _rescueData_latitude, _rescueData_longitude, _rescueData_vesselLength, _rescueData_vesselTypeDescription, _rescueData_makeAndModel, _rescueData_color, _rescueData_ownerName, _rescueData_phone, _rescueData_cgMembership, _rescueData_email, _rescueData_address, _missionData_description, _missionData_operationDescription;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                                        children: \"Target vessel and details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                        children: \"Record vessel name and callsign and include number of people on board\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 801,\n                                columnNumber: 21\n                            }, this),\n                            operationType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_17__.Combobox, {\n                                id: \"operation-type\",\n                                options: operationType.map((opt)=>({\n                                        value: opt.value,\n                                        label: opt.label\n                                    })),\n                                multi: true,\n                                value: rescueData === null || rescueData === void 0 ? void 0 : rescueData.operationType,\n                                onChange: (value)=>{\n                                    setRescueData({\n                                        ...rescueData,\n                                        operationType: value\n                                    });\n                                },\n                                placeholder: \"Operation Type\",\n                                disabled: locked\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 809,\n                                columnNumber: 25\n                            }, this),\n                            (rescueData === null || rescueData === void 0 ? void 0 : (_rescueData_operationType = rescueData.operationType) === null || _rescueData_operationType === void 0 ? void 0 : _rescueData_operationType.find((operation)=>operation.value == \"Other\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                    id: \"operation-description\",\n                                    rows: 4,\n                                    placeholder: \"Operation description\",\n                                    value: (_rescueData_operationDescription = rescueData === null || rescueData === void 0 ? void 0 : rescueData.operationDescription) !== null && _rescueData_operationDescription !== void 0 ? _rescueData_operationDescription : \"\",\n                                    disabled: locked,\n                                    onChange: (data)=>{\n                                        setRescueData({\n                                            ...rescueData,\n                                            operationDescription: data.target.value\n                                        });\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 830,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                label: \"Vessel Name\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                    id: \"vessel-name\",\n                                    type: \"text\",\n                                    placeholder: \"Vessel Name\",\n                                    value: rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselName,\n                                    disabled: locked,\n                                    onChange: (e)=>{\n                                        setRescueData({\n                                            ...rescueData,\n                                            vesselName: e.target.value\n                                        });\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                    lineNumber: 847,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 846,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                label: \"Call Sign\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                    id: \"call-sign\",\n                                    type: \"text\",\n                                    placeholder: \"Call Sign\",\n                                    value: rescueData === null || rescueData === void 0 ? void 0 : rescueData.callSign,\n                                    disabled: locked,\n                                    onChange: (e)=>{\n                                        setRescueData({\n                                            ...rescueData,\n                                            callSign: e.target.value\n                                        });\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                    lineNumber: 862,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                label: \"People on Board\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                    id: \"pob\",\n                                    type: \"number\",\n                                    placeholder: \"Enter POB\",\n                                    min: 1,\n                                    value: rescueData === null || rescueData === void 0 ? void 0 : rescueData.pob,\n                                    disabled: locked,\n                                    onChange: (e)=>{\n                                        setRescueData({\n                                            ...rescueData,\n                                            pob: e.target.value\n                                        });\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                    lineNumber: 878,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 877,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 800,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 894,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                                        children: \"Vessel location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 897,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                        children: \"Record the approximate location of vessel requiring assistance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 896,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                label: \"Vessel position\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                            id: \"location-lat\",\n                                            type: \"text\",\n                                            placeholder: \"Latitude\",\n                                            value: (_rescueData_latitude = rescueData === null || rescueData === void 0 ? void 0 : rescueData.latitude) !== null && _rescueData_latitude !== void 0 ? _rescueData_latitude : \"\",\n                                            disabled: locked,\n                                            onChange: (e)=>{\n                                                setRescueData({\n                                                    ...rescueData,\n                                                    latitude: e.target.value\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                            id: \"location-long\",\n                                            type: \"text\",\n                                            placeholder: \"Longitude\",\n                                            value: (_rescueData_longitude = rescueData === null || rescueData === void 0 ? void 0 : rescueData.longitude) !== null && _rescueData_longitude !== void 0 ? _rescueData_longitude : \"\",\n                                            disabled: locked,\n                                            onChange: (e)=>{\n                                                setRescueData({\n                                                    ...rescueData,\n                                                    longitude: e.target.value\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                            lineNumber: 918,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 903,\n                                columnNumber: 21\n                            }, this),\n                            locations && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_17__.Combobox, {\n                                id: \"geo-location\",\n                                options: locations.map((loc)=>({\n                                        value: loc.value,\n                                        label: loc.label\n                                    })),\n                                value: (locations === null || locations === void 0 ? void 0 : locations.find((location)=>location.value == (rescueData === null || rescueData === void 0 ? void 0 : rescueData.locationID))) || null,\n                                onChange: (value)=>{\n                                    setRescueData({\n                                        ...rescueData,\n                                        locationID: value === null || value === void 0 ? void 0 : value.value\n                                    });\n                                },\n                                placeholder: \"Select location\",\n                                disabled: locked\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 934,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                id: \"location-description\",\n                                rows: 4,\n                                placeholder: \"Location description\",\n                                value: rescueData === null || rescueData === void 0 ? void 0 : rescueData.locationDescription,\n                                disabled: locked,\n                                onChange: (e)=>{\n                                    setRescueData({\n                                        ...rescueData,\n                                        locationDescription: e.target.value\n                                    });\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 957,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 895,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 971,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                                        children: \"Vessel description\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                        children: \"Include details of vessel type, make and descriptors\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 975,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 973,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                label: \"Vessel Length\",\n                                htmlFor: \"vessel-length\",\n                                className: \"my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                    id: \"vessel-length\",\n                                    type: \"number\",\n                                    placeholder: \"Vessel Length\",\n                                    value: (_rescueData_vesselLength = rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselLength) !== null && _rescueData_vesselLength !== void 0 ? _rescueData_vesselLength : 0,\n                                    min: 1,\n                                    disabled: locked,\n                                    onChange: (e)=>{\n                                        setRescueData({\n                                            ...rescueData,\n                                            vesselLength: e.target.value\n                                        });\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 979,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_17__.Combobox, {\n                                id: \"vessel-type\",\n                                options: vesselTypes.map((type)=>({\n                                        value: type.value,\n                                        label: type.label\n                                    })),\n                                value: (vesselTypes === null || vesselTypes === void 0 ? void 0 : vesselTypes.find((type)=>type.value == (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselType))) || null,\n                                onChange: (value)=>{\n                                    setRescueData({\n                                        ...rescueData,\n                                        vesselType: value === null || value === void 0 ? void 0 : value.value\n                                    });\n                                },\n                                placeholder: \"Vessel type\",\n                                disabled: locked\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 21\n                            }, this),\n                            (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselType) == \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                    id: \"vessel-type-description\",\n                                    rows: 4,\n                                    placeholder: \"Vessel Description\",\n                                    value: (_rescueData_vesselTypeDescription = rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselTypeDescription) !== null && _rescueData_vesselTypeDescription !== void 0 ? _rescueData_vesselTypeDescription : \"\",\n                                    onChange: (data)=>{\n                                        setRescueData({\n                                            ...rescueData,\n                                            vesselTypeDescription: data.target.value\n                                        });\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1020,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                label: \"Make and model\",\n                                htmlFor: \"make\",\n                                className: \"my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                    id: \"make\",\n                                    type: \"text\",\n                                    placeholder: \"Make and model\",\n                                    value: (_rescueData_makeAndModel = rescueData === null || rescueData === void 0 ? void 0 : rescueData.makeAndModel) !== null && _rescueData_makeAndModel !== void 0 ? _rescueData_makeAndModel : \"\",\n                                    disabled: locked,\n                                    onChange: (e)=>{\n                                        setRescueData({\n                                            ...rescueData,\n                                            makeAndModel: e.target.value\n                                        });\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                    lineNumber: 1040,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1036,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                label: \"Color\",\n                                htmlFor: \"color\",\n                                className: \"my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                    id: \"color\",\n                                    type: \"text\",\n                                    placeholder: \"Color\",\n                                    value: (_rescueData_color = rescueData === null || rescueData === void 0 ? void 0 : rescueData.color) !== null && _rescueData_color !== void 0 ? _rescueData_color : \"\",\n                                    disabled: locked,\n                                    onChange: (e)=>{\n                                        setRescueData({\n                                            ...rescueData,\n                                            color: e.target.value\n                                        });\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                    lineNumber: 1055,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1054,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 972,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1070,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \" space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                                        children: \"Owners details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                        children: \"Record vessel owners’ details and membership number if applicable\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1074,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1072,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                        label: \"Owner's name\",\n                                        htmlFor: \"owner-name\",\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                            id: \"owner-name\",\n                                            type: \"text\",\n                                            placeholder: \"Owner's name\",\n                                            value: (_rescueData_ownerName = rescueData === null || rescueData === void 0 ? void 0 : rescueData.ownerName) !== null && _rescueData_ownerName !== void 0 ? _rescueData_ownerName : \"\",\n                                            disabled: locked,\n                                            onChange: (e)=>{\n                                                setRescueData({\n                                                    ...rescueData,\n                                                    ownerName: e.target.value\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                            lineNumber: 1084,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1080,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                        label: \"Phone number\",\n                                        htmlFor: \"owner-phone\",\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                            id: \"owner-phone\",\n                                            type: \"text\",\n                                            placeholder: \"Phone number\",\n                                            value: (_rescueData_phone = rescueData === null || rescueData === void 0 ? void 0 : rescueData.phone) !== null && _rescueData_phone !== void 0 ? _rescueData_phone : \"\",\n                                            disabled: locked,\n                                            onChange: (e)=>{\n                                                setRescueData({\n                                                    ...rescueData,\n                                                    phone: e.target.value\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                            lineNumber: 1102,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1098,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1079,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                        label: \"Coastguard NZ membership\",\n                                        htmlFor: \"cgnz\",\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                            id: \"cgnz\",\n                                            type: \"text\",\n                                            placeholder: \"Coastguard NZ membership\",\n                                            value: (_rescueData_cgMembership = rescueData === null || rescueData === void 0 ? void 0 : rescueData.cgMembership) !== null && _rescueData_cgMembership !== void 0 ? _rescueData_cgMembership : \"\",\n                                            disabled: locked,\n                                            onChange: (e)=>{\n                                                setRescueData({\n                                                    ...rescueData,\n                                                    cgMembership: e.target.value\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                            lineNumber: 1122,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1118,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                        label: \"Email address\",\n                                        htmlFor: \"owner-email\",\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                            id: \"owner-email\",\n                                            type: \"text\",\n                                            placeholder: \"Email Address\",\n                                            value: (_rescueData_email = rescueData === null || rescueData === void 0 ? void 0 : rescueData.email) !== null && _rescueData_email !== void 0 ? _rescueData_email : \"\",\n                                            disabled: locked,\n                                            onChange: (e)=>{\n                                                setRescueData({\n                                                    ...rescueData,\n                                                    email: e.target.value\n                                                });\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                            lineNumber: 1140,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1136,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1117,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                label: \"Owner's address\",\n                                htmlFor: \"owner-address\",\n                                className: \"my-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                    id: \"owner-address\",\n                                    rows: 4,\n                                    className: \"\",\n                                    placeholder: \"Owner's address\",\n                                    value: (_rescueData_address = rescueData === null || rescueData === void 0 ? void 0 : rescueData.address) !== null && _rescueData_address !== void 0 ? _rescueData_address : \"\",\n                                    disabled: locked,\n                                    onChange: (data)=>{\n                                        setRescueData({\n                                            ...rescueData,\n                                            address: data.target.value\n                                        });\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                    lineNumber: 1159,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1155,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                type: \"checkbox\",\n                                id: \"owner-on-board\",\n                                checked: rescueData === null || rescueData === void 0 ? void 0 : rescueData.ownerOnBoard,\n                                onCheckedChange: (checked)=>{\n                                    setRescueData({\n                                        ...rescueData,\n                                        ownerOnBoard: checked\n                                    });\n                                },\n                                disabled: locked,\n                                variant: \"warning\",\n                                label: \"Is the owner on-board?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1174,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1071,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1189,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                                        children: \"Mission\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1192,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                        children: \"Mission details and timeline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1193,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1191,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_17__.Combobox, {\n                                id: \"mission\",\n                                options: missions.map((mission)=>({\n                                        value: mission.value,\n                                        label: mission.label\n                                    })),\n                                value: (missions === null || missions === void 0 ? void 0 : missions.find((mission)=>mission.value == (missionData === null || missionData === void 0 ? void 0 : missionData.missionType))) || null,\n                                onChange: (value)=>{\n                                    setMissionData({\n                                        ...missionData,\n                                        missionType: value === null || value === void 0 ? void 0 : value.value\n                                    });\n                                },\n                                placeholder: \"Mission Type\",\n                                disabled: locked\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1195,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                id: \"mission-description\",\n                                rows: 4,\n                                placeholder: \"Mission description\",\n                                value: (_missionData_description = missionData === null || missionData === void 0 ? void 0 : missionData.description) !== null && _missionData_description !== void 0 ? _missionData_description : \"\",\n                                disabled: locked,\n                                onChange: (e)=>{\n                                    setMissionData({\n                                        ...missionData,\n                                        description: e.target.value\n                                    });\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1216,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                        children: \"Mission timeline\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1230,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 flex-col\",\n                                        children: timeline && (timeline === null || timeline === void 0 ? void 0 : timeline.map((comment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-4 w-full mb-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4 justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"comment-html\",\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: comment.description\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                                            lineNumber: 1238,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-4\",\n                                                            children: [\n                                                                comment.author.id > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                                                    children: comment.author.firstName + \" \" + comment.author.surname\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                                                    lineNumber: 1245,\n                                                                    columnNumber: 53\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                                                    children: (0,_app_helpers_dateHelper__WEBPACK_IMPORTED_MODULE_7__.formatDateTime)(comment.time)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                                                    lineNumber: 1253,\n                                                                    columnNumber: 49\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                                                                    size: \"icon\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setOpenCommentsDialog(true);\n                                                                        setCommentData(comment);\n                                                                        handleEditorChange(comment.description);\n                                                                        setCommentTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(comment.time).format(\"DD/MM/YYYY HH:mm\"));\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Pencil_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                                                        lineNumber: 1277,\n                                                                        columnNumber: 53\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                                                    lineNumber: 1258,\n                                                                    columnNumber: 49\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                                            lineNumber: 1243,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                                lineNumber: 1234,\n                                                columnNumber: 37\n                                            }, this)))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1231,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>{\n                                                setOpenCommentsDialog(true);\n                                                handleEditorChange(\"\");\n                                                setCommentData(false);\n                                            },\n                                            children: \"Add Notes/Comments\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                            lineNumber: 1285,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1284,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1229,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1190,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1297,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                                        children: \"Mission complete\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1300,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                        children: \"Record the operation outcome, location and time of completion\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                        lineNumber: 1301,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1299,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_17__.Combobox, {\n                                id: \"operation-outcome\",\n                                options: operationOutcomes.map((outcome)=>({\n                                        value: outcome.value,\n                                        label: outcome.label\n                                    })),\n                                value: (operationOutcomes === null || operationOutcomes === void 0 ? void 0 : operationOutcomes.find((outcome)=>outcome.value == (missionData === null || missionData === void 0 ? void 0 : missionData.operationOutcome))) || null,\n                                onChange: (value)=>{\n                                    setMissionData({\n                                        ...missionData,\n                                        operationOutcome: value === null || value === void 0 ? void 0 : value.value\n                                    });\n                                },\n                                placeholder: \"Operation outcome\",\n                                disabled: locked\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1306,\n                                columnNumber: 21\n                            }, this),\n                            (missionData === null || missionData === void 0 ? void 0 : missionData.operationOutcome) == \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                id: \"operation-outcome-description\",\n                                rows: 4,\n                                placeholder: \"Description\",\n                                value: (_missionData_operationDescription = missionData === null || missionData === void 0 ? void 0 : missionData.operationDescription) !== null && _missionData_operationDescription !== void 0 ? _missionData_operationDescription : \"\",\n                                disabled: locked,\n                                onChange: (e)=>{\n                                    setMissionData({\n                                        ...missionData,\n                                        operationDescription: e.target.value\n                                    });\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1329,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_18__.TimePicker, {\n                                label: \"Time of completion\",\n                                value: time ? (()=>{\n                                    // Handle different time formats\n                                    if (typeof time === \"string\") {\n                                        // If it's already in HH:mm format\n                                        if (time.match(/^\\d{2}:\\d{2}$/)) {\n                                            const timeDate = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"YYYY-MM-DD\"), \" \").concat(time));\n                                            return timeDate.isValid() ? timeDate.toDate() : new Date();\n                                        }\n                                        // If it's a full datetime string\n                                        const parsedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(time);\n                                        return parsedTime.isValid() ? parsedTime.toDate() : new Date();\n                                    }\n                                    // If it's already a Date object\n                                    return dayjs__WEBPACK_IMPORTED_MODULE_1___default()(time).isValid() ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(time).toDate() : new Date();\n                                })() : new Date(),\n                                onChange: (date)=>{\n                                    handleTimeChange(date);\n                                },\n                                use24Hour: true,\n                                disabled: locked,\n                                className: \"w-full\",\n                                nowButton: true,\n                                nowButtonLabel: \"Set To Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1343,\n                                columnNumber: 21\n                            }, this),\n                            locations && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_17__.Combobox, {\n                                id: \"completed-geo-location\",\n                                options: locations.map((location)=>({\n                                        value: location.value,\n                                        label: location.label\n                                    })),\n                                value: (locations === null || locations === void 0 ? void 0 : locations.find((location)=>location.value == (missionData === null || missionData === void 0 ? void 0 : missionData.currentLocationID))) || null,\n                                onChange: (value)=>{\n                                    setMissionData({\n                                        ...missionData,\n                                        currentLocationID: value === null || value === void 0 ? void 0 : value.value\n                                    });\n                                },\n                                placeholder: \"Current Location\",\n                                disabled: locked\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1382,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1298,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_25__.FormFooter, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                                variant: \"back\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Pencil_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"],\n                                onClick: closeModal,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1407,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_21__.Button, {\n                                disabled: locked,\n                                onClick: handleSave,\n                                children: selectedEvent ? \"Update\" : \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                                lineNumber: 1413,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1406,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                lineNumber: 799,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_24__.AlertDialogNew, {\n                openDialog: openCommentsDialog,\n                setOpenDialog: setOpenCommentsDialog,\n                handleCreate: handleSaveComments,\n                title: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Update Comment\" : \"Create New Comment\",\n                size: \"xl\",\n                description: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Edit the comment details below.\" : \"Add a new comment to the mission timeline.\",\n                actionText: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Update\" : \"Create Comment\",\n                cancelText: \"Cancel\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_17__.Combobox, {\n                            id: \"comment-type\",\n                            options: commentTypes.map((type)=>({\n                                    value: type.value,\n                                    label: type.label\n                                })),\n                            value: (commentTypes === null || commentTypes === void 0 ? void 0 : commentTypes.find((type)=>{\n                                var _commentData_commentType;\n                                return type.value == (commentData === null || commentData === void 0 ? void 0 : (_commentData_commentType = commentData.commentType) === null || _commentData_commentType === void 0 ? void 0 : _commentData_commentType.replaceAll(\"_\", \" \"));\n                            })) || null,\n                            onChange: (value)=>setCommentData({\n                                    ...commentData,\n                                    commentType: value === null || value === void 0 ? void 0 : value.value\n                                }),\n                            placeholder: \"Comment type\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                            lineNumber: 1438,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1437,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_18__.TimePicker, {\n                            value: commentTime ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()(commentTime, \"DD/MM/YYYY HH:mm\").toDate() : new Date(),\n                            onChange: handleCommentTimeChange,\n                            use24Hour: true,\n                            className: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                            lineNumber: 1464,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1463,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        id: \"comment\",\n                        placeholder: \"Comment\",\n                        className: \"w-full\",\n                        content: content,\n                        handleEditorChange: handleEditorChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1479,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: members && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_17__.Combobox, {\n                            id: \"comment-author\",\n                            options: members.map((member)=>({\n                                    value: member.value,\n                                    label: member.label\n                                })),\n                            value: (members === null || members === void 0 ? void 0 : members.find((member)=>{\n                                var _commentData_author;\n                                return member.value == (commentData === null || commentData === void 0 ? void 0 : (_commentData_author = commentData.author) === null || _commentData_author === void 0 ? void 0 : _commentData_author.id);\n                            })) || null,\n                            onChange: (value)=>setCommentData({\n                                    ...commentData,\n                                    authorID: value === null || value === void 0 ? void 0 : value.value\n                                }),\n                            placeholder: \"Crew member\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                            lineNumber: 1489,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                        lineNumber: 1487,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue.tsx\",\n                lineNumber: 1419,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(VesselRescue, \"EIlLprrXOlnM262d96n8ZbMsjdo=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_27__.useMutation\n    ];\n});\n_c = VesselRescue;\nvar _c;\n$RefreshReg$(_c, \"VesselRescue\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue.tsx\n"));

/***/ })

});