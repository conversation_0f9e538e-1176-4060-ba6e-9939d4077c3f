"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/risk-strategies/page",{

/***/ "(app-pages-browser)/./src/app/ui/risk-evaluations/risk-strategies.tsx":
/*!*********************************************************!*\
  !*** ./src/app/ui/risk-evaluations/risk-strategies.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RiskStrategies; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/collapsible-data-table */ \"(app-pages-browser)/./src/components/collapsible-data-table.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction RiskStrategies() {\n    _s();\n    const [riskFactors, setRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [openStrategyDialog, setOpenStrategyDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStrategy, setCurrentStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        getRiskFactors({\n            variables: {\n                filter: {\n                    type: {\n                        ne: \"RiskFactor\"\n                    }\n                }\n            }\n        });\n    }, []);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            console.info(data.readRiskFactors.nodes);\n            setRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleUpdateStrategy = ()=>{\n        if (content && currentStrategy) {\n            updateMitigationStrategy({\n                variables: {\n                    input: {\n                        id: currentStrategy.id,\n                        strategy: content\n                    }\n                }\n            });\n        }\n        setOpenStrategyDialog(false);\n    };\n    const [updateMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateMitigationStrategy, {\n        onCompleted: ()=>{\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            ne: \"RiskFactor\"\n                        }\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const columns = (0,_components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_7__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"Risk strategy\",\n            cellAlignment: \"left\",\n            cell (param) {\n                let { row } = param;\n                const riskFactor = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        riskFactor.title || \"-\",\n                        \" (\",\n                        riskFactor.type === \"TowingChecklist\" && \"Towing Checklist\",\n                        riskFactor.type === \"DangerousGoods\" && \"Dangerous Goods\",\n                        riskFactor.type === \"BarCrossingChecklist\" && \"Bar Crossing Checklist\",\n                        \")\"\n                    ]\n                }, void 0, true);\n            }\n        },\n        {\n            accessorKey: \"impact\",\n            header: \"Impact\",\n            cellAlignment: \"left\"\n        },\n        {\n            accessorKey: \"probability\",\n            header: \"Probability\",\n            cellAlignment: \"left\",\n            cell (param) {\n                let { getValue } = param;\n                return \"\".concat(getValue(), \"/10\");\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.ListHeader, {\n                title: \"Risk Strategies\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                lineNumber: 139,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: riskFactors && riskFactors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_collapsible_data_table__WEBPACK_IMPORTED_MODULE_7__.CollapsibleDataTable, {\n                    data: riskFactors,\n                    columns: columns,\n                    showToolbar: false,\n                    collapsible: true,\n                    pageSize: 20,\n                    renderExpandedContent: (rowData)=>{\n                        const strategies = rowData.mitigationStrategy.nodes;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H3, {\n                                    className: \"text-xl\",\n                                    children: \"Mitigation Strategies\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 37\n                                }, void 0),\n                                strategies.length == 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"py-4\",\n                                    children: \"No strategies available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 41\n                                }, void 0),\n                                strategies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.TableBody, {\n                                        children: strategies.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                onClick: ()=>{\n                                                    setContent(item.strategy);\n                                                    setOpenStrategyDialog(true);\n                                                    setCurrentStrategy(item);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        dangerouslySetInnerHTML: {\n                                                            __html: item.strategy\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 61\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 57\n                                                }, void 0)\n                                            }, item.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 53\n                                            }, void 0))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 45\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 41\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 33\n                        }, void 0);\n                    },\n                    canExpand: (_)=>true,\n                    showPageSizeSelector: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                lineNumber: 140,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.AlertDialogNew, {\n                openDialog: openStrategyDialog,\n                setOpenDialog: setOpenStrategyDialog,\n                handleCreate: handleUpdateStrategy,\n                actionText: \"Update\",\n                title: \"Mitigation Strategy\",\n                size: \"xl\",\n                description: \"Edit the mitigation strategy content below\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    id: \"strategy\",\n                    placeholder: \"Mitigation strategy\",\n                    className: \"w-full\",\n                    content: content,\n                    handleEditorChange: handleEditorChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\risk-evaluations\\\\risk-strategies.tsx\",\n                lineNumber: 197,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(RiskStrategies, \"A9zMpFAiuz/UxBDuOJ415fxHSVg=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_8__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useMutation\n    ];\n});\n_c = RiskStrategies;\nvar _c;\n$RefreshReg$(_c, \"RiskStrategies\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/risk-evaluations/risk-strategies.tsx\n"));

/***/ })

});