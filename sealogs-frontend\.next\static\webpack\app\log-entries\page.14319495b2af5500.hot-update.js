"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/pvpd-risk-analysis.tsx":
/*!*********************************************************!*\
  !*** ./src/app/ui/logbook/forms/pvpd-risk-analysis.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PVPDRiskAnalysis; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_select_creatable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-select/creatable */ \"(app-pages-browser)/./node_modules/.pnpm/react-select@5.10.1_@types+_398a8a5109757eda919bd2626034f8bf/node_modules/react-select/creatable/dist/react-select-creatable.esm.js\");\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/riskFactor */ \"(app-pages-browser)/./src/app/offline/models/riskFactor.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/mitigationStrategy */ \"(app-pages-browser)/./src/app/offline/models/mitigationStrategy.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/risk-analysis */ \"(app-pages-browser)/./src/components/ui/risk-analysis/index.ts\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction PVPDRiskAnalysis(param) {\n    let { onSidebarClose, currentTrip, crewMembers = false, open, onOpenChange, editDGR = false, currentEvent, offline = false, setAllChecked, onRefreshEvent } = param;\n    var _riskAnalysis_riskFactors;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const logentryID = (_searchParams_get1 = searchParams.get(\"logentryID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const [riskAnalysis, setRiskAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [riskBuffer, setRiskBuffer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [checklistId, setChecklistId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"0\");\n    const checklistIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"0\");\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Helper function to check if riskBuffer is a valid object\n    const isRiskBufferValid = (buffer)=>{\n        return buffer !== false && typeof buffer === \"object\";\n    };\n    // Memoize model instances to prevent recreation on every render\n    const models = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            logBookEntry: new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_8__[\"default\"](),\n            dangerousGoodsChecklist: new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_9__[\"default\"](),\n            riskFactor: new _app_offline_models_riskFactor__WEBPACK_IMPORTED_MODULE_10__[\"default\"](),\n            crewMember: new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_11__[\"default\"](),\n            mitigationStrategy: new _app_offline_models_mitigationStrategy__WEBPACK_IMPORTED_MODULE_12__[\"default\"]()\n        }), []);\n    const [getSectionCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            const crewMembers = data.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMember.id\n                };\n            }).filter((member)=>member.value != logbook.master.id);\n            setMembers([\n                ...members,\n                ...crewMembers\n            ]);\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        var _logbook_master_firstName, _logbook_master_surname;\n        const master = {\n            label: \"\".concat((_logbook_master_firstName = logbook.master.firstName) !== null && _logbook_master_firstName !== void 0 ? _logbook_master_firstName : \"\", \" \").concat((_logbook_master_surname = logbook.master.surname) !== null && _logbook_master_surname !== void 0 ? _logbook_master_surname : \"\"),\n            value: logbook.master.id\n        };\n        setMembers([\n            master\n        ]);\n        const sections = logbook.logBookEntrySections.nodes.filter((node)=>node.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\");\n        if (sections) {\n            const sectionIDs = sections.map((section)=>section.id);\n            if ((sectionIDs === null || sectionIDs === void 0 ? void 0 : sectionIDs.length) > 0) {\n                if (offline) {\n                    const data = await models.crewMember.getByIds(sectionIDs);\n                    const crewMembers = data.map((member)=>{\n                        var _member_crewMember_firstName, _member_crewMember_surname;\n                        return {\n                            label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                            value: member.crewMember.id\n                        };\n                    }).filter((member)=>member.value != logbook.master.id);\n                    setMembers([\n                        ...members,\n                        ...crewMembers\n                    ]);\n                } else {\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: {\n                                id: {\n                                    in: sectionIDs\n                                }\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    if (+logentryID > 0 && !offline) {\n        (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_7__.getLogBookEntryByID)(+logentryID, handleSetLogbook);\n    }\n    const offlineUseEffect = async ()=>{\n        const data = await models.logBookEntry.getById(+logentryID);\n        handleSetLogbook(data);\n    };\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"Error updating trip report stop\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineUseEffect();\n        }\n    }, [\n        offline\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (crewMembers) {\n            setMembers(crewMembers);\n        }\n    }, [\n        crewMembers\n    ]);\n    const [updateDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateDangerousGoodsChecklist, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateDangerousGoodsChecklistNoRefresh] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateDangerousGoodsChecklist, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            // Update the local checklist ID state\n            setChecklistId(data.id);\n            checklistIdRef.current = data.id;\n            // Note: currentEvent is read-only, so we rely on the parent refresh callback\n            // Refresh the parent component's event data\n            if (onRefreshEvent) {\n                onRefreshEvent(currentEvent.id);\n            }\n            // Reload the risk analysis data\n            updateTripReport_Stop({\n                variables: {\n                    input: {\n                        id: currentEvent.id,\n                        dangerousGoodsChecklistID: data.id\n                    }\n                }\n            });\n            getRiskAnalysis({\n                variables: {\n                    id: data.id\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods checklist\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Error\", {\n                description: \"Failed to create dangerous goods checklist\"\n            });\n        }\n    });\n    const handleDgrFieldChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((field)=>async (check)=>{\n            if (!editDGR) {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Error\", {\n                    description: \"You do not have permission to edit this section\"\n                });\n                return;\n            }\n            setRiskBuffer((prev)=>{\n                const newBuffer = {\n                    ...prev || {},\n                    [field]: check ? \"on\" : \"off\"\n                };\n                return newBuffer;\n            });\n            const currentChecklistId = checklistIdRef.current;\n            if (+currentChecklistId > 0) {\n                if (offline) {\n                    await models.dangerousGoodsChecklist.save({\n                        id: currentChecklistId,\n                        [field]: check\n                    });\n                } else {\n                    updateDangerousGoodsChecklistNoRefresh({\n                        variables: {\n                            input: {\n                                id: currentChecklistId,\n                                [field]: check\n                            }\n                        }\n                    });\n                }\n            } else {\n                if (!offline) {\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: currentEvent.id,\n                                [field]: check\n                            }\n                        }\n                    });\n                }\n            }\n        }, [\n        editDGR,\n        sonner__WEBPACK_IMPORTED_MODULE_5__.toast,\n        currentEvent.id,\n        offline,\n        models.dangerousGoodsChecklist,\n        updateDangerousGoodsChecklistNoRefresh,\n        createDangerousGoodsChecklist\n    ]);\n    const fields = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                name: \"VesselSecuredToWharf\",\n                label: \"Vessel secured to wharf\",\n                value: \"vesselSecuredToWharf\",\n                checked: riskBuffer && typeof riskBuffer === \"object\" && riskBuffer.vesselSecuredToWharf ? riskBuffer.vesselSecuredToWharf === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.vesselSecuredToWharf,\n                handleChange: handleDgrFieldChange(\"vesselSecuredToWharf\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Conduct SAP prior to approaching the vessel.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"BravoFlagRaised\",\n                label: \"Bravo flag raised\",\n                value: \"bravoFlagRaised\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.bravoFlagRaised ? riskBuffer.bravoFlagRaised === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.bravoFlagRaised,\n                handleChange: handleDgrFieldChange(\"bravoFlagRaised\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Ascertain the nature of the problem, any damage, or taking on water.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Does a crew member need to go on board the other vessel to assist?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 368,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"TwoCrewLoadingVessel\",\n                label: \"Two crew loading vessel\",\n                value: \"twoCrewLoadingVessel\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.twoCrewLoadingVessel ? riskBuffer.twoCrewLoadingVessel === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.twoCrewLoadingVessel,\n                handleChange: handleDgrFieldChange(\"twoCrewLoadingVessel\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Check how many people are aboard, ensure everyone is accounted for.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Check for injuries or medical assistance required.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 391,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"FireHosesRiggedAndReady\",\n                label: \"Fire hoses rigged and ready\",\n                value: \"fireHosesRiggedAndReady\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.fireHosesRiggedAndReady ? riskBuffer.fireHosesRiggedAndReady === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.fireHosesRiggedAndReady,\n                handleChange: handleDgrFieldChange(\"fireHosesRiggedAndReady\")\n            },\n            {\n                name: \"NoSmokingSignagePosted\",\n                label: \"No smoking signage posted\",\n                value: \"noSmokingSignagePosted\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.noSmokingSignagePosted ? riskBuffer.noSmokingSignagePosted === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.noSmokingSignagePosted,\n                handleChange: handleDgrFieldChange(\"noSmokingSignagePosted\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Request that everyone wears a lifejacket.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"SpillKitAvailable\",\n                label: \"Spill kit available\",\n                value: \"spillKitAvailable\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.spillKitAvailable ? riskBuffer.spillKitAvailable === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.spillKitAvailable,\n                handleChange: handleDgrFieldChange(\"spillKitAvailable\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Ensure that communications have been established and checked prior to beginning the tow, i.e., VHF, hand signals, and/or light signals if the tow is to be conducted at night.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Ensure there is agreement on where to tow the vessel to.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 441,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"FireExtinguishersAvailable\",\n                label: \"Fire extinguishers available\",\n                value: \"fireExtinguishersAvailable\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.fireExtinguishersAvailable ? riskBuffer.fireExtinguishersAvailable === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.fireExtinguishersAvailable,\n                handleChange: handleDgrFieldChange(\"fireExtinguishersAvailable\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Towline securely attached\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Ensure everything on board is stowed and secure.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Confirm waterline length/cruising speed of the vessel (safe tow speed).\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Confirm attachment points for the towline.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 477,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Confirm that the towline is securely attached.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Ensure that no one on the other vessel is in close proximity to the towline before commencing the tow.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Turn on CRV towing lights and other vessel’s navigation lights.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Post towline lookout with responsibility for quick release of the tow / must carry or have a knife handy.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"DGDeclarationReceived\",\n                label: \"DG declaration received\",\n                value: \"dgDeclarationReceived\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.dgDeclarationReceived ? riskBuffer.dgDeclarationReceived === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.dgDeclarationReceived,\n                handleChange: handleDgrFieldChange(\"dgDeclarationReceived\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Conduct SAP prior to approaching the vessel.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"LoadPlanReceived\",\n                label: \"Load plan received\",\n                value: \"loadPlanReceived\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.loadPlanReceived ? riskBuffer.loadPlanReceived === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.loadPlanReceived,\n                handleChange: handleDgrFieldChange(\"loadPlanReceived\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Conduct SAP prior to approaching the vessel.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"MSDSAvailable\",\n                label: \"MSDS available for all dangerous goods carried\",\n                value: \"msdsAvailable\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.msdsAvailable ? riskBuffer.msdsAvailable === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.msdsAvailable,\n                handleChange: handleDgrFieldChange(\"msdsAvailable\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Conduct SAP prior to approaching the vessel.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 548,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 546,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"AnyVehiclesSecureToVehicleDeck\",\n                label: \"Any vehicles secure to vehicle deck\",\n                value: \"anyVehiclesSecureToVehicleDeck\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.anyVehiclesSecureToVehicleDeck ? riskBuffer.anyVehiclesSecureToVehicleDeck === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.anyVehiclesSecureToVehicleDeck,\n                handleChange: handleDgrFieldChange(\"anyVehiclesSecureToVehicleDeck\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Conduct SAP prior to approaching the vessel.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"SafetyAnnouncement\",\n                label: \"Safety announcement includes reference to dangerous goods & no smoking\",\n                value: \"safetyAnnouncement\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.safetyAnnouncement ? riskBuffer.safetyAnnouncement === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.safetyAnnouncement,\n                handleChange: handleDgrFieldChange(\"safetyAnnouncement\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Conduct SAP prior to approaching the vessel.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 590,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 21\n                }, this)\n            },\n            {\n                name: \"VehicleStationaryAndSecure\",\n                label: \"Vehicle stationary and secure prior to vehicle departing vessel\",\n                value: \"vehicleStationaryAndSecure\",\n                checked: isRiskBufferValid(riskBuffer) && riskBuffer.vehicleStationaryAndSecure ? riskBuffer.vehicleStationaryAndSecure === \"on\" : riskAnalysis === null || riskAnalysis === void 0 ? void 0 : riskAnalysis.vehicleStationaryAndSecure,\n                handleChange: handleDgrFieldChange(\"vehicleStationaryAndSecure\"),\n                description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Conduct SAP prior to approaching the vessel.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: \"Check for fittings on the vessel that could damage the CRV when coming alongside.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 612,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 21\n                }, this)\n            }\n        ], [\n        riskBuffer,\n        riskAnalysis\n    ]);\n    // Memoize selected member to prevent recreation on every render\n    const selectedMember = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(members === null || members === void 0 ? void 0 : members.find((member)=>member.value == (isRiskBufferValid(riskBuffer) ? riskBuffer.memberID : null))) || null, [\n        members,\n        riskBuffer\n    ]);\n    // Update the parent's \"all checked\" state when fields change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setAllChecked(fields.every((field)=>field.checked));\n    }, [\n        fields,\n        setAllChecked\n    ]);\n    // Offline and online risk factors & analysis\n    const offlineMount = async ()=>{\n        const data = await models.riskFactor.getByFieldID(\"type\", \"DangerousGoods\");\n        const risks = Array.from(new Set(data.map((risk)=>risk.title))).map((risk)=>({\n                label: risk,\n                value: risk\n            }));\n        setAllRisks(risks);\n        setAllRiskFactors(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (offline) {\n            offlineMount();\n        } else {\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"DangerousGoods\"\n                        }\n                    }\n                }\n            });\n        }\n    }, []);\n    const offlineOpenRiskAnalysis = async ()=>{\n        var _data_member;\n        const data = await models.dangerousGoodsChecklist.getById(currentEvent.dangerousGoodsChecklist.id);\n        setRiskAnalysis(data);\n        const newRiskBuffer = {\n            vesselSecuredToWharf: (data === null || data === void 0 ? void 0 : data.vesselSecuredToWharf) ? \"on\" : \"off\",\n            bravoFlagRaised: (data === null || data === void 0 ? void 0 : data.bravoFlagRaised) ? \"on\" : \"off\",\n            twoCrewLoadingVessel: (data === null || data === void 0 ? void 0 : data.twoCrewLoadingVessel) ? \"on\" : \"off\",\n            fireHosesRiggedAndReady: (data === null || data === void 0 ? void 0 : data.fireHosesRiggedAndReady) ? \"on\" : \"off\",\n            noSmokingSignagePosted: (data === null || data === void 0 ? void 0 : data.noSmokingSignagePosted) ? \"on\" : \"off\",\n            spillKitAvailable: (data === null || data === void 0 ? void 0 : data.spillKitAvailable) ? \"on\" : \"off\",\n            fireExtinguishersAvailable: (data === null || data === void 0 ? void 0 : data.fireExtinguishersAvailable) ? \"on\" : \"off\",\n            dgDeclarationReceived: (data === null || data === void 0 ? void 0 : data.dgDeclarationReceived) ? \"on\" : \"off\",\n            loadPlanReceived: (data === null || data === void 0 ? void 0 : data.loadPlanReceived) ? \"on\" : \"off\",\n            msdsAvailable: (data === null || data === void 0 ? void 0 : data.msdsAvailable) ? \"on\" : \"off\",\n            anyVehiclesSecureToVehicleDeck: (data === null || data === void 0 ? void 0 : data.anyVehiclesSecureToVehicleDeck) ? \"on\" : \"off\",\n            safetyAnnouncement: (data === null || data === void 0 ? void 0 : data.safetyAnnouncement) ? \"on\" : \"off\",\n            vehicleStationaryAndSecure: (data === null || data === void 0 ? void 0 : data.vehicleStationaryAndSecure) ? \"on\" : \"off\",\n            memberID: (data === null || data === void 0 ? void 0 : (_data_member = data.member) === null || _data_member === void 0 ? void 0 : _data_member.id) || null\n        };\n        setRiskBuffer(newRiskBuffer);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (open) {\n            const currentChecklistId = currentEvent.dangerousGoodsChecklist.id;\n            // Initialize local checklist ID state\n            setChecklistId(currentChecklistId);\n            checklistIdRef.current = currentChecklistId;\n            if (offline) {\n                offlineOpenRiskAnalysis();\n            } else {\n                // Check if dangerous goods checklist exists\n                // Use the current checklist ID or the previous one from ref if available\n                const validChecklistId = +currentChecklistId > 0 ? currentChecklistId : +checklistIdRef.current > 0 ? checklistIdRef.current : null;\n                if (validChecklistId) {\n                    // Update state to use the valid ID\n                    if (validChecklistId !== currentChecklistId) {\n                        setChecklistId(validChecklistId);\n                        checklistIdRef.current = validChecklistId;\n                    }\n                    getRiskAnalysis({\n                        variables: {\n                            id: validChecklistId\n                        }\n                    });\n                } else {\n                    // Create a new dangerous goods checklist only if we don't have one\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: currentEvent.id\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    }, [\n        open,\n        currentEvent.dangerousGoodsChecklist.id,\n        currentEvent.id\n    ]);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readRiskFactors_nodes;\n            const risks = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title))).map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [getRiskAnalysis] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GetOneDangerousGoodsChecklist, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _data_readOneDangerousGoodsChecklist, _data_readOneDangerousGoodsChecklist1, _data_readOneDangerousGoodsChecklist2, _data_readOneDangerousGoodsChecklist3, _data_readOneDangerousGoodsChecklist4, _data_readOneDangerousGoodsChecklist5, _data_readOneDangerousGoodsChecklist6, _data_readOneDangerousGoodsChecklist7, _data_readOneDangerousGoodsChecklist8, _data_readOneDangerousGoodsChecklist9, _data_readOneDangerousGoodsChecklist10, _data_readOneDangerousGoodsChecklist11, _data_readOneDangerousGoodsChecklist12, _data_readOneDangerousGoodsChecklist_member, _data_readOneDangerousGoodsChecklist13;\n            setRiskAnalysis(data.readOneDangerousGoodsChecklist);\n            const newRiskBuffer = {\n                vesselSecuredToWharf: ((_data_readOneDangerousGoodsChecklist = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist.vesselSecuredToWharf) ? \"on\" : \"off\",\n                bravoFlagRaised: ((_data_readOneDangerousGoodsChecklist1 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist1 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist1.bravoFlagRaised) ? \"on\" : \"off\",\n                twoCrewLoadingVessel: ((_data_readOneDangerousGoodsChecklist2 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist2 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist2.twoCrewLoadingVessel) ? \"on\" : \"off\",\n                fireHosesRiggedAndReady: ((_data_readOneDangerousGoodsChecklist3 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist3 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist3.fireHosesRiggedAndReady) ? \"on\" : \"off\",\n                noSmokingSignagePosted: ((_data_readOneDangerousGoodsChecklist4 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist4 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist4.noSmokingSignagePosted) ? \"on\" : \"off\",\n                spillKitAvailable: ((_data_readOneDangerousGoodsChecklist5 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist5 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist5.spillKitAvailable) ? \"on\" : \"off\",\n                fireExtinguishersAvailable: ((_data_readOneDangerousGoodsChecklist6 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist6 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist6.fireExtinguishersAvailable) ? \"on\" : \"off\",\n                dgDeclarationReceived: ((_data_readOneDangerousGoodsChecklist7 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist7 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist7.dgDeclarationReceived) ? \"on\" : \"off\",\n                loadPlanReceived: ((_data_readOneDangerousGoodsChecklist8 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist8 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist8.loadPlanReceived) ? \"on\" : \"off\",\n                msdsAvailable: ((_data_readOneDangerousGoodsChecklist9 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist9 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist9.msdsAvailable) ? \"on\" : \"off\",\n                anyVehiclesSecureToVehicleDeck: ((_data_readOneDangerousGoodsChecklist10 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist10 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist10.anyVehiclesSecureToVehicleDeck) ? \"on\" : \"off\",\n                safetyAnnouncement: ((_data_readOneDangerousGoodsChecklist11 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist11 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist11.safetyAnnouncement) ? \"on\" : \"off\",\n                vehicleStationaryAndSecure: ((_data_readOneDangerousGoodsChecklist12 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist12 === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist12.vehicleStationaryAndSecure) ? \"on\" : \"off\",\n                memberID: ((_data_readOneDangerousGoodsChecklist13 = data.readOneDangerousGoodsChecklist) === null || _data_readOneDangerousGoodsChecklist13 === void 0 ? void 0 : (_data_readOneDangerousGoodsChecklist_member = _data_readOneDangerousGoodsChecklist13.member) === null || _data_readOneDangerousGoodsChecklist_member === void 0 ? void 0 : _data_readOneDangerousGoodsChecklist_member.id) || null\n            };\n            setRiskBuffer(newRiskBuffer);\n        },\n        onError: (error)=>{\n            console.error(\"[PVPD] Error loading risk analysis data:\", error);\n        }\n    });\n    const updateRiskAnalysisMember = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (memberID)=>{\n        if (!editDGR) {\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Error\", {\n                description: \"You do not have permission to edit this section\"\n            });\n            return;\n        }\n        setRiskBuffer((prev)=>{\n            const newBuffer = {\n                ...prev || {},\n                memberID: memberID\n            };\n            return newBuffer;\n        });\n        const currentChecklistId = checklistIdRef.current;\n        if (+currentChecklistId > 0) {\n            if (offline) {\n                await models.dangerousGoodsChecklist.save({\n                    id: currentChecklistId,\n                    memberID: memberID\n                });\n            } else {\n                updateDangerousGoodsChecklist({\n                    variables: {\n                        input: {\n                            id: currentChecklistId,\n                            memberID: memberID\n                        }\n                    }\n                });\n            }\n        } else {\n            if (!offline) {\n                createDangerousGoodsChecklist({\n                    variables: {\n                        input: {\n                            tripReport_StopID: currentEvent.id,\n                            memberID: memberID\n                        }\n                    }\n                });\n            }\n        }\n    }, [\n        editDGR,\n        sonner__WEBPACK_IMPORTED_MODULE_5__.toast,\n        currentEvent.id,\n        offline,\n        models.dangerousGoodsChecklist,\n        updateDangerousGoodsChecklist,\n        createDangerousGoodsChecklist\n    ]);\n    const handleEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((newContent)=>{\n        setContent(newContent);\n    }, []);\n    const handleImpactChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((value)=>{\n        setCurrentRisk((prev)=>({\n                ...prev,\n                impact: value === null || value === void 0 ? void 0 : value.value\n            }));\n    }, []);\n    const riskImpacts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>[\n            {\n                value: \"Low\",\n                label: \"Low impact\"\n            },\n            {\n                value: \"Medium\",\n                label: \"Medium impact\"\n            },\n            {\n                value: \"High\",\n                label: \"High impact\"\n            },\n            {\n                value: \"Severe\",\n                label: \"Severe impact\"\n            }\n        ], []);\n    const handleSaveRisk = async ()=>{\n        var _currentRisk_mitigationStrategy;\n        if (currentRisk.id > 0) {\n            if (offline) {\n                await models.riskFactor.save({\n                    id: currentRisk.id,\n                    type: \"DangerousGoods\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    dangerousGoodsChecklistID: checklistIdRef.current\n                });\n                setOpenRiskDialog(false);\n            } else {\n                updateRiskFactor({\n                    variables: {\n                        input: {\n                            id: currentRisk.id,\n                            type: \"DangerousGoods\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            dangerousGoodsChecklistID: checklistIdRef.current\n                        }\n                    }\n                });\n            }\n            setAllRiskFactors(allRiskFactors.map((risk)=>{\n                if (risk.id === currentRisk.id) {\n                    return {\n                        ...risk,\n                        title: currentRisk.title,\n                        impact: currentRisk.impact,\n                        probability: currentRisk.probability,\n                        mitigationStrategy: {\n                            nodes: currentStrategies\n                        }\n                    };\n                }\n                return risk;\n            }));\n            if (!allRisks.find((r)=>r.value === currentRisk.title)) {\n                setAllRisks([\n                    ...allRisks,\n                    {\n                        value: currentRisk.title,\n                        label: currentRisk.title\n                    }\n                ]);\n            }\n            setRiskAnalysis({\n                ...riskAnalysis,\n                riskFactors: {\n                    nodes: riskAnalysis.riskFactors.nodes.map((risk)=>{\n                        if (risk.id === currentRisk.id) {\n                            return {\n                                ...risk,\n                                title: currentRisk.title,\n                                impact: currentRisk.impact,\n                                probability: currentRisk.probability,\n                                mitigationStrategy: {\n                                    nodes: currentStrategies\n                                }\n                            };\n                        }\n                        return risk;\n                    })\n                }\n            });\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>r.title === currentRisk.title && r.mitigationStrategy.nodes.length > 0).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            if (offline) {\n                const data = await models.riskFactor.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                    type: \"DangerousGoods\",\n                    title: currentRisk.title,\n                    impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                    probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                    mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                    dangerousGoodsChecklistID: checklistIdRef.current,\n                    vesselID: vesselID\n                });\n                setOpenRiskDialog(false);\n                setAllRiskFactors([\n                    ...allRiskFactors,\n                    {\n                        id: data.id,\n                        title: currentRisk.title,\n                        impact: currentRisk.impact,\n                        probability: currentRisk.probability,\n                        mitigationStrategy: {\n                            nodes: currentStrategies\n                        }\n                    }\n                ]);\n                if (!allRisks.find((r)=>r.value === currentRisk.title)) {\n                    setAllRisks([\n                        ...allRisks,\n                        {\n                            value: currentRisk.title,\n                            label: currentRisk.title\n                        }\n                    ]);\n                }\n                setRiskAnalysis({\n                    ...riskAnalysis,\n                    riskFactors: {\n                        nodes: [\n                            ...riskAnalysis.riskFactors.nodes,\n                            {\n                                id: data.id,\n                                title: currentRisk.title,\n                                impact: currentRisk.impact,\n                                probability: currentRisk.probability,\n                                mitigationStrategy: {\n                                    nodes: currentStrategies\n                                }\n                            }\n                        ]\n                    }\n                });\n                setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>r.title === currentRisk.title && r.mitigationStrategy.nodes.length > 0).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                        id: s.id,\n                        strategy: s.strategy\n                    })))));\n            } else {\n                createRiskFactor({\n                    variables: {\n                        input: {\n                            type: \"DangerousGoods\",\n                            title: currentRisk.title,\n                            impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                            probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                            mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                            dangerousGoodsChecklistID: checklistIdRef.current,\n                            vesselID: vesselID\n                        }\n                    }\n                });\n            }\n        }\n        if ((currentRisk === null || currentRisk === void 0 ? void 0 : (_currentRisk_mitigationStrategy = currentRisk.mitigationStrategy) === null || _currentRisk_mitigationStrategy === void 0 ? void 0 : _currentRisk_mitigationStrategy.id) > 0) {\n            if (offline) {\n                await models.mitigationStrategy.save({\n                    id: currentRisk.mitigationStrategy.id,\n                    strategy: content\n                });\n            } else {\n                updateMitigationStrategy({\n                    variables: {\n                        input: {\n                            id: currentRisk.mitigationStrategy.id,\n                            strategy: content\n                        }\n                    }\n                });\n            }\n        } else {\n            if (content) {\n                if (offline) {\n                    const data = await models.mitigationStrategy.save({\n                        id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                        strategy: content\n                    });\n                    setCurrentStrategies([\n                        ...currentStrategies,\n                        {\n                            id: data.id,\n                            strategy: content\n                        }\n                    ]);\n                    setContent(\"\");\n                    setCurrentRisk({\n                        ...currentRisk,\n                        mitigationStrategy: data.id\n                    });\n                } else {\n                    createMitigationStrategy({\n                        variables: {\n                            input: {\n                                strategy: content\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n            setCurrentRisk({\n                ...currentRisk,\n                mitigationStrategy: data.createMitigationStrategy.id\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateMitigationStrategy, {\n        onCompleted: ()=>{},\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateRiskFactor, {\n        onCompleted: (data)=>{\n            var _allRiskFactors_filter_map_, _allRiskFactors_filter;\n            setOpenRiskDialog(false);\n            setAllRiskFactors([\n                ...allRiskFactors,\n                {\n                    id: data.createRiskFactor.id,\n                    title: currentRisk.title,\n                    impact: currentRisk.impact,\n                    probability: currentRisk.probability,\n                    mitigationStrategy: {\n                        nodes: currentStrategies\n                    }\n                }\n            ]);\n            if (!allRisks.find((r)=>r.value === currentRisk.title)) {\n                setAllRisks([\n                    ...allRisks,\n                    {\n                        value: currentRisk.title,\n                        label: currentRisk.title\n                    }\n                ]);\n            }\n            setRiskAnalysis({\n                ...riskAnalysis,\n                riskFactors: {\n                    nodes: [\n                        ...riskAnalysis.riskFactors.nodes,\n                        {\n                            id: data.createRiskFactor.id,\n                            title: currentRisk.title,\n                            impact: currentRisk.impact,\n                            probability: currentRisk.probability,\n                            mitigationStrategy: {\n                                nodes: currentStrategies\n                            }\n                        }\n                    ]\n                }\n            });\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : (_allRiskFactors_filter = allRiskFactors.filter((r)=>r.title === currentRisk.title && r.mitigationStrategy.nodes.length > 0)) === null || _allRiskFactors_filter === void 0 ? void 0 : (_allRiskFactors_filter_map_ = _allRiskFactors_filter.map((r)=>r.mitigationStrategy.nodes)[0]) === null || _allRiskFactors_filter_map_ === void 0 ? void 0 : _allRiskFactors_filter_map_.map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            setOpenRiskDialog(false);\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleRiskValue = (v)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: v === null || v === void 0 ? void 0 : v.value\n        });\n        setRiskValue({\n            value: v.value,\n            label: v.value\n        });\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>risk.title === v.value && risk.mitigationStrategy.nodes.length > 0).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>r.title === v.value && r.mitigationStrategy.nodes.length > 0).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    const handleCreateRisk = (inputValue)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: inputValue\n        });\n        setRiskValue({\n            value: inputValue,\n            label: inputValue\n        });\n        if (allRisks) {\n            const risk = [\n                ...allRisks,\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ];\n            setAllRisks(risk);\n        } else {\n            setAllRisks([\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ]);\n        }\n    };\n    const handleDeleteRisk = async ()=>{\n        if (offline) {\n            await models.riskFactor.save({\n                id: riskToDelete.id,\n                vesselID: 0,\n                dangerousGoodsChecklistID: 0\n            });\n            setOpenRiskDialog(false);\n        } else {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: riskToDelete.id,\n                        vesselID: 0,\n                        dangerousGoodsChecklistID: 0\n                    }\n                }\n            });\n        }\n        setRiskAnalysis({\n            ...riskAnalysis,\n            riskFactors: {\n                nodes: riskAnalysis.riskFactors.nodes.filter((risk)=>risk.id !== riskToDelete.id)\n            }\n        });\n        setOpenDeleteConfirmation(false);\n    };\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.length > 0) {\n            if (currentStrategies.find((s)=>s.id === strategy.id)) {\n                setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n            } else {\n                setCurrentStrategies([\n                    ...currentStrategies,\n                    strategy\n                ]);\n            }\n        } else {\n            setCurrentStrategies([\n                strategy\n            ]);\n        }\n    };\n    const handleNewStratagy = async ()=>{\n        if (content) {\n            if (offline) {\n                const data = await models.mitigationStrategy.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                    strategy: content\n                });\n                setCurrentStrategies([\n                    ...currentStrategies,\n                    {\n                        id: data.id,\n                        strategy: content\n                    }\n                ]);\n                setContent(\"\");\n                setCurrentRisk({\n                    ...currentRisk,\n                    mitigationStrategy: data.id\n                });\n            } else {\n                createMitigationStrategy({\n                    variables: {\n                        input: {\n                            strategy: content\n                        }\n                    }\n                });\n            }\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>risk.title === v.title && risk.mitigationStrategy.nodes.length > 0).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>r.title === v.title && r.mitigationStrategy.nodes.length > 0).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_15__.RiskAnalysisSheet, {\n                open: open,\n                onOpenChange: (isOpen)=>{\n                    if (onOpenChange) {\n                        onOpenChange(isOpen);\n                    }\n                },\n                onSidebarClose: ()=>{\n                    if (onSidebarClose) {\n                        onSidebarClose();\n                    }\n                    if (onOpenChange) {\n                        onOpenChange(false);\n                    }\n                },\n                title: \"Risk Analysis\",\n                subtitle: \"Dangerous Goods\",\n                checkFields: fields,\n                riskFactors: (riskAnalysis === null || riskAnalysis === void 0 ? void 0 : (_riskAnalysis_riskFactors = riskAnalysis.riskFactors) === null || _riskAnalysis_riskFactors === void 0 ? void 0 : _riskAnalysis_riskFactors.nodes) || [],\n                crewMembers: members ? members.map((m)=>({\n                        ...m,\n                        value: String(m.value)\n                    })) : [],\n                selectedAuthor: selectedMember,\n                onAuthorChange: (value)=>{\n                    if (value) {\n                        updateRiskAnalysisMember(value.value);\n                    }\n                },\n                canEdit: editDGR,\n                canDeleteRisks: editDGR,\n                onRiskClick: (risk)=>{\n                    var _risk_mitigationStrategy;\n                    if (!editDGR) {\n                        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Error\", {\n                            description: \"You do not have permission to edit this section\"\n                        });\n                        return;\n                    }\n                    handleSetRiskValue(risk);\n                    setCurrentRisk(risk);\n                    setOpenRiskDialog(true);\n                    setCurrentStrategies((risk === null || risk === void 0 ? void 0 : (_risk_mitigationStrategy = risk.mitigationStrategy) === null || _risk_mitigationStrategy === void 0 ? void 0 : _risk_mitigationStrategy.nodes) || []);\n                },\n                onAddRiskClick: ()=>{\n                    if (!editDGR) {\n                        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Error\", {\n                            description: \"You do not have permission to edit this section\"\n                        });\n                        return;\n                    }\n                    setCurrentRisk({});\n                    setContent(\"\");\n                    setRiskValue(null);\n                    setOpenRiskDialog(true);\n                    setCurrentStrategies([]);\n                },\n                onRiskDelete: (risk)=>{\n                    if (!editDGR) {\n                        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Error\", {\n                            description: \"You do not have permission to edit this section\"\n                        });\n                        return;\n                    }\n                    setOpenDeleteConfirmation(true);\n                    setRiskToDelete(risk);\n                },\n                setAllChecked: setAllChecked,\n                selectedEvent: currentEvent,\n                currentTrip: currentTrip\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                lineNumber: 1406,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_16__.AlertDialogNew, {\n                openDialog: openRiskDialog,\n                setOpenDialog: setOpenRiskDialog,\n                handleCreate: handleSaveRisk,\n                actionText: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.id) > 0 ? \"Update\" : \"Create Risk\",\n                title: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.id) > 0 ? \"Update Risk\" : \"Create New Risk\",\n                variant: \"default\",\n                size: \"md\",\n                position: \"center\",\n                cancelText: \"Cancel\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-5\",\n                    children: [\n                        allRisks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Risk\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                    lineNumber: 1497,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_select_creatable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    id: \"risk\",\n                                    options: allRisks,\n                                    menuPlacement: \"top\",\n                                    placeholder: \"Risk\",\n                                    value: riskValue,\n                                    onCreateOption: handleCreateRisk,\n                                    onChange: handleRiskValue,\n                                    classNames: {\n                                        control: ()=>\"flex py-0.5 w-full bg-transparent rounded-lg border\",\n                                        singleValue: ()=>\"\",\n                                        menu: ()=>\"\",\n                                        option: ()=>\"\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                    lineNumber: 1498,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 1496,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Risk impact\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                    lineNumber: 1517,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_14__.Combobox, {\n                                    id: \"impact\",\n                                    options: riskImpacts,\n                                    placeholder: \"Risk impact\",\n                                    value: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? riskImpacts.find((impact)=>impact.value == (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact)) : null,\n                                    onChange: handleImpactChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                    lineNumber: 1520,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 1516,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Risk probability\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                    lineNumber: 1537,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"range\",\n                                            min: \"0\",\n                                            max: \"10\",\n                                            step: \"1\",\n                                            value: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                                            onChange: (e)=>{\n                                                setCurrentRisk((prev)=>({\n                                                        ...prev,\n                                                        probability: parseInt(e.target.value)\n                                                    }));\n                                            },\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                            lineNumber: 1541,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground mt-1\",\n                                            children: [\n                                                (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5,\n                                                \"/10\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                            lineNumber: 1555,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                    lineNumber: 1540,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 1536,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Mitigation strategy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                            lineNumber: 1562,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setOpenRecommendedstrategy(true),\n                                            className: \"text-sm text-primary hover:underline\",\n                                            children: \"Add strategy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                            lineNumber: 1565,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                    lineNumber: 1561,\n                                    columnNumber: 25\n                                }, this),\n                                currentStrategies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 space-y-2\",\n                                    children: currentStrategies.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-muted/50 rounded-md\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                dangerouslySetInnerHTML: {\n                                                    __html: s.strategy\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                                lineNumber: 1578,\n                                                columnNumber: 41\n                                            }, this)\n                                        }, s.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                            lineNumber: 1575,\n                                            columnNumber: 37\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                    lineNumber: 1573,\n                                    columnNumber: 29\n                                }, this),\n                                content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-2 bg-muted/50 rounded-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: content\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                        lineNumber: 1589,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                                    lineNumber: 1588,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                            lineNumber: 1560,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                    lineNumber: 1494,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                lineNumber: 1484,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_risk_analysis__WEBPACK_IMPORTED_MODULE_15__.StrategyDialog, {\n                open: openRecommendedstrategy,\n                onOpenChange: setOpenRecommendedstrategy,\n                onSave: handleNewStratagy,\n                currentRisk: currentRisk,\n                recommendedStrategies: recommendedStratagies,\n                currentStrategies: currentStrategies,\n                onStrategySelect: handleSetCurrentStrategies,\n                content: content,\n                onEditorChange: handleEditorChange\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                lineNumber: 1599,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_16__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: handleDeleteRisk,\n                actionText: \"Delete\",\n                title: \"Delete Risk\",\n                description: \"Are you sure you want to delete this risk? This action cannot be undone.\",\n                variant: \"\",\n                size: \"md\",\n                position: \"center\",\n                showIcon: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\pvpd-risk-analysis.tsx\",\n                lineNumber: 1610,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(PVPDRiskAnalysis, \"ldcJTS2S5v008Nj0D7M+SFp2eAw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_17__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_18__.useMutation\n    ];\n});\n_c = PVPDRiskAnalysis;\nvar _c;\n$RefreshReg$(_c, \"PVPDRiskAnalysis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/pvpd-risk-analysis.tsx\n"));

/***/ })

});