"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx":
/*!***********************************************************!*\
  !*** ./src/app/ui/logbook/forms/vessel-rescue-fields.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VesselRescueFields; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _app_offline_models_eventType_VesselRescue__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/models/eventType_VesselRescue */ \"(app-pages-browser)/./src/app/offline/models/eventType_VesselRescue.js\");\n/* harmony import */ var _app_offline_models_cgEventMission__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/cgEventMission */ \"(app-pages-browser)/./src/app/offline/models/cgEventMission.js\");\n/* harmony import */ var _app_offline_models_missionTimeline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/missionTimeline */ \"(app-pages-browser)/./src/app/offline/models/missionTimeline.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/app/offline/models/crewMembers_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/crewMembers_LogBookEntrySection.js\");\n/* harmony import */ var _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/app/offline/models/logBookEntry */ \"(app-pages-browser)/./src/app/offline/models/logBookEntry.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _app_ui_maintenance_task_task__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/app/ui/maintenance/task/task */ \"(app-pages-browser)/./src/app/ui/maintenance/task/task.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction VesselRescueFields(param) {\n    let { geoLocations, selectedEvent = false, closeModal, handleSaveParent, currentRescueID, type, eventCurrentLocation, locationDescription, setLocationDescription, offline = false, locked = false } = param;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_23__.useSearchParams)();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"));\n    const [openCommentsDialog, setOpenCommentsDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [commentTime, setCommentTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [rescueData, setRescueData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [missionData, setMissionData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [commentData, setCommentData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [timeline, setTimeline] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [deleteCommentsDialog, setDeleteCommentsDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [allVesselCrews, setAllVesselCrews] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [vessel, setVessel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [masterID, setMasterID] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [crewMemberOptions, setCrewMemberOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [allMembers, setAllMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewMembers, setCrewMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [crewMembersList, setCrewMembersList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [logbook, setLogbook] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentMissionLocation, setCurrentMissionLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [showInputDetailsP, setShowInputDetailsPanel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_28__.useMediaQuery)(\"(min-width: 640px)\");\n    const memberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const vesselRescueModel = new _app_offline_models_eventType_VesselRescue__WEBPACK_IMPORTED_MODULE_9__[\"default\"]();\n    const cgEventMissionModel = new _app_offline_models_cgEventMission__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\n    const missionTimelineModel = new _app_offline_models_missionTimeline__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const cmlbsModel = new _app_offline_models_crewMembers_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_24__[\"default\"]();\n    const logbookModel = new _app_offline_models_logBookEntry__WEBPACK_IMPORTED_MODULE_25__[\"default\"]();\n    const handleTimeChange = (date)=>{\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    var _searchParams_get1;\n    const logentryID = (_searchParams_get1 = searchParams.get(\"logentryID\")) !== null && _searchParams_get1 !== void 0 ? _searchParams_get1 : 0;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setRescueData(false);\n        if (currentRescueID) {\n            getCurrentEvent(currentRescueID);\n        }\n    }, [\n        currentRescueID\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setCurrentLocation(eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.currentLocation);\n        handleLocationChange({\n            value: eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID\n        });\n    }, [\n        eventCurrentLocation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (rescueData) {\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationDescription\n                };\n            });\n        }\n    }, [\n        locationDescription\n    ]);\n    const getCurrentEvent = async (currentRescueID)=>{\n        if (currentRescueID > 0) {\n            if (offline) {\n                const event = await vesselRescueModel.getById(currentRescueID);\n                if (event) {\n                    var _event_mission, _event_mission1, _event_mission2, _event_mission_missionType, _event_mission3, _event_mission4, _event_mission_operationOutcome, _event_mission5, _event_mission_currentLocation, _event_mission6, _event_mission7, _event_mission_currentLocation1, _event_mission8, _event_mission_currentLocation2, _event_mission9, _event_missionTimeline, _event_mission_currentLocation3, _event_mission10, _event_mission_currentLocation4, _event_mission11;\n                    setRescueData({\n                        vesselName: (event === null || event === void 0 ? void 0 : event.vesselName) ? event === null || event === void 0 ? void 0 : event.vesselName : \"\",\n                        callSign: (event === null || event === void 0 ? void 0 : event.callSign) ? event === null || event === void 0 ? void 0 : event.callSign : \"\",\n                        pob: (event === null || event === void 0 ? void 0 : event.pob) ? event === null || event === void 0 ? void 0 : event.pob : \"\",\n                        latitude: (event === null || event === void 0 ? void 0 : event.latitude) ? event === null || event === void 0 ? void 0 : event.latitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.latitude,\n                        longitude: (event === null || event === void 0 ? void 0 : event.longitude) ? event === null || event === void 0 ? void 0 : event.longitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.longitude,\n                        locationDescription: (event === null || event === void 0 ? void 0 : event.locationDescription) ? event === null || event === void 0 ? void 0 : event.locationDescription : \"\",\n                        vesselLength: (event === null || event === void 0 ? void 0 : event.vesselLength) ? event === null || event === void 0 ? void 0 : event.vesselLength : \"\",\n                        vesselType: (event === null || event === void 0 ? void 0 : event.vesselType) ? event === null || event === void 0 ? void 0 : event.vesselType : \"\",\n                        makeAndModel: (event === null || event === void 0 ? void 0 : event.makeAndModel) ? event === null || event === void 0 ? void 0 : event.makeAndModel : \"\",\n                        color: (event === null || event === void 0 ? void 0 : event.color) ? event === null || event === void 0 ? void 0 : event.color : \"\",\n                        ownerName: (event === null || event === void 0 ? void 0 : event.ownerName) ? event === null || event === void 0 ? void 0 : event.ownerName : \"\",\n                        phone: (event === null || event === void 0 ? void 0 : event.phone) ? event === null || event === void 0 ? void 0 : event.phone : \"\",\n                        email: (event === null || event === void 0 ? void 0 : event.email) ? event === null || event === void 0 ? void 0 : event.email : \"\",\n                        address: (event === null || event === void 0 ? void 0 : event.address) ? event === null || event === void 0 ? void 0 : event.address : \"\",\n                        ownerOnBoard: (event === null || event === void 0 ? void 0 : event.ownerOnBoard) ? event === null || event === void 0 ? void 0 : event.ownerOnBoard : false,\n                        cgMembership: (event === null || event === void 0 ? void 0 : event.cgMembership) ? event === null || event === void 0 ? void 0 : event.cgMembership : \"\",\n                        locationID: (event === null || event === void 0 ? void 0 : event.vesselLocationID) ? event === null || event === void 0 ? void 0 : event.vesselLocationID : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID,\n                        missionID: (event === null || event === void 0 ? void 0 : (_event_mission = event.mission) === null || _event_mission === void 0 ? void 0 : _event_mission.id) ? event === null || event === void 0 ? void 0 : (_event_mission1 = event.mission) === null || _event_mission1 === void 0 ? void 0 : _event_mission1.id : \"\",\n                        operationType: (event === null || event === void 0 ? void 0 : event.operationType) ? operationType.filter((operation)=>event === null || event === void 0 ? void 0 : event.operationType.split(\",\").includes(operation.value)) : [],\n                        operationDescription: (event === null || event === void 0 ? void 0 : event.operationDescription) ? event === null || event === void 0 ? void 0 : event.operationDescription : \"\",\n                        vesselTypeDescription: (event === null || event === void 0 ? void 0 : event.vesselTypeDescription) ? event === null || event === void 0 ? void 0 : event.vesselTypeDescription : \"\"\n                    });\n                    setTime(event === null || event === void 0 ? void 0 : (_event_mission2 = event.mission) === null || _event_mission2 === void 0 ? void 0 : _event_mission2.completedAt);\n                    setMissionData({\n                        missionType: event === null || event === void 0 ? void 0 : (_event_mission3 = event.mission) === null || _event_mission3 === void 0 ? void 0 : (_event_mission_missionType = _event_mission3.missionType) === null || _event_mission_missionType === void 0 ? void 0 : _event_mission_missionType.replaceAll(\"_\", \" \"),\n                        description: event === null || event === void 0 ? void 0 : (_event_mission4 = event.mission) === null || _event_mission4 === void 0 ? void 0 : _event_mission4.description,\n                        operationOutcome: event === null || event === void 0 ? void 0 : (_event_mission5 = event.mission) === null || _event_mission5 === void 0 ? void 0 : (_event_mission_operationOutcome = _event_mission5.operationOutcome) === null || _event_mission_operationOutcome === void 0 ? void 0 : _event_mission_operationOutcome.replaceAll(\"_\", \" \"),\n                        currentLocationID: event === null || event === void 0 ? void 0 : (_event_mission6 = event.mission) === null || _event_mission6 === void 0 ? void 0 : (_event_mission_currentLocation = _event_mission6.currentLocation) === null || _event_mission_currentLocation === void 0 ? void 0 : _event_mission_currentLocation.id,\n                        operationDescription: event === null || event === void 0 ? void 0 : (_event_mission7 = event.mission) === null || _event_mission7 === void 0 ? void 0 : _event_mission7.operationDescription,\n                        lat: event === null || event === void 0 ? void 0 : (_event_mission8 = event.mission) === null || _event_mission8 === void 0 ? void 0 : (_event_mission_currentLocation1 = _event_mission8.currentLocation) === null || _event_mission_currentLocation1 === void 0 ? void 0 : _event_mission_currentLocation1.lat,\n                        long: event === null || event === void 0 ? void 0 : (_event_mission9 = event.mission) === null || _event_mission9 === void 0 ? void 0 : (_event_mission_currentLocation2 = _event_mission9.currentLocation) === null || _event_mission_currentLocation2 === void 0 ? void 0 : _event_mission_currentLocation2.long\n                    });\n                    setTimeline(event === null || event === void 0 ? void 0 : (_event_missionTimeline = event.missionTimeline) === null || _event_missionTimeline === void 0 ? void 0 : _event_missionTimeline.nodes);\n                    setCurrentLocation({\n                        latitude: event === null || event === void 0 ? void 0 : (_event_mission10 = event.mission) === null || _event_mission10 === void 0 ? void 0 : (_event_mission_currentLocation3 = _event_mission10.currentLocation) === null || _event_mission_currentLocation3 === void 0 ? void 0 : _event_mission_currentLocation3.lat,\n                        longitude: event === null || event === void 0 ? void 0 : (_event_mission11 = event.mission) === null || _event_mission11 === void 0 ? void 0 : (_event_mission_currentLocation4 = _event_mission11.currentLocation) === null || _event_mission_currentLocation4 === void 0 ? void 0 : _event_mission_currentLocation4.long\n                    });\n                    setCurrentMissionLocation({\n                        latitude: event === null || event === void 0 ? void 0 : event.lat,\n                        longitude: event === null || event === void 0 ? void 0 : event.long\n                    });\n                    setLocationDescription(event === null || event === void 0 ? void 0 : event.locationDescription);\n                }\n            } else {\n                getTripEvent({\n                    variables: {\n                        id: +currentRescueID\n                    }\n                });\n            }\n        }\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GetTripEvent_VesselRescue, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneEventType_VesselRescue;\n            if (event) {\n                var _event_mission, _event_mission1, _event_mission2, _event_mission_missionType, _event_mission3, _event_mission4, _event_mission_operationOutcome, _event_mission5, _event_mission_currentLocation, _event_mission6, _event_mission7, _event_mission_currentLocation1, _event_mission8, _event_mission_currentLocation2, _event_mission9, _event_missionTimeline, _event_mission_currentLocation3, _event_mission10, _event_mission_currentLocation4, _event_mission11;\n                setRescueData({\n                    vesselName: (event === null || event === void 0 ? void 0 : event.vesselName) ? event === null || event === void 0 ? void 0 : event.vesselName : \"\",\n                    callSign: (event === null || event === void 0 ? void 0 : event.callSign) ? event === null || event === void 0 ? void 0 : event.callSign : \"\",\n                    pob: (event === null || event === void 0 ? void 0 : event.pob) ? event === null || event === void 0 ? void 0 : event.pob : \"\",\n                    latitude: (event === null || event === void 0 ? void 0 : event.latitude) ? event === null || event === void 0 ? void 0 : event.latitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.latitude,\n                    longitude: (event === null || event === void 0 ? void 0 : event.longitude) ? event === null || event === void 0 ? void 0 : event.longitude : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.longitude,\n                    locationDescription: (event === null || event === void 0 ? void 0 : event.locationDescription) ? event === null || event === void 0 ? void 0 : event.locationDescription : \"\",\n                    vesselLength: (event === null || event === void 0 ? void 0 : event.vesselLength) ? event === null || event === void 0 ? void 0 : event.vesselLength : \"\",\n                    vesselType: (event === null || event === void 0 ? void 0 : event.vesselType) ? event === null || event === void 0 ? void 0 : event.vesselType : \"\",\n                    makeAndModel: (event === null || event === void 0 ? void 0 : event.makeAndModel) ? event === null || event === void 0 ? void 0 : event.makeAndModel : \"\",\n                    color: (event === null || event === void 0 ? void 0 : event.color) ? event === null || event === void 0 ? void 0 : event.color : \"\",\n                    ownerName: (event === null || event === void 0 ? void 0 : event.ownerName) ? event === null || event === void 0 ? void 0 : event.ownerName : \"\",\n                    phone: (event === null || event === void 0 ? void 0 : event.phone) ? event === null || event === void 0 ? void 0 : event.phone : \"\",\n                    email: (event === null || event === void 0 ? void 0 : event.email) ? event === null || event === void 0 ? void 0 : event.email : \"\",\n                    address: (event === null || event === void 0 ? void 0 : event.address) ? event === null || event === void 0 ? void 0 : event.address : \"\",\n                    ownerOnBoard: (event === null || event === void 0 ? void 0 : event.ownerOnBoard) ? event === null || event === void 0 ? void 0 : event.ownerOnBoard : false,\n                    cgMembership: (event === null || event === void 0 ? void 0 : event.cgMembership) ? event === null || event === void 0 ? void 0 : event.cgMembership : \"\",\n                    locationID: (event === null || event === void 0 ? void 0 : event.vesselLocationID) ? event === null || event === void 0 ? void 0 : event.vesselLocationID : eventCurrentLocation === null || eventCurrentLocation === void 0 ? void 0 : eventCurrentLocation.geoLocationID,\n                    missionID: (event === null || event === void 0 ? void 0 : (_event_mission = event.mission) === null || _event_mission === void 0 ? void 0 : _event_mission.id) ? event === null || event === void 0 ? void 0 : (_event_mission1 = event.mission) === null || _event_mission1 === void 0 ? void 0 : _event_mission1.id : \"\",\n                    operationType: (event === null || event === void 0 ? void 0 : event.operationType) ? operationType.filter((operation)=>event === null || event === void 0 ? void 0 : event.operationType.split(\",\").includes(operation.value)) : [],\n                    operationDescription: (event === null || event === void 0 ? void 0 : event.operationDescription) ? event === null || event === void 0 ? void 0 : event.operationDescription : \"\",\n                    vesselTypeDescription: (event === null || event === void 0 ? void 0 : event.vesselTypeDescription) ? event === null || event === void 0 ? void 0 : event.vesselTypeDescription : \"\"\n                });\n                setTime(event === null || event === void 0 ? void 0 : (_event_mission2 = event.mission) === null || _event_mission2 === void 0 ? void 0 : _event_mission2.completedAt);\n                setMissionData({\n                    missionType: event === null || event === void 0 ? void 0 : (_event_mission3 = event.mission) === null || _event_mission3 === void 0 ? void 0 : (_event_mission_missionType = _event_mission3.missionType) === null || _event_mission_missionType === void 0 ? void 0 : _event_mission_missionType.replaceAll(\"_\", \" \"),\n                    description: event === null || event === void 0 ? void 0 : (_event_mission4 = event.mission) === null || _event_mission4 === void 0 ? void 0 : _event_mission4.description,\n                    operationOutcome: event === null || event === void 0 ? void 0 : (_event_mission5 = event.mission) === null || _event_mission5 === void 0 ? void 0 : (_event_mission_operationOutcome = _event_mission5.operationOutcome) === null || _event_mission_operationOutcome === void 0 ? void 0 : _event_mission_operationOutcome.replaceAll(\"_\", \" \"),\n                    currentLocationID: event === null || event === void 0 ? void 0 : (_event_mission6 = event.mission) === null || _event_mission6 === void 0 ? void 0 : (_event_mission_currentLocation = _event_mission6.currentLocation) === null || _event_mission_currentLocation === void 0 ? void 0 : _event_mission_currentLocation.id,\n                    operationDescription: event === null || event === void 0 ? void 0 : (_event_mission7 = event.mission) === null || _event_mission7 === void 0 ? void 0 : _event_mission7.operationDescription,\n                    lat: event === null || event === void 0 ? void 0 : (_event_mission8 = event.mission) === null || _event_mission8 === void 0 ? void 0 : (_event_mission_currentLocation1 = _event_mission8.currentLocation) === null || _event_mission_currentLocation1 === void 0 ? void 0 : _event_mission_currentLocation1.lat,\n                    long: event === null || event === void 0 ? void 0 : (_event_mission9 = event.mission) === null || _event_mission9 === void 0 ? void 0 : (_event_mission_currentLocation2 = _event_mission9.currentLocation) === null || _event_mission_currentLocation2 === void 0 ? void 0 : _event_mission_currentLocation2.long\n                });\n                setTimeline(event === null || event === void 0 ? void 0 : (_event_missionTimeline = event.missionTimeline) === null || _event_missionTimeline === void 0 ? void 0 : _event_missionTimeline.nodes);\n                setCurrentLocation({\n                    latitude: event === null || event === void 0 ? void 0 : (_event_mission10 = event.mission) === null || _event_mission10 === void 0 ? void 0 : (_event_mission_currentLocation3 = _event_mission10.currentLocation) === null || _event_mission_currentLocation3 === void 0 ? void 0 : _event_mission_currentLocation3.lat,\n                    longitude: event === null || event === void 0 ? void 0 : (_event_mission11 = event.mission) === null || _event_mission11 === void 0 ? void 0 : (_event_mission_currentLocation4 = _event_mission11.currentLocation) === null || _event_mission_currentLocation4 === void 0 ? void 0 : _event_mission_currentLocation4.long\n                });\n                setCurrentMissionLocation({\n                    latitude: event === null || event === void 0 ? void 0 : event.lat,\n                    longitude: event === null || event === void 0 ? void 0 : event.long\n                });\n                setLocationDescription(event === null || event === void 0 ? void 0 : event.locationDescription);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const handleSetMemberList = (members)=>{\n        var _members_filter;\n        setMembers(members === null || members === void 0 ? void 0 : (_members_filter = members.filter((member)=>member.archived == false && member.firstName != \"\")) === null || _members_filter === void 0 ? void 0 : _members_filter.map((member)=>({\n                label: member.firstName + \" \" + member.surname,\n                value: member.id\n            })));\n    };\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getSeaLogsMembersList)(handleSetMemberList, offline);\n    const handleCommentTimeChange = (date)=>{\n        setCommentTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    // setCommentTime(date)\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (geoLocations) {\n            setLocations(geoLocations.map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                })));\n        }\n    }, [\n        geoLocations\n    ]);\n    const vesselTypes = [\n        {\n            label: \"Commercial\",\n            value: \"Commercial\"\n        },\n        {\n            label: \"Recreation\",\n            value: \"Recreation\"\n        },\n        // { label: 'Power', value: 'Power' },\n        {\n            label: \"Sail\",\n            value: \"Sail\"\n        },\n        {\n            label: \"Paddle crafts\",\n            value: \"Paddle crafts\"\n        },\n        {\n            label: \"PWC\",\n            value: \"PWC\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const missions = [\n        {\n            label: \"To locate\",\n            value: \"To locate\"\n        },\n        {\n            label: \"To assist\",\n            value: \"To assist\"\n        },\n        {\n            label: \"To save\",\n            value: \"To save\"\n        },\n        {\n            label: \"To rescue\",\n            value: \"To rescue\"\n        },\n        {\n            label: \"To remove\",\n            value: \"To remove\"\n        }\n    ];\n    const operationOutcomes = [\n        {\n            label: \"Assisted by others\",\n            value: \"Assisted by others\"\n        },\n        {\n            label: \"Assisted on scene\",\n            value: \"Assisted on scene\"\n        },\n        {\n            label: \"Medical treatment\",\n            value: \"Medical treatment\"\n        },\n        {\n            label: \"Safe and well\",\n            value: \"Safe and well\"\n        },\n        {\n            label: \"Not located\",\n            value: \"Not located\"\n        },\n        {\n            label: \"Not recoverable\",\n            value: \"Not recoverable\"\n        },\n        {\n            label: \"Fatality\",\n            value: \"Fatality\"\n        },\n        {\n            label: \"Stood down\",\n            value: \"Stood down\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const commentTypes = [\n        {\n            label: \"General\",\n            value: \"General\"\n        },\n        {\n            label: \"Underway\",\n            value: \"Underway\"\n        },\n        {\n            label: \"On Scene\",\n            value: \"On Scene\"\n        }\n    ];\n    const operationType = [\n        {\n            label: \"Mechanical / equipment failure\",\n            value: \"Mechanical / equipment failure\"\n        },\n        {\n            label: \"Vessel adrift\",\n            value: \"Vessel adrift\"\n        },\n        {\n            label: \"Vessel aground\",\n            value: \"Vessel aground\"\n        },\n        {\n            label: \"Capsize\",\n            value: \"Capsize\"\n        },\n        {\n            label: \"Vessel requiring tow\",\n            value: \"Vessel requiring tow\"\n        },\n        {\n            label: \"Flare sighting\",\n            value: \"Flare sighting\"\n        },\n        {\n            label: \"Vessel sinking\",\n            value: \"Vessel sinking\"\n        },\n        {\n            label: \"Collision\",\n            value: \"Collision\"\n        },\n        {\n            label: \"Vessel overdue\",\n            value: \"Vessel overdue\"\n        },\n        {\n            label: \"Other\",\n            value: \"Other\"\n        }\n    ];\n    const handleSaveComments = async ()=>{\n        if ((rescueData === null || rescueData === void 0 ? void 0 : rescueData.missionID) === undefined) {\n            sonner__WEBPACK_IMPORTED_MODULE_18__.toast.error(\"Please save the event first in order to create timeline!\");\n            setOpenCommentsDialog(false);\n            return;\n        }\n        const variables = {\n            input: {\n                commentType: (commentData === null || commentData === void 0 ? void 0 : commentData.commentType) ? commentData === null || commentData === void 0 ? void 0 : commentData.commentType : \"General\",\n                description: content ? content : \"\",\n                time: commentTime ? dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"DD/MM/YYYY\") + \" \" + commentTime : dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"DD/MM/YYYY HH:mm\"),\n                authorID: commentData === null || commentData === void 0 ? void 0 : commentData.authorID,\n                // missionID: rescueData?.missionID,\n                vesselRescueID: currentRescueID\n            }\n        };\n        if ((commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0) {\n            if (offline) {\n                await missionTimelineModel.save({\n                    id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                    ...variables.input\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_18__.toast.success(\"Mission timeline updated\");\n                setOpenCommentsDialog(false);\n                setDeleteCommentsDialog(false);\n                getCurrentEvent(currentRescueID);\n            } else {\n                updateMissionTimeline({\n                    variables: {\n                        input: {\n                            id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                await missionTimelineModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    ...variables.input\n                });\n                sonner__WEBPACK_IMPORTED_MODULE_18__.toast.success(\"Mission timeline created\");\n                setOpenCommentsDialog(false);\n                setDeleteCommentsDialog(false);\n                await getCurrentEvent(currentRescueID);\n            } else {\n                createMissionTimeline({\n                    variables: {\n                        input: {\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        }\n        setOpenCommentsDialog(false);\n    };\n    const [createMissionTimeline] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateMissionTimeline, {\n        onCompleted: (response)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_18__.toast.success(\"Mission timeline created\");\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating mission timeline\", error);\n        }\n    });\n    const [updateMissionTimeline] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateMissionTimeline, {\n        onCompleted: (response)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_18__.toast.success(\"Mission timeline updated\");\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating mission timeline\", error);\n        }\n    });\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleSave = async ()=>{\n        var _rescueData_latitude, _currentLocation_latitude, _rescueData_longitude, _currentLocation_longitude, _rescueData_operationType;\n        const variables = {\n            input: {\n                vesselName: rescueData.vesselName,\n                callSign: rescueData.callSign,\n                pob: +rescueData.pob,\n                latitude: rescueData.latitude > 0 ? (_rescueData_latitude = rescueData.latitude) === null || _rescueData_latitude === void 0 ? void 0 : _rescueData_latitude.toString() : (_currentLocation_latitude = currentLocation.latitude) === null || _currentLocation_latitude === void 0 ? void 0 : _currentLocation_latitude.toString(),\n                longitude: rescueData.longitude > 0 ? (_rescueData_longitude = rescueData.longitude) === null || _rescueData_longitude === void 0 ? void 0 : _rescueData_longitude.toString() : (_currentLocation_longitude = currentLocation.longitude) === null || _currentLocation_longitude === void 0 ? void 0 : _currentLocation_longitude.toString(),\n                locationDescription: rescueData.locationDescription,\n                vesselLength: +rescueData.vesselLength,\n                vesselType: rescueData.vesselType,\n                makeAndModel: rescueData.makeAndModel,\n                color: rescueData.color,\n                ownerName: rescueData.ownerName,\n                phone: rescueData.phone,\n                email: rescueData.email,\n                address: rescueData.address,\n                ownerOnBoard: rescueData.ownerOnBoard,\n                cgMembershipType: \"cgnz\",\n                cgMembership: rescueData.cgMembership,\n                missionID: rescueData.missionID,\n                vesselLocationID: rescueData.locationID > 0 ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                operationType: (_rescueData_operationType = rescueData.operationType) === null || _rescueData_operationType === void 0 ? void 0 : _rescueData_operationType.map((type)=>type.value).join(\",\"),\n                operationDescription: rescueData.operationDescription,\n                vesselTypeDescription: rescueData.vesselTypeDescription\n            }\n        };\n        if (currentRescueID > 0) {\n            if (offline) {\n                const data = await vesselRescueModel.save({\n                    id: +currentRescueID,\n                    ...variables.input\n                });\n                if (rescueData.missionID > 0) {\n                    var _currentMissionLocation_latitude, _currentMissionLocation_longitude;\n                    await cgEventMissionModel.save({\n                        id: rescueData.missionID,\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.locationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\",\n                        lat: (_currentMissionLocation_latitude = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude === void 0 ? void 0 : _currentMissionLocation_latitude.toString(),\n                        long: (_currentMissionLocation_longitude = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude === void 0 ? void 0 : _currentMissionLocation_longitude.toString()\n                    });\n                } else {\n                    var _currentMissionLocation_latitude1, _currentMissionLocation_longitude1;\n                    var _currentMissionLocation_latitude_toString, _currentMissionLocation_longitude_toString;\n                    await cgEventMissionModel.save({\n                        id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.currentLocationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\",\n                        lat: (_currentMissionLocation_latitude_toString = (_currentMissionLocation_latitude1 = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude1 === void 0 ? void 0 : _currentMissionLocation_latitude1.toString()) !== null && _currentMissionLocation_latitude_toString !== void 0 ? _currentMissionLocation_latitude_toString : null,\n                        long: (_currentMissionLocation_longitude_toString = (_currentMissionLocation_longitude1 = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude1 === void 0 ? void 0 : _currentMissionLocation_longitude1.toString()) !== null && _currentMissionLocation_longitude_toString !== void 0 ? _currentMissionLocation_longitude_toString : null\n                    });\n                }\n                handleSaveParent(+currentRescueID, 0);\n            } else {\n                updateEventType_VesselRescue({\n                    variables: {\n                        input: {\n                            id: +currentRescueID,\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                var _rescueData_latitude1, _currentLocation_latitude1, _rescueData_longitude1, _currentLocation_longitude1, _rescueData_operationType1;\n                const data = await vesselRescueModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    vesselName: rescueData.vesselName,\n                    callSign: rescueData.callSign,\n                    pob: +rescueData.pob,\n                    latitude: rescueData.latitude > 0 ? (_rescueData_latitude1 = rescueData.latitude) === null || _rescueData_latitude1 === void 0 ? void 0 : _rescueData_latitude1.toString() : (_currentLocation_latitude1 = currentLocation.latitude) === null || _currentLocation_latitude1 === void 0 ? void 0 : _currentLocation_latitude1.toString(),\n                    longitude: rescueData.longitude > 0 ? (_rescueData_longitude1 = rescueData.longitude) === null || _rescueData_longitude1 === void 0 ? void 0 : _rescueData_longitude1.toString() : (_currentLocation_longitude1 = currentLocation.longitude) === null || _currentLocation_longitude1 === void 0 ? void 0 : _currentLocation_longitude1.toString(),\n                    locationDescription: rescueData.locationDescription,\n                    vesselLength: +rescueData.vesselLength,\n                    vesselType: rescueData.vesselType,\n                    makeAndModel: rescueData.makeAndModel,\n                    color: rescueData.color,\n                    ownerName: rescueData.ownerName,\n                    phone: rescueData.phone,\n                    email: rescueData.email,\n                    address: rescueData.address,\n                    ownerOnBoard: rescueData.ownerOnBoard,\n                    cgMembershipType: \"cgnz\",\n                    cgMembership: rescueData.cgMembership,\n                    missionID: rescueData.missionID,\n                    vesselLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                    operationType: (_rescueData_operationType1 = rescueData.operationType) === null || _rescueData_operationType1 === void 0 ? void 0 : _rescueData_operationType1.map((type)=>type.value).join(\",\"),\n                    operationDescription: rescueData.operationDescription,\n                    vesselTypeDescription: rescueData.vesselTypeDescription\n                });\n                await cgEventMissionModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_12__.generateUniqueId)(),\n                    missionType: missionData.missionType,\n                    description: missionData.description,\n                    operationDescription: missionData.operationDescription,\n                    operationOutcome: missionData.operationOutcome,\n                    completedAt: time,\n                    currentLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                    eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                    eventType: \"VesselRescue\"\n                });\n                handleSaveParent(+(data === null || data === void 0 ? void 0 : data.id), 0);\n                closeModal();\n            } else {\n                var _rescueData_latitude2, _currentLocation_latitude2, _rescueData_longitude2, _currentLocation_longitude2, _rescueData_operationType2;\n                createEventType_VesselRescue({\n                    variables: {\n                        input: {\n                            vesselName: rescueData.vesselName,\n                            callSign: rescueData.callSign,\n                            pob: +rescueData.pob,\n                            latitude: rescueData.latitude > 0 ? (_rescueData_latitude2 = rescueData.latitude) === null || _rescueData_latitude2 === void 0 ? void 0 : _rescueData_latitude2.toString() : (_currentLocation_latitude2 = currentLocation.latitude) === null || _currentLocation_latitude2 === void 0 ? void 0 : _currentLocation_latitude2.toString(),\n                            longitude: rescueData.longitude > 0 ? (_rescueData_longitude2 = rescueData.longitude) === null || _rescueData_longitude2 === void 0 ? void 0 : _rescueData_longitude2.toString() : (_currentLocation_longitude2 = currentLocation.longitude) === null || _currentLocation_longitude2 === void 0 ? void 0 : _currentLocation_longitude2.toString(),\n                            locationDescription: rescueData.locationDescription,\n                            vesselLength: +rescueData.vesselLength,\n                            vesselType: rescueData.vesselType,\n                            makeAndModel: rescueData.makeAndModel,\n                            color: rescueData.color,\n                            ownerName: rescueData.ownerName,\n                            phone: rescueData.phone,\n                            email: rescueData.email,\n                            address: rescueData.address,\n                            ownerOnBoard: rescueData.ownerOnBoard,\n                            cgMembershipType: \"cgnz\",\n                            cgMembership: rescueData.cgMembership,\n                            missionID: rescueData.missionID,\n                            vesselLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                            operationType: (_rescueData_operationType2 = rescueData.operationType) === null || _rescueData_operationType2 === void 0 ? void 0 : _rescueData_operationType2.map((type)=>type.value).join(\",\"),\n                            operationDescription: rescueData.operationDescription,\n                            vesselTypeDescription: rescueData.vesselTypeDescription\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createEventType_VesselRescue] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateEventType_VesselRescue, {\n        onCompleted: (response)=>{\n            const data = response.createEventType_VesselRescue;\n            createCGEventMission({\n                variables: {\n                    input: {\n                        missionType: missionData.missionType,\n                        description: missionData.description,\n                        operationDescription: missionData.operationDescription,\n                        operationOutcome: missionData.operationOutcome,\n                        completedAt: time,\n                        currentLocationID: rescueData.locationID ? rescueData.locationID : eventCurrentLocation.geoLocationID,\n                        eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                        eventType: \"VesselRescue\"\n                    }\n                }\n            });\n            handleSaveParent(+(data === null || data === void 0 ? void 0 : data.id), 0);\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating vessel rescue\", error);\n        }\n    });\n    const [updateEventType_VesselRescue] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateEventType_VesselRescue, {\n        onCompleted: (response)=>{\n            const data = response.updateEventType_VesselRescue;\n            if (rescueData.missionID > 0) {\n                var _currentMissionLocation_latitude, _currentMissionLocation_longitude;\n                updateCGEventMission({\n                    variables: {\n                        input: {\n                            id: rescueData.missionID,\n                            missionType: missionData.missionType,\n                            description: missionData.description,\n                            operationDescription: missionData.operationDescription,\n                            operationOutcome: missionData.operationOutcome,\n                            completedAt: time,\n                            currentLocationID: rescueData.locationID,\n                            eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                            eventType: \"VesselRescue\",\n                            lat: (_currentMissionLocation_latitude = currentMissionLocation.latitude) === null || _currentMissionLocation_latitude === void 0 ? void 0 : _currentMissionLocation_latitude.toString(),\n                            long: (_currentMissionLocation_longitude = currentMissionLocation.longitude) === null || _currentMissionLocation_longitude === void 0 ? void 0 : _currentMissionLocation_longitude.toString()\n                        }\n                    }\n                });\n            } else {\n                createCGEventMission({\n                    variables: {\n                        input: {\n                            missionType: missionData.missionType,\n                            description: missionData.description,\n                            operationDescription: missionData.operationDescription,\n                            operationOutcome: missionData.operationOutcome,\n                            completedAt: time,\n                            currentLocationID: rescueData.currentLocationID,\n                            eventID: +(data === null || data === void 0 ? void 0 : data.id),\n                            eventType: \"VesselRescue\",\n                            lat: currentMissionLocation.latitude.toString(),\n                            long: currentMissionLocation.longitude.toString()\n                        }\n                    }\n                });\n            }\n            handleSaveParent(+currentRescueID, 0);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating vessel rescue\", error);\n        }\n    });\n    const [createCGEventMission] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateCGEventMission, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"Error creating CG Event Mission\", error);\n        }\n    });\n    const [updateCGEventMission] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateCGEventMission, {\n        onCompleted: (response)=>{},\n        onError: (error)=>{\n            console.error(\"Error updating CG Event Mission\", error);\n        }\n    });\n    const handleLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationID: +value.value,\n                    latitude: null,\n                    longitude: null\n                };\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setRescueData((prev)=>{\n                return {\n                    ...prev,\n                    locationID: 0,\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                };\n            });\n        }\n    };\n    const handleMissionLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setMissionData({\n                ...missionData,\n                currentLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setMissionData({\n                ...missionData,\n                currentLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n        }\n    };\n    const handleCreateComment = ()=>{\n        if (selectedEvent) {\n            setOpenCommentsDialog(true);\n            handleEditorChange(\"\");\n            setCommentData(false);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_18__.toast.error(\"Please save the event first in order to create timeline!\");\n        }\n    };\n    const handleEditComment = (comment)=>{\n        setOpenCommentsDialog(true);\n        setCommentData(comment);\n        handleEditorChange(comment.description);\n        setCommentTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(comment.time).format(\"HH:mm\"));\n    };\n    const handleDeleteComment = (commentId)=>{\n        const comment = timeline === null || timeline === void 0 ? void 0 : timeline.find((c)=>c.id === commentId);\n        if (comment) {\n            setDeleteCommentsDialog(true);\n            setCommentData(comment);\n        }\n    };\n    const handleDeleteComments = async ()=>{\n        if (offline) {\n            await missionTimelineModel.save({\n                id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                archived: true\n            });\n            setOpenCommentsDialog(false);\n            setDeleteCommentsDialog(false);\n            getCurrentEvent(currentRescueID);\n            setDeleteCommentsDialog(false);\n        } else {\n            updateMissionTimeline({\n                variables: {\n                    input: {\n                        id: commentData === null || commentData === void 0 ? void 0 : commentData.id,\n                        archived: true\n                    }\n                }\n            });\n            setDeleteCommentsDialog(false);\n        }\n    };\n    const offlineGetSeaLogsMembersList = async ()=>{\n        // getSeaLogsMembersList(handleSetMemberList)\n        const members = await memberModel.getAll();\n        handleSetMemberList(members);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (offline) {\n            offlineGetSeaLogsMembersList();\n        }\n    }, [\n        offline\n    ]);\n    (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.getVesselByID)(+vesselID, setVessel, offline);\n    const [getSectionCrewMembers_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.CrewMembers_LogBookEntrySection, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            let data = response.readCrewMembers_LogBookEntrySections.nodes;\n            setCrewMembers(data);\n        },\n        onError: (error)=>{\n            console.error(\"CrewMembers_LogBookEntrySection error\", error);\n        }\n    });\n    const handleSetLogbook = async (logbook)=>{\n        setLogbook(logbook);\n        const sectionTypes = Array.from(new Set(logbook.logBookEntrySections.nodes.map((sec)=>sec.className))).map((type)=>({\n                className: type,\n                ids: logbook.logBookEntrySections.nodes.filter((sec)=>sec.className === type).map((sec)=>sec.id)\n            }));\n        sectionTypes.forEach(async (section)=>{\n            if (section.className === \"SeaLogs\\\\CrewMembers_LogBookEntrySection\") {\n                if (offline) {\n                    const data = await cmlbsModel.getByIds(section.ids);\n                    setCrewMembers(data);\n                } else {\n                    const searchFilter = {};\n                    searchFilter.id = {\n                        in: section.ids\n                    };\n                    getSectionCrewMembers_LogBookEntrySection({\n                        variables: {\n                            filter: searchFilter\n                        }\n                    });\n                }\n            }\n        });\n    };\n    const getLogBookEntryByID = async (id)=>{\n        if (offline) {\n            const data = await logbookModel.getById(id);\n            if (data) {\n                handleSetLogbook(data);\n            }\n        } else {\n            queryLogBookEntry({\n                variables: {\n                    logbookEntryId: +id\n                }\n            });\n        }\n    };\n    const [queryLogBookEntry] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GET_LOGBOOK_ENTRY_BY_ID, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readOneLogBookEntry;\n            if (data) {\n                handleSetLogbook(data);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryLogBookEntry error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getLogBookEntryByID(+logentryID);\n    }, []);\n    const [queryVesselCrews] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.CREW_LIST, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers;\n            if (data) {\n                const allMembers = data.nodes.filter((item)=>{\n                    return +item.id !== +masterID;\n                }).map((member)=>{\n                    // const crewWithTraining = GetCrewListWithTrainingStatus(\n                    //     [member],\n                    //     [vessel],\n                    // )[0]\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        // data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setAllMembers(allMembers);\n                const members = allMembers.filter((member)=>{\n                    if (!crewMembers) {\n                        return true;\n                    }\n                    return !Array.isArray(crewMembers) || !crewMembers.some((section)=>section && section.crewMember && section.crewMember.id === member.value && section.punchOut === null);\n                });\n                const memberOptions = members.filter((member)=>!crewMembersList || !Array.isArray(crewMembersList) || !crewMembersList.includes(+member.value));\n                setCrewMemberOptions(memberOptions);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"queryVesselCrews error\", error);\n        }\n    });\n    const loadVesselCrews = async ()=>{\n        if (offline) {\n            const data = await seaLogsMemberModel.getByVesselId(vesselID);\n            setAllVesselCrews(data);\n            if (data) {\n                const members = data.filter((item)=>+item.id !== +logbook.master.id).map((member)=>{\n                    const crewWithTraining = (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_5__.GetCrewListWithTrainingStatus)([\n                        member\n                    ], [\n                        vessel\n                    ])[0];\n                    return {\n                        label: \"\".concat(member.firstName || \"\", \" \").concat(member.surname || \"\").trim(),\n                        value: member.id,\n                        data: crewWithTraining,\n                        profile: {\n                            firstName: member.firstName,\n                            surname: member.surname,\n                            avatar: member.profileImage\n                        }\n                    };\n                });\n                setCrewMemberOptions(members);\n            }\n        } else {\n            await queryVesselCrews({\n                variables: {\n                    filter: {\n                        vehicles: {\n                            id: {\n                                eq: vesselID\n                            }\n                        },\n                        isArchived: {\n                            eq: false\n                        }\n                    }\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            loadVesselCrews();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" pt-0\"),\n        children: [\n            type === \"TaskingComplete\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-6 pb-0 pt-0 px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"my-0  col-span-3 md:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H3, {\n                                    children: \"Mission complete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1112,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                    children: \"Record the operation outcome, location and time of completion\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1113,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1111,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-3 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                        options: operationOutcomes,\n                                        value: operationOutcomes === null || operationOutcomes === void 0 ? void 0 : operationOutcomes.find((outcome)=>outcome.value == (missionData === null || missionData === void 0 ? void 0 : missionData.operationOutcome)),\n                                        onChange: (value)=>{\n                                            setMissionData({\n                                                ...missionData,\n                                                operationOutcome: value === null || value === void 0 ? void 0 : value.value\n                                            });\n                                        },\n                                        placeholder: \"Operation outcome\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1120,\n                                        columnNumber: 33\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1119,\n                                    columnNumber: 29\n                                }, this),\n                                (missionData === null || missionData === void 0 ? void 0 : missionData.operationOutcome) == \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"my-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                        id: \"operation-outcome-description\",\n                                        rows: 4,\n                                        className: \"\",\n                                        placeholder: \"Description\",\n                                        value: (missionData === null || missionData === void 0 ? void 0 : missionData.operationDescription) ? missionData === null || missionData === void 0 ? void 0 : missionData.operationDescription : \"\",\n                                        onChange: ()=>{\n                                            setMissionData({\n                                                ...missionData,\n                                                operationDescription: document.getElementById(\"operation-outcome-description\").value\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1137,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1118,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1110,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 pb-4 pt-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2.5\",\n                        children: [\n                            timeline && (timeline === null || timeline === void 0 ? void 0 : timeline.map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_task_task__WEBPACK_IMPORTED_MODULE_27__.RecordCard, {\n                                    record: comment,\n                                    onEdit: handleEditComment,\n                                    onDelete: handleDeleteComment\n                                }, \"\".concat(comment.id, \"-record-\").concat(comment.time || \"\"), false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1170,\n                                    columnNumber: 33\n                                }, this))),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-row gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                        onClick: ()=>setShowInputDetailsPanel(true),\n                                        children: \"Vessel details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1178,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Tooltip, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.TooltipTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                                    onClick: handleCreateComment,\n                                                    disabled: !selectedEvent,\n                                                    children: \"Add notes/comments\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                    lineNumber: 1185,\n                                                    columnNumber: 37\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1184,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.TooltipContent, {\n                                                hidden: (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id) > 0,\n                                                children: \"Please save the event first in order to create timeline!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1183,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                lineNumber: 1177,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1167,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1166,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1165,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1201,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                        onClick: handleSave,\n                        children: \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                        lineNumber: 1207,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1200,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_17__.AlertDialogNew, {\n                openDialog: openCommentsDialog,\n                setOpenDialog: setOpenCommentsDialog,\n                size: \"xl\",\n                handleCreate: handleSaveComments,\n                title: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Update Comment\" : \"Create Comment\",\n                actionText: (commentData === null || commentData === void 0 ? void 0 : commentData.id) > 0 ? \"Update\" : \"Create Comment\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                            htmlFor: \"comment-type\",\n                            label: \"Comment Type\",\n                            className: \"text-sm font-medium\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                id: \"comment-type\",\n                                options: commentTypes,\n                                value: commentTypes === null || commentTypes === void 0 ? void 0 : commentTypes.find((type)=>{\n                                    var _commentData_commentType;\n                                    return type.value == (commentData === null || commentData === void 0 ? void 0 : (_commentData_commentType = commentData.commentType) === null || _commentData_commentType === void 0 ? void 0 : _commentData_commentType.replaceAll(\"_\", \" \"));\n                                }),\n                                onChange: (value)=>setCommentData({\n                                        ...commentData,\n                                        commentType: value === null || value === void 0 ? void 0 : value.value\n                                    }),\n                                placeholder: \"Select comment type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                lineNumber: 1223,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1219,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                    htmlFor: \"comment_time\",\n                                    className: \"flex items-center gap-2\",\n                                    children: \"Time of Completion\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1245,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    time: commentTime,\n                                    handleTimeChange: (date)=>{\n                                        handleCommentTimeChange(date);\n                                    },\n                                    timeID: \"comment_time\",\n                                    fieldName: \"comment_time\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1244,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                    htmlFor: \"comment\",\n                                    children: \"Comment Content\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1261,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    id: \"comment\",\n                                    placeholder: \"Write your comment here...\",\n                                    className: \"w-full min-h-[150px] bg-secondary-foreground\",\n                                    content: content,\n                                    handleEditorChange: handleEditorChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1262,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1260,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                    htmlFor: \"author\",\n                                    className: \"flex items-center gap-2\",\n                                    children: \"Author\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1272,\n                                    columnNumber: 25\n                                }, this),\n                                members && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                    id: \"author\",\n                                    options: crewMemberOptions,\n                                    value: crewMemberOptions === null || crewMemberOptions === void 0 ? void 0 : crewMemberOptions.find((member)=>{\n                                        var _commentData_author;\n                                        return member.value == (commentData === null || commentData === void 0 ? void 0 : (_commentData_author = commentData.author) === null || _commentData_author === void 0 ? void 0 : _commentData_author.id);\n                                    }),\n                                    onChange: (value)=>setCommentData({\n                                            ...commentData,\n                                            authorID: value === null || value === void 0 ? void 0 : value.value\n                                        }),\n                                    placeholder: \"Select crew\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1278,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1271,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1218,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1209,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_17__.AlertDialogNew, {\n                openDialog: deleteCommentsDialog,\n                setOpenDialog: setDeleteCommentsDialog,\n                handleCreate: handleDeleteComments,\n                title: \"Delete Comment\",\n                variant: \"danger\",\n                actionText: \"Confirm delete\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                    children: \"Are you sure you want to delete this comment? This action cannot be undone and all associated data will be permanently removed from the system.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1304,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1297,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.Sheet, {\n                open: showInputDetailsP,\n                onOpenChange: (open)=>setShowInputDetailsPanel(open),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetContent, {\n                    side: \"right\",\n                    className: \"w-full max-w-md sm:max-w-xl bg-background phablet:bg-muted\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_19__.SheetHeader, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1316,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col h-full min-h-[400px] overflow-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.ScrollArea, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-grow space-y-6 py-4 mx-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Target Vessel Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1324,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1323,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                                                children: \"Record vessel name, callsign and number of people on board\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1328,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1322,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"vessel-name\",\n                                                                        children: \"Vessel Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1335,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"vessel-name\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter vessel name\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselName) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselName: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1338,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1334,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"call-sign\",\n                                                                        children: \"Call Sign\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1356,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"call-sign\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter call sign\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.callSign) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                callSign: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1359,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1355,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"pob\",\n                                                                        children: \"People On Board (POB)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1377,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"pob\",\n                                                                        type: \"number\",\n                                                                        placeholder: \"Enter number of people\",\n                                                                        min: 1,\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.pob) || 0,\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                pob: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1380,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1333,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1321,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Vessel Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1401,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1400,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                                                children: \"Include details of vessel type, make and descriptors\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1405,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1399,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"vessel-length\",\n                                                                        children: \"Number of Vessels\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1412,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"vessel-length\",\n                                                                        type: \"number\",\n                                                                        placeholder: \"Enter vessel length\",\n                                                                        min: 1,\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselLength) || 0,\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselLength: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1415,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1411,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"vessel-type\",\n                                                                        children: \"Vessel type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1435,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_15__.Combobox, {\n                                                                        options: vesselTypes,\n                                                                        value: vesselTypes === null || vesselTypes === void 0 ? void 0 : vesselTypes.find((type)=>type.value == (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselType)),\n                                                                        onChange: (value)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselType: value === null || value === void 0 ? void 0 : value.value\n                                                                            });\n                                                                        },\n                                                                        placeholder: \"Select vessel type\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1438,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1434,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselType) == \"Other\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"vessel-type-description\",\n                                                                        children: \"Vessel type description\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1458,\n                                                                        columnNumber: 49\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                                                        id: \"vessel-type-description\",\n                                                                        rows: 3,\n                                                                        placeholder: \"Describe the vessel type\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.vesselTypeDescription) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                vesselTypeDescription: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1461,\n                                                                        columnNumber: 49\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1457,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"make\",\n                                                                        children: \"Make and odel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1481,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"make\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter make and model\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.makeAndModel) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                makeAndModel: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1484,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1480,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"color\",\n                                                                        children: \"Color\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1503,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                        id: \"color\",\n                                                                        type: \"text\",\n                                                                        placeholder: \"Enter vessel color\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.color) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                color: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1504,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1502,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1410,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1398,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardHeader, {\n                                                        className: \"pb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"my-4 text-sm font-semibold uppercase\",\n                                                                    children: \"Owner's Details\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1524,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1523,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.P, {\n                                                                children: \"Record vessel owner's details and membership number if applicable\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1528,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1522,\n                                                        columnNumber: 37\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_21__.CardContent, {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                                htmlFor: \"owner-name\",\n                                                                                children: \"Owner's Name\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1536,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                                id: \"owner-name\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter owner's name\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.ownerName) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        ownerName: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1539,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1535,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                                htmlFor: \"owner-phone\",\n                                                                                children: \"Phone Number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1558,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                                id: \"owner-phone\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter phone number\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.phone) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        phone: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1561,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1557,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1534,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                                htmlFor: \"cgnz\",\n                                                                                children: \"Coastguard NZ Membership\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1581,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                                id: \"cgnz\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter membership number\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.cgMembership) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        cgMembership: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1584,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1580,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                                htmlFor: \"owner-email\",\n                                                                                children: \"Email Address\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1603,\n                                                                                columnNumber: 49\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                                                                id: \"owner-email\",\n                                                                                type: \"text\",\n                                                                                placeholder: \"Enter email address\",\n                                                                                value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.email) || \"\",\n                                                                                onChange: (e)=>{\n                                                                                    setRescueData({\n                                                                                        ...rescueData,\n                                                                                        email: e.target.value\n                                                                                    });\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                                lineNumber: 1606,\n                                                                                columnNumber: 49\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1602,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1579,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                        htmlFor: \"owner-address\",\n                                                                        children: \"Owner's Address\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1625,\n                                                                        columnNumber: 45\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                                                                        id: \"owner-address\",\n                                                                        rows: 3,\n                                                                        placeholder: \"Enter owner's address\",\n                                                                        value: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.address) || \"\",\n                                                                        onChange: (e)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                address: e.target.value\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1628,\n                                                                        columnNumber: 45\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1624,\n                                                                columnNumber: 41\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 pt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_14__.Label, {\n                                                                    htmlFor: \"owner-onboard\",\n                                                                    className: \"cursor-pointer\",\n                                                                    label: \"Is the owner on-board?\",\n                                                                    leftContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_22__.Checkbox, {\n                                                                        id: \"owner-onboard\",\n                                                                        checked: (rescueData === null || rescueData === void 0 ? void 0 : rescueData.ownerOnBoard) || false,\n                                                                        size: \"lg\",\n                                                                        isRadioStyle: true,\n                                                                        onCheckedChange: (checked)=>{\n                                                                            setRescueData({\n                                                                                ...rescueData,\n                                                                                ownerOnBoard: checked === true\n                                                                            });\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                        lineNumber: 1650,\n                                                                        columnNumber: 53\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                    lineNumber: 1645,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                                lineNumber: 1644,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                        lineNumber: 1533,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                                lineNumber: 1521,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1319,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1318,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_26__.Button, {\n                                        onClick: ()=>setShowInputDetailsPanel(false),\n                                        children: \"Save Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                        lineNumber: 1679,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                                    lineNumber: 1677,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                            lineNumber: 1317,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                    lineNumber: 1313,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n                lineNumber: 1310,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\vessel-rescue-fields.tsx\",\n        lineNumber: 1107,\n        columnNumber: 9\n    }, this);\n}\n_s(VesselRescueFields, \"NdmyJLvu/06md10blforhEP93qE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_23__.useSearchParams,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_28__.useMediaQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_30__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_29__.useLazyQuery\n    ];\n});\n_c = VesselRescueFields;\nvar _c;\n$RefreshReg$(_c, \"VesselRescueFields\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue-fields.tsx\n"));

/***/ })

});