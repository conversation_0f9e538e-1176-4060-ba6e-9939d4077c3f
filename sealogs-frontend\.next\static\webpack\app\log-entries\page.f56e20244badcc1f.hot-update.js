"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/restricted-visibility.tsx":
/*!************************************************************!*\
  !*** ./src/app/ui/logbook/forms/restricted-visibility.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RestrictedVisibility; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_eventType_RestrictedVisibility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/eventType_RestrictedVisibility */ \"(app-pages-browser)/./src/app/offline/models/eventType_RestrictedVisibility.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/slider */ \"(app-pages-browser)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_CloudFlareCaptures__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../components/CloudFlareCaptures */ \"(app-pages-browser)/./src/app/ui/logbook/components/CloudFlareCaptures.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RestrictedVisibility(param) {\n    let { currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, logBookConfig, locked, offline = false, members } = param;\n    var _tripEvent_eventType_RestrictedVisibility, _tripEvent_eventType_RestrictedVisibility1, _tripEvent_eventType_RestrictedVisibility2, _tripEvent_eventType_RestrictedVisibility3, _tripEvent_eventType_RestrictedVisibility4, _tripEvent_eventType_RestrictedVisibility5, _currentRisk_mitigationStrategy_nodes, _currentRisk_mitigationStrategy, _currentRisk_mitigationStrategy1;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_13__.useSearchParams)();\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [crossingTime, setCrossingTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [crossedTime, setCrossedTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [restrictedVisibility, setRestrictedVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [openProcedureChecklist, setOpenProcedureChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Used in the component to track if SOP should be displayed\n    const [displaySOP, setDisplaySOP] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedAuthor, setSelectedAuthor] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [riskFactors, setRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewMembers, setCrewMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentStartLocation, setCurrentStartLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentEndLocation, setCurrentEndLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_7__[\"default\"]();\n    const restrictedVisibilityModel = new _app_offline_models_eventType_RestrictedVisibility__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const currentEventRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [closeOnSave, setCloseOnSave] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleCrossingTimeChange = (date)=>{\n        setCrossingTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    const handleCrossedTimeChange = (date)=>{\n        setCrossedTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    // Function to set display SOP state - used in the component\n    const handleSetDisplaySOP = (value)=>{\n        setDisplaySOP(value);\n        setOpenProcedureChecklist(value);\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleDeleteRisk = async ()=>{\n        updateRiskFactor({\n            variables: {\n                input: {\n                    id: riskToDelete.id,\n                    eventType_RestrictedVisibilityID: 0,\n                    vesselID: 0\n                }\n            }\n        });\n        setOpenDeleteConfirmation(false);\n    };\n    const handleSetRiskToDelete = (risk)=>{\n        setRiskToDelete(risk);\n        setOpenDeleteConfirmation(true);\n    };\n    const handleNewStrategy = async ()=>{\n        if (content) {\n            createMitigationStrategy({\n                variables: {\n                    input: {\n                        strategy: content\n                    }\n                }\n            });\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.length > 0) {\n            if (currentStrategies.find((s)=>s.id === strategy.id)) {\n                setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n            } else {\n                setCurrentStrategies([\n                    ...currentStrategies,\n                    strategy\n                ]);\n            }\n        } else {\n            setCurrentStrategies([\n                strategy\n            ]);\n        }\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if (v.mitigationStrategy.nodes) {\n            setCurrentStrategies(v.mitigationStrategy.nodes);\n        }\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.title && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.title && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    // This function is no longer used as its functionality is now in handleRiskValue\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getRiskFactors({\n            variables: {\n                filter: {\n                    type: {\n                        eq: \"RestrictedVisibility\"\n                    }\n                }\n            }\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getRiskFactors({\n            variables: {\n                filter: {\n                    type: {\n                        eq: \"RestrictedVisibility\"\n                    }\n                }\n            }\n        });\n    }, [\n        openProcedureChecklist\n    ]);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _Array_from, _data_readRiskFactors_nodes;\n            const risks = (_Array_from = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n            setRiskFactors(data.readRiskFactors.nodes.filter((r)=>r.eventType_RestrictedVisibilityID == (restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.id)));\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setRestrictedVisibility(false);\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setRestrictedVisibility(false);\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentEvent = async (id)=>{\n        getTripEvent({\n            variables: {\n                id: id\n            }\n        });\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_RestrictedVisibility, _event_eventType_RestrictedVisibility1, _event_eventType_RestrictedVisibility2, _event_eventType_RestrictedVisibility3, _event_eventType_RestrictedVisibility4, _event_eventType_RestrictedVisibility5, _event_eventType_RestrictedVisibility6, _event_eventType_RestrictedVisibility7, _event_eventType_RestrictedVisibility8, _event_eventType_RestrictedVisibility9, _event_eventType_RestrictedVisibility10, _event_eventType_RestrictedVisibility11, _event_eventType_RestrictedVisibility12, _event_eventType_RestrictedVisibility13, _event_eventType_RestrictedVisibility14, _event_eventType_RestrictedVisibility15, _event_eventType_RestrictedVisibility16, _event_eventType_RestrictedVisibility17, _event_eventType_RestrictedVisibility18, _event_eventType_RestrictedVisibility19, _event_eventType_RestrictedVisibility20, _event_eventType_RestrictedVisibility21, _event_eventType_RestrictedVisibility22, _event_eventType_RestrictedVisibility23, _event_eventType_RestrictedVisibility24, _event_eventType_RestrictedVisibility25, _event_eventType_RestrictedVisibility26, _event_eventType_RestrictedVisibility27, _event_eventType_RestrictedVisibility28, _event_eventType_RestrictedVisibility29, _event_eventType_RestrictedVisibility30, _event_eventType_RestrictedVisibility31, _event_eventType_RestrictedVisibility32, _event_eventType_RestrictedVisibility33, _event_eventType_RestrictedVisibility34;\n                setTripEvent(event);\n                setRestrictedVisibility({\n                    id: +event.eventType_RestrictedVisibility.id,\n                    startLocationID: (_event_eventType_RestrictedVisibility = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility === void 0 ? void 0 : _event_eventType_RestrictedVisibility.startLocationID,\n                    crossingTime: (_event_eventType_RestrictedVisibility1 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility1 === void 0 ? void 0 : _event_eventType_RestrictedVisibility1.crossingTime,\n                    estSafeSpeed: (_event_eventType_RestrictedVisibility2 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility2 === void 0 ? void 0 : _event_eventType_RestrictedVisibility2.estSafeSpeed,\n                    stopAssessPlan: (_event_eventType_RestrictedVisibility3 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility3 === void 0 ? void 0 : _event_eventType_RestrictedVisibility3.stopAssessPlan,\n                    crewBriefing: (_event_eventType_RestrictedVisibility4 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility4 === void 0 ? void 0 : _event_eventType_RestrictedVisibility4.crewBriefing,\n                    navLights: (_event_eventType_RestrictedVisibility5 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility5 === void 0 ? void 0 : _event_eventType_RestrictedVisibility5.navLights,\n                    soundSignal: (_event_eventType_RestrictedVisibility6 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility6 === void 0 ? void 0 : _event_eventType_RestrictedVisibility6.soundSignal,\n                    lookout: (_event_eventType_RestrictedVisibility7 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility7 === void 0 ? void 0 : _event_eventType_RestrictedVisibility7.lookout,\n                    soundSignals: (_event_eventType_RestrictedVisibility8 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility8 === void 0 ? void 0 : _event_eventType_RestrictedVisibility8.soundSignals,\n                    radarWatch: (_event_eventType_RestrictedVisibility9 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility9 === void 0 ? void 0 : _event_eventType_RestrictedVisibility9.radarWatch,\n                    radioWatch: (_event_eventType_RestrictedVisibility10 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility10 === void 0 ? void 0 : _event_eventType_RestrictedVisibility10.radioWatch,\n                    endLocationID: (_event_eventType_RestrictedVisibility11 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility11 === void 0 ? void 0 : _event_eventType_RestrictedVisibility11.endLocationID,\n                    crossedTime: (_event_eventType_RestrictedVisibility12 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility12 === void 0 ? void 0 : _event_eventType_RestrictedVisibility12.crossedTime,\n                    approxSafeSpeed: (_event_eventType_RestrictedVisibility13 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility13 === void 0 ? void 0 : _event_eventType_RestrictedVisibility13.approxSafeSpeed,\n                    report: (_event_eventType_RestrictedVisibility14 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility14 === void 0 ? void 0 : _event_eventType_RestrictedVisibility14.report,\n                    startLat: (_event_eventType_RestrictedVisibility15 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility15 === void 0 ? void 0 : _event_eventType_RestrictedVisibility15.startLat,\n                    startLong: (_event_eventType_RestrictedVisibility16 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility16 === void 0 ? void 0 : _event_eventType_RestrictedVisibility16.startLong,\n                    endLat: (_event_eventType_RestrictedVisibility17 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility17 === void 0 ? void 0 : _event_eventType_RestrictedVisibility17.endLat,\n                    endLong: (_event_eventType_RestrictedVisibility18 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility18 === void 0 ? void 0 : _event_eventType_RestrictedVisibility18.endLong\n                });\n                if (((_event_eventType_RestrictedVisibility19 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility19 === void 0 ? void 0 : _event_eventType_RestrictedVisibility19.startLat) && ((_event_eventType_RestrictedVisibility20 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility20 === void 0 ? void 0 : _event_eventType_RestrictedVisibility20.startLong)) {\n                    var _event_eventType_RestrictedVisibility35, _event_eventType_RestrictedVisibility36;\n                    setCurrentStartLocation({\n                        latitude: (_event_eventType_RestrictedVisibility35 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility35 === void 0 ? void 0 : _event_eventType_RestrictedVisibility35.startLat,\n                        longitude: (_event_eventType_RestrictedVisibility36 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility36 === void 0 ? void 0 : _event_eventType_RestrictedVisibility36.startLong\n                    });\n                }\n                if (((_event_eventType_RestrictedVisibility21 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility21 === void 0 ? void 0 : _event_eventType_RestrictedVisibility21.endLat) && ((_event_eventType_RestrictedVisibility22 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility22 === void 0 ? void 0 : _event_eventType_RestrictedVisibility22.endLong)) {\n                    var _event_eventType_RestrictedVisibility37, _event_eventType_RestrictedVisibility38;\n                    setCurrentEndLocation({\n                        latitude: (_event_eventType_RestrictedVisibility37 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility37 === void 0 ? void 0 : _event_eventType_RestrictedVisibility37.endLat,\n                        longitude: (_event_eventType_RestrictedVisibility38 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility38 === void 0 ? void 0 : _event_eventType_RestrictedVisibility38.endLong\n                    });\n                }\n                setCrossedTime((_event_eventType_RestrictedVisibility23 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility23 === void 0 ? void 0 : _event_eventType_RestrictedVisibility23.crossedTime);\n                setCrossingTime((_event_eventType_RestrictedVisibility24 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility24 === void 0 ? void 0 : _event_eventType_RestrictedVisibility24.crossingTime);\n                if (((_event_eventType_RestrictedVisibility25 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility25 === void 0 ? void 0 : _event_eventType_RestrictedVisibility25.memberID) > 0) {\n                    var _event_eventType_RestrictedVisibility39, _event_eventType_RestrictedVisibility40, _event_eventType_RestrictedVisibility41;\n                    setSelectedAuthor({\n                        label: \"\".concat((_event_eventType_RestrictedVisibility39 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility39 === void 0 ? void 0 : _event_eventType_RestrictedVisibility39.member.firstName, \" \").concat((_event_eventType_RestrictedVisibility40 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility40 === void 0 ? void 0 : _event_eventType_RestrictedVisibility40.member.surname),\n                        value: (_event_eventType_RestrictedVisibility41 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility41 === void 0 ? void 0 : _event_eventType_RestrictedVisibility41.memberID\n                    });\n                }\n                if (((_event_eventType_RestrictedVisibility26 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility26 === void 0 ? void 0 : _event_eventType_RestrictedVisibility26.stopAssessPlan) || ((_event_eventType_RestrictedVisibility27 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility27 === void 0 ? void 0 : _event_eventType_RestrictedVisibility27.crewBriefing) || ((_event_eventType_RestrictedVisibility28 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility28 === void 0 ? void 0 : _event_eventType_RestrictedVisibility28.navLights) || ((_event_eventType_RestrictedVisibility29 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility29 === void 0 ? void 0 : _event_eventType_RestrictedVisibility29.soundSignal) || ((_event_eventType_RestrictedVisibility30 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility30 === void 0 ? void 0 : _event_eventType_RestrictedVisibility30.lookout) || ((_event_eventType_RestrictedVisibility31 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility31 === void 0 ? void 0 : _event_eventType_RestrictedVisibility31.soundSignals) || ((_event_eventType_RestrictedVisibility32 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility32 === void 0 ? void 0 : _event_eventType_RestrictedVisibility32.radarWatch) || ((_event_eventType_RestrictedVisibility33 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility33 === void 0 ? void 0 : _event_eventType_RestrictedVisibility33.radioWatch) || ((_event_eventType_RestrictedVisibility34 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility34 === void 0 ? void 0 : _event_eventType_RestrictedVisibility34.memberID) > 0) {\n                    setDisplaySOP(true);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        const variables = {\n            input: {\n                startLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.startLocationID,\n                crossingTime: crossingTime !== null && crossingTime !== void 0 ? crossingTime : dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"),\n                estSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.estSafeSpeed,\n                stopAssessPlan: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.stopAssessPlan,\n                crewBriefing: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.crewBriefing,\n                navLights: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.navLights,\n                soundSignal: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignal,\n                lookout: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.lookout,\n                soundSignals: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignals,\n                radarWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radarWatch,\n                radioWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radioWatch,\n                endLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.endLocationID,\n                crossedTime: crossedTime !== null && crossedTime !== void 0 ? crossedTime : dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"),\n                approxSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.approxSafeSpeed,\n                report: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.report,\n                startLat: currentStartLocation.latitude.toString(),\n                startLong: currentStartLocation.longitude.toString(),\n                endLat: currentEndLocation.latitude.toString(),\n                endLong: currentEndLocation.longitude.toString(),\n                memberID: selectedAuthor === null || selectedAuthor === void 0 ? void 0 : selectedAuthor.value\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                await tripEventModel.save({\n                    id: +currentEvent.id,\n                    eventCategory: \"RestrictedVisibility\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +currentEvent.id,\n                            eventCategory: \"RestrictedVisibility\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n            if (offline) {\n                await restrictedVisibilityModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_RestrictedVisibilityID),\n                    ...variables.input\n                });\n            } else {\n                updateEventType_RestrictedVisibility({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_RestrictedVisibilityID),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                const tripEventData = await tripEventModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_9__.generateUniqueId)(),\n                    eventCategory: \"RestrictedVisibility\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                setCurrentEvent(tripEventData);\n                const restrictedVisibilityData = await restrictedVisibilityModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_9__.generateUniqueId)(),\n                    startLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.startLocationID,\n                    crossingTime: crossingTime,\n                    estSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.estSafeSpeed,\n                    stopAssessPlan: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.stopAssessPlan,\n                    crewBriefing: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.crewBriefing,\n                    navLights: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.navLights,\n                    soundSignal: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignal,\n                    lookout: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.lookout,\n                    soundSignals: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignals,\n                    radarWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radarWatch,\n                    radioWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radioWatch,\n                    endLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.endLocationID,\n                    crossedTime: crossedTime,\n                    approxSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.approxSafeSpeed,\n                    report: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.report,\n                    startLat: currentStartLocation.latitude.toString(),\n                    startLong: currentStartLocation.longitude.toString(),\n                    endLat: currentEndLocation.latitude.toString(),\n                    endLong: currentEndLocation.longitude.toString()\n                });\n                await tripEventModel.save({\n                    id: tripEventData.id,\n                    eventCategory: \"RestrictedVisibility\",\n                    eventType_RestrictedVisibilityID: restrictedVisibilityData.id\n                });\n                getCurrentEvent(tripEventData.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                if (closeOnSave) {\n                    setCloseOnSave(false);\n                    closeModal();\n                }\n            } else {\n                createTripEvent({\n                    variables: {\n                        input: {\n                            eventCategory: \"RestrictedVisibility\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripEvent, {\n        onCompleted: (response)=>{\n            const data = response.createTripEvent;\n            currentEventRef.current = data;\n            setCurrentEvent(data);\n            createEventType_RestrictedVisibility({\n                variables: {\n                    input: {\n                        startLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.startLocationID,\n                        crossingTime: crossingTime,\n                        estSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.estSafeSpeed,\n                        stopAssessPlan: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.stopAssessPlan,\n                        crewBriefing: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.crewBriefing,\n                        navLights: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.navLights,\n                        soundSignal: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignal,\n                        lookout: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.lookout,\n                        soundSignals: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignals,\n                        radarWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radarWatch,\n                        radioWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radioWatch,\n                        endLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.endLocationID,\n                        crossedTime: crossedTime,\n                        approxSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.approxSafeSpeed,\n                        report: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.report,\n                        startLat: currentStartLocation.latitude.toString(),\n                        startLong: currentStartLocation.longitude.toString(),\n                        endLat: currentEndLocation.latitude.toString(),\n                        endLong: currentEndLocation.longitude.toString(),\n                        memberID: selectedAuthor === null || selectedAuthor === void 0 ? void 0 : selectedAuthor.value\n                    }\n                }\n            });\n            updateTripEvent({\n                variables: {\n                    input: {\n                        id: data.id,\n                        eventCategory: \"RestrictedVisibility\",\n                        eventType_RestrictedVisibilityID: data.id\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip event\", error);\n        }\n    });\n    const [createEventType_RestrictedVisibility] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateEventType_RestrictedVisibility, {\n        onCompleted: (response)=>{\n            var _currentEventRef_current;\n            const data = response.createEventType_RestrictedVisibility;\n            updateTripEvent({\n                variables: {\n                    input: {\n                        id: (_currentEventRef_current = currentEventRef.current) === null || _currentEventRef_current === void 0 ? void 0 : _currentEventRef_current.id,\n                        eventType_RestrictedVisibilityID: data.id\n                    }\n                }\n            });\n            if (closeOnSave) {\n                setCloseOnSave(false);\n                closeModal();\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error creating Person rescue\", error);\n        }\n    });\n    const [updateEventType_RestrictedVisibility] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateEventType_RestrictedVisibility, {\n        onCompleted: ()=>{\n            // Successfully updated restricted visibility\n            if (closeOnSave) {\n                setCloseOnSave(false);\n                closeModal();\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error updating restricted visibility\", error);\n        }\n    });\n    const [updateTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripEvent, {\n        onCompleted: ()=>{\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating trip event\", error);\n        }\n    });\n    const displayField = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0 && ((_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const handleStartLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setRestrictedVisibility({\n                ...restrictedVisibility,\n                startLocationID: +value.value,\n                startLat: null,\n                startLong: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setRestrictedVisibility({\n                ...restrictedVisibility,\n                startLocationID: 0,\n                startLat: value.latitude,\n                startLong: value.longitude\n            });\n        }\n    };\n    const handleEndLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setRestrictedVisibility({\n                ...restrictedVisibility,\n                endLocationID: +value.value,\n                endLat: null,\n                endLong: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setRestrictedVisibility({\n                ...restrictedVisibility,\n                endLocationID: 0,\n                endLat: value.latitude,\n                endLong: value.longitude\n            });\n        }\n    };\n    const startLocationData = {\n        geoLocationID: (restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.startLocationID) > 0 ? restrictedVisibility.startLocationID : (_tripEvent_eventType_RestrictedVisibility = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility.startLocationID,\n        lat: (_tripEvent_eventType_RestrictedVisibility1 = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility1 === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility1.startLat,\n        long: (_tripEvent_eventType_RestrictedVisibility2 = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility2 === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility2.startLong\n    };\n    const endLocationData = {\n        geoLocationID: (restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.endLocationID) > 0 ? restrictedVisibility.endLocationID : (_tripEvent_eventType_RestrictedVisibility3 = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility3 === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility3.endLocationID,\n        lat: (_tripEvent_eventType_RestrictedVisibility4 = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility4 === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility4.endLat,\n        long: (_tripEvent_eventType_RestrictedVisibility5 = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility5 === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility5.endLong\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (members) {\n            const crewMembers = members.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMemberID\n                };\n            });\n            setCrewMembers(crewMembers);\n        }\n    }, [\n        members\n    ]);\n    const riskImpacts = [\n        {\n            value: \"Low\",\n            label: \"Low impact\"\n        },\n        {\n            value: \"Medium\",\n            label: \"Medium impact\"\n        },\n        {\n            value: \"High\",\n            label: \"High impact\"\n        },\n        {\n            value: \"Severe\",\n            label: \"Severe impact\"\n        }\n    ];\n    const handleSaveRisk = async ()=>{\n        if (currentRisk.id > 0) {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: currentRisk.id,\n                        type: \"RestrictedVisibility\",\n                        title: currentRisk.title,\n                        impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                        probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                        mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                        eventType_RestrictedVisibilityID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.id\n                    }\n                }\n            });\n        } else {\n            createRiskFactor({\n                variables: {\n                    input: {\n                        type: \"RestrictedVisibility\",\n                        title: currentRisk.title,\n                        impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                        probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                        mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                        eventType_RestrictedVisibilityID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.id,\n                        vesselID: vesselID\n                    }\n                }\n            });\n        }\n    };\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateRiskFactor, {\n        onCompleted: ()=>{\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"RestrictedVisibility\"\n                        }\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"RestrictedVisibility\"\n                        }\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleCreateRisk = (inputValue)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: inputValue\n        });\n        setRiskValue({\n            value: inputValue,\n            label: inputValue\n        });\n        if (allRisks) {\n            const risk = [\n                ...allRisks,\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ];\n            setAllRisks(risk);\n        } else {\n            setAllRisks([\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ]);\n        }\n    };\n    const handleRiskValue = (v)=>{\n        // If v is null, user cleared the selection\n        if (!v) {\n            setCurrentRisk({\n                ...currentRisk,\n                title: \"\"\n            });\n            setRiskValue(null);\n            setRecommendedStratagies(false);\n            return;\n        }\n        // Check if this is a new value (not in existing options)\n        const isNewValue = !allRisks.some((risk)=>risk.value === v.value);\n        if (isNewValue) {\n            // Handle creating a new risk option\n            handleCreateRisk(v.value);\n        } else {\n            // Handle selecting an existing risk\n            setCurrentRisk({\n                ...currentRisk,\n                title: v.value\n            });\n            setRiskValue({\n                value: v.value,\n                label: v.value\n            });\n            if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n                var _risk_mitigationStrategy_nodes;\n                return risk.title === v.value && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n            }).length) > 0) {\n                setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                    var _r_mitigationStrategy_nodes;\n                    return r.title === v.value && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n                }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                        id: s.id,\n                        strategy: s.strategy\n                    })))));\n            } else {\n                setRecommendedStratagies(false);\n            }\n        }\n    };\n    var _restrictedVisibility_estSafeSpeed, _restrictedVisibility_approxSafeSpeed, _restrictedVisibility_report;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            displayField(\"RestrictedVisibility_CrossingTime\") || displayField(\"RestrictedVisibility_StartLocation\") || displayField(\"RestrictedVisibility_EstSafeSpeed\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [\n                        displayField(\"RestrictedVisibility_StartLocation\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                            htmlFor: \"startLocation\",\n                            disabled: locked,\n                            label: \"Location where limited visibility starts\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                offline: offline,\n                                setCurrentLocation: setCurrentStartLocation,\n                                handleLocationChange: handleStartLocationChange,\n                                currentEvent: startLocationData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 867,\n                            columnNumber: 29\n                        }, this),\n                        displayField(\"RestrictedVisibility_CrossingTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                            htmlFor: \"crossingTime\",\n                            disabled: locked,\n                            label: \"Time where limited visibility starts\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                time: crossingTime,\n                                handleTimeChange: handleCrossingTimeChange,\n                                timeID: \"crossingTime\",\n                                fieldName: \"Time vis. restriction started\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 886,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 882,\n                            columnNumber: 29\n                        }, this),\n                        displayField(\"RestrictedVisibility_EstSafeSpeed\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                            htmlFor: \"estSafeSpeed\",\n                            disabled: locked,\n                            label: \"Estimated safe speed for conditions\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                id: \"estSafeSpeed\",\n                                type: \"number\",\n                                placeholder: \"Enter safe speed for conditions\",\n                                min: 1,\n                                className: \"w-full\",\n                                value: (_restrictedVisibility_estSafeSpeed = restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.estSafeSpeed) !== null && _restrictedVisibility_estSafeSpeed !== void 0 ? _restrictedVisibility_estSafeSpeed : undefined,\n                                onChange: (e)=>{\n                                    setRestrictedVisibility({\n                                        ...restrictedVisibility,\n                                        estSafeSpeed: e.target.value\n                                    });\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 899,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 895,\n                            columnNumber: 29\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                    lineNumber: 865,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false) : null,\n            displayField(\"RestrictedVisibility_StopAssessPlan\") || displayField(\"RestrictedVisibility_CrewBriefing\") || displayField(\"RestrictedVisibility_NavLights\") || displayField(\"RestrictedVisibility_SoundSignal\") || displayField(\"RestrictedVisibility_Lookout\") || displayField(\"RestrictedVisibility_SoundSignals\") || displayField(\"RestrictedVisibility_RadarWatch\") || displayField(\"RestrictedVisibility_RadioWatch\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 930,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                id: \"displaySOP\",\n                                checked: restrictedVisibility !== false,\n                                onClick: ()=>setOpenProcedureChecklist(true),\n                                label: \"Safe operating procedures checklist\",\n                                variant: \"success\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 931,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 949,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true) : null,\n            displayField(\"RestrictedVisibility_EndLocation\") || displayField(\"RestrictedVisibility_CrossedTime\") || displayField(\"RestrictedVisibility_ApproxSafeSpeed\") || displayField(\"RestrictedVisibility_Report\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col space-y-6\",\n                children: [\n                    displayField(\"RestrictedVisibility_EndLocation\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        htmlFor: \"endLocation\",\n                        disabled: locked,\n                        label: \"Location where limited visibility ends\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: setCurrentEndLocation,\n                            handleLocationChange: handleEndLocationChange,\n                            currentEvent: endLocationData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 962,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 958,\n                        columnNumber: 25\n                    }, this),\n                    displayField(\"RestrictedVisibility_CrossedTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        htmlFor: \"crossedTime\",\n                        disabled: locked,\n                        label: \"Time when limited visibility ends\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            time: crossedTime,\n                            handleTimeChange: handleCrossedTimeChange,\n                            timeID: \"crossedTime\",\n                            fieldName: \"End time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 971,\n                        columnNumber: 25\n                    }, this),\n                    displayField(\"RestrictedVisibility_ApproxSafeSpeed\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        htmlFor: \"approxSafeSpeed\",\n                        disabled: locked,\n                        label: \"Approximate average speed during restricted visibility period\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                            id: \"approxSafeSpeed\",\n                            type: \"number\",\n                            placeholder: \"Enter approximate average speed\",\n                            min: 1,\n                            className: \"w-full\",\n                            value: (_restrictedVisibility_approxSafeSpeed = restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.approxSafeSpeed) !== null && _restrictedVisibility_approxSafeSpeed !== void 0 ? _restrictedVisibility_approxSafeSpeed : undefined,\n                            onChange: (e)=>{\n                                setRestrictedVisibility({\n                                    ...restrictedVisibility,\n                                    approxSafeSpeed: e.target.value\n                                });\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 988,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 984,\n                        columnNumber: 25\n                    }, this),\n                    displayField(\"RestrictedVisibility_Report\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        htmlFor: \"restricted-visibility-report\",\n                        disabled: locked,\n                        label: \"Comments or observations\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                            id: \"restricted-visibility-report\",\n                            className: \"w-full min-h-[120px]\",\n                            rows: 4,\n                            placeholder: \"Add any comments or observations pertinant to the limited visibility event\",\n                            value: (_restrictedVisibility_report = restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.report) !== null && _restrictedVisibility_report !== void 0 ? _restrictedVisibility_report : undefined,\n                            onChange: (e)=>{\n                                setRestrictedVisibility({\n                                    ...restrictedVisibility,\n                                    report: e.target.value\n                                });\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 1012,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1008,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-1 md:col-span-2 flex flex-col sm:flex-row justify-end gap-2 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                variant: \"back\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                onClick: closeModal,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1030,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                variant: \"primary\",\n                                onClick: locked ? ()=>{} : ()=>{\n                                    setCloseOnSave(true);\n                                    handleSave();\n                                },\n                                disabled: locked,\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                children: selectedEvent ? \"Update\" : \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1036,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1029,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                lineNumber: 956,\n                columnNumber: 17\n            }, this) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.Sheet, {\n                open: openProcedureChecklist,\n                onOpenChange: setOpenProcedureChecklist,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetContent, {\n                    side: \"right\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetTitle, {\n                                children: \"Safe operating procedures checklist\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1058,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 1057,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetBody, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" grid grid-cols-1 gap-6\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        displayField(\"RestrictedVisibility_StopAssessPlan\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"stopAssessPlan\",\n                                            checked: restrictedVisibility.stopAssessPlan,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    stopAssessPlan: checked === true\n                                                });\n                                            },\n                                            label: \"Stopped, assessed, planned\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 37\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_CrewBriefing\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"crewBriefing\",\n                                            checked: restrictedVisibility.crewBriefing,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    crewBriefing: checked === true\n                                                });\n                                            },\n                                            label: \"Briefed crew\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1090,\n                                            columnNumber: 37\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_NavLights\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"navLights\",\n                                            checked: restrictedVisibility.navLights,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    navLights: checked === true\n                                                });\n                                            },\n                                            label: \"Navigation lights on\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1110,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {\n                                            className: \"my-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1125,\n                                            columnNumber: 33\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_SoundSignal\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                    htmlFor: \"soundSignal\",\n                                                    label: \"Sounds signals used (pick one)\",\n                                                    className: \"mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                    lineNumber: 1131,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_21__.RadioGroup, {\n                                                    defaultValue: restrictedVisibility.soundSignal,\n                                                    onValueChange: (value)=>{\n                                                        setRestrictedVisibility({\n                                                            ...restrictedVisibility,\n                                                            soundSignal: value\n                                                        });\n                                                    },\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                                            type: \"radio\",\n                                                            id: \"soundSignalNone\",\n                                                            value: \"None\",\n                                                            label: \"None needed\",\n                                                            radioGroupValue: restrictedVisibility.soundSignal,\n                                                            variant: \"warning\",\n                                                            onCheckedChange: ()=>setRestrictedVisibility({\n                                                                    ...restrictedVisibility,\n                                                                    soundSignal: \"None\"\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                            lineNumber: 1149,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                                            type: \"radio\",\n                                                            id: \"soundSignalMakingWay\",\n                                                            value: \"MakingWay\",\n                                                            label: \"Making way (1 long / 2 mins)\",\n                                                            radioGroupValue: restrictedVisibility.soundSignal,\n                                                            variant: \"warning\",\n                                                            onCheckedChange: ()=>setRestrictedVisibility({\n                                                                    ...restrictedVisibility,\n                                                                    soundSignal: \"MakingWay\"\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                            lineNumber: 1166,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                                            type: \"radio\",\n                                                            id: \"soundSignalNotMakingWay\",\n                                                            value: \"NotMakingWay\",\n                                                            label: \"Not making way (2 long / 2 mins)\",\n                                                            radioGroupValue: restrictedVisibility.soundSignal,\n                                                            variant: \"warning\",\n                                                            onCheckedChange: ()=>setRestrictedVisibility({\n                                                                    ...restrictedVisibility,\n                                                                    soundSignal: \"NotMakingWay\"\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                            lineNumber: 1184,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                                            type: \"radio\",\n                                                            id: \"soundSignalTowing\",\n                                                            value: \"Towing\",\n                                                            label: \"Towing (1 long + 2 short / 2 mins)\",\n                                                            radioGroupValue: restrictedVisibility.soundSignal,\n                                                            variant: \"warning\",\n                                                            onCheckedChange: ()=>setRestrictedVisibility({\n                                                                    ...restrictedVisibility,\n                                                                    soundSignal: \"Towing\"\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                            lineNumber: 1202,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                    lineNumber: 1136,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1130,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {\n                                            className: \"my-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1222,\n                                            columnNumber: 33\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_Lookout\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"lookout\",\n                                            checked: restrictedVisibility.lookout,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    lookout: checked === true\n                                                });\n                                            },\n                                            label: \"Set proper lookout\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1227,\n                                            columnNumber: 37\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_SoundSignals\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"soundSignals\",\n                                            checked: restrictedVisibility.soundSignals,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    soundSignals: checked === true\n                                                });\n                                            },\n                                            label: \"Listening for other sound signals\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1245,\n                                            columnNumber: 37\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_RadarWatch\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"radarWatch\",\n                                            checked: restrictedVisibility.radarWatch,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    radarWatch: checked === true\n                                                });\n                                            },\n                                            label: \"Radar watch on\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1265,\n                                            columnNumber: 37\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_RadioWatch\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"radioWatch\",\n                                            checked: restrictedVisibility.radioWatch,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    radioWatch: checked === true\n                                                });\n                                            },\n                                            label: \"Radio watch on\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1285,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full flex flex-col space-y-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CloudFlareCaptures__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                inputId: (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id) || 0,\n                                                sectionId: currentTrip.id,\n                                                buttonType: \"button\",\n                                                sectionName: \"tripEventID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                lineNumber: 1302,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1301,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                    lineNumber: 1065,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1063,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 1062,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetFooter, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                onClick: ()=>setOpenProcedureChecklist(false),\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1313,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 1312,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                    lineNumber: 1056,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                lineNumber: 1053,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__.AlertDialogNew, {\n                openDialog: openRiskDialog,\n                setOpenDialog: setOpenRiskDialog,\n                handleCreate: handleSaveRisk,\n                title: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.id) > 0 ? \"Update Risk\" : \"Create New Risk\",\n                actionText: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.id) > 0 ? \"Update\" : \"Create Risk\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                htmlFor: \"impact\",\n                                label: \"Risk\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1328,\n                                columnNumber: 21\n                            }, this),\n                            allRisks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                id: \"impact\",\n                                options: allRisks,\n                                placeholder: \"Select or enter a risk\",\n                                value: riskValue,\n                                onChange: handleRiskValue,\n                                buttonClassName: \"w-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1330,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1327,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                htmlFor: \"risk-impact\",\n                                label: \"Risk impact\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1341,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                id: \"impact\",\n                                options: riskImpacts,\n                                placeholder: \"Select risk impact\",\n                                value: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? riskImpacts === null || riskImpacts === void 0 ? void 0 : riskImpacts.find((impact)=>impact.value == (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact)) : null,\n                                onChange: (value)=>setCurrentRisk({\n                                        ...currentRisk,\n                                        impact: value === null || value === void 0 ? void 0 : value.value\n                                    }),\n                                buttonClassName: \"w-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1342,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1340,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                htmlFor: \"risk-probability\",\n                                label: \"Risk probability\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1364,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_17__.Slider, {\n                                        defaultValue: [\n                                            (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5\n                                        ],\n                                        className: \"my-4\",\n                                        onValueChange: (value)=>setCurrentRisk({\n                                                ...currentRisk,\n                                                probability: value[0]\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1369,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Low\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                lineNumber: 1380,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"High\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                lineNumber: 1381,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1379,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1368,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1363,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                                className: \"text-lg font-semibold leading-6 text-gray-700 mb-4\",\n                                children: \"Mitigation strategy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1386,\n                                columnNumber: 21\n                            }, this),\n                            (currentRisk === null || currentRisk === void 0 ? void 0 : (_currentRisk_mitigationStrategy = currentRisk.mitigationStrategy) === null || _currentRisk_mitigationStrategy === void 0 ? void 0 : (_currentRisk_mitigationStrategy_nodes = _currentRisk_mitigationStrategy.nodes) === null || _currentRisk_mitigationStrategy_nodes === void 0 ? void 0 : _currentRisk_mitigationStrategy_nodes.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-gray-50 rounded-md\",\n                                children: currentRisk === null || currentRisk === void 0 ? void 0 : (_currentRisk_mitigationStrategy1 = currentRisk.mitigationStrategy) === null || _currentRisk_mitigationStrategy1 === void 0 ? void 0 : _currentRisk_mitigationStrategy1.nodes.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2 last:mb-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            dangerouslySetInnerHTML: {\n                                                __html: s.strategy\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1395,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, s.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1394,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1391,\n                                columnNumber: 25\n                            }, this),\n                            content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-4 bg-gray-50 rounded-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    dangerouslySetInnerHTML: {\n                                        __html: content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                    lineNumber: 1408,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1407,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                    variant: \"primary\",\n                                    className: \"bg-orange-400 hover:bg-orange-500\",\n                                    onClick: ()=>setOpenRecommendedstrategy(true),\n                                    children: \"Add strategy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                    lineNumber: 1417,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1416,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1385,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                lineNumber: 1321,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__.AlertDialogNew, {\n                openDialog: openRecommendedstrategy,\n                setOpenDialog: setOpenRecommendedstrategy,\n                handleCreate: handleNewStrategy,\n                title: \"Recommended strategy\",\n                actionText: \"Save\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                                children: \"Available strategies\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1433,\n                                columnNumber: 21\n                            }, this),\n                            recommendedStratagies ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-3\",\n                                        children: recommendedStratagies === null || recommendedStratagies === void 0 ? void 0 : recommendedStratagies.map((risk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                onClick: ()=>{\n                                                    handleSetCurrentStrategies(risk);\n                                                    if (currentRisk) {\n                                                        handleSetRiskValue({\n                                                            title: currentRisk.title,\n                                                            mitigationStrategy: {\n                                                                nodes: [\n                                                                    risk\n                                                                ]\n                                                            }\n                                                        });\n                                                    }\n                                                },\n                                                className: \"\".concat((currentStrategies === null || currentStrategies === void 0 ? void 0 : currentStrategies.find((s)=>s.id === risk.id)) ? \"border-orange-400 bg-orange-50\" : \"border-gray-200 bg-gray-50\", \" border p-4 rounded-lg cursor-pointer text-left w-full\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: risk === null || risk === void 0 ? void 0 : risk.strategy\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                    lineNumber: 1452,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, risk.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                lineNumber: 1438,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1436,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                                        className: \"text-lg font-normal leading-6 text-gray-700 mt-6\",\n                                        children: \"or add new Mitigation strategy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1460,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                                        className: \"p-4 bg-gray-50 rounded-md text-gray-600 text-center\",\n                                        children: \"No recommendations available!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1466,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                                        className: \"text-lg font-normal leading-6 mt-4 mb-2 text-gray-700\",\n                                        children: \"Create a new strategy instead\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1469,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1432,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                htmlFor: \"strategy\",\n                                label: \"Strategy details\",\n                                className: \"block mb-2 font-medium text-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1476,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-md overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    id: \"strategy\",\n                                    placeholder: \"Mitigation strategy\",\n                                    className: \"w-full\",\n                                    content: content,\n                                    handleEditorChange: handleEditorChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                    lineNumber: 1482,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1481,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1475,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                lineNumber: 1426,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: handleDeleteRisk,\n                title: \"Delete risk analysis!\",\n                actionText: \"Delete\",\n                variant: \"\",\n                showDestructiveAction: true,\n                destructiveActionText: \"Delete\",\n                handleDestructiveAction: handleDeleteRisk,\n                children: \"Are you sure you want to delete this risk?\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                lineNumber: 1492,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n        lineNumber: 860,\n        columnNumber: 9\n    }, this);\n}\n_s(RestrictedVisibility, \"EgioK3yv1FmAu+UeMCuY7LVYwY4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_13__.useSearchParams,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation\n    ];\n});\n_c = RestrictedVisibility;\nvar _c;\n$RefreshReg$(_c, \"RestrictedVisibility\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/restricted-visibility.tsx\n"));

/***/ })

});