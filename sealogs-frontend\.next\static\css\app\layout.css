/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/globals.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: currentColor; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: IBMPlexSans, sans-serif; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
    :root {
        --radius: 0.5rem;

        /* base surfaces & text */
        --background: 216 33% 97%; /* Wedgewood 50   */
        --foreground: 207 11% 31%; /* Outer-Space 700 */

        /* cards & popovers */
        --card: 0 0% 100%; /* pure white      */
        --card-foreground: 210 9% 27%; /* Outer-Space 800 */

        --popover: 216 33% 97%; /* Wedgewood 50    */
        --popover-foreground: 207 11% 31%; /* Outer-Space 700 */

        /* brand / primary */
        --primary: 208 79% 27%; /* Curious-Blue 800 */
        --primary-foreground: 208 87% 97%; /* Curious-Blue 50  */

        /* NEW - secondary theme tone (Wedgewood) */
        --secondary: 206 34% 60%; /* Wedgewood 400   */
        --secondary-foreground: 207 11% 31%; /* Outer-Space 700 */

        /* muted / neutrals */
        --muted: 216 33% 97%; /* Wedgewood 50 */
        --muted-foreground: 201 11% 53%; /* Outer-Space 400 */

        /* accent / hover highlight */
        --accent: 208 87% 97%; /* Curious-Blue 50 */
        --accent-foreground: 205 82% 54%; /* Curious-Blue 400 */

        /* NEW - semantic success & warning */
        --success: 174 100% 40%; /* Bright-Turquoise 500 */
        --success-foreground: 166 100% 97%; /* Bright-Turquoise 50  */
        --warning: 30 80% 50%; /* Fire-Bush 500        */
        --warning-foreground: 40 90% 96%; /* Fire-Bush 50         */

        /* destructive / error */
        --destructive: 1 83% 54%; /* Cinnabar 600     */
        --destructive-foreground: 0 100% 97%; /* Cinnabar 50      */

        /* lines / controls */
        --border: 196 12% 82%; /* Outer-Space 200  */
        --input: 201 12% 43%; /* Outer-Space 500  */
        --ring: 205 82% 54%; /* Curious-Blue 400 */

        /* sample chart palette */
        --chart-1: hsl(2, 100%, 94%);
        --chart-2: hsl(41, 83%, 77%);
        --chart-3: hsl(210, 81%, 94%);
        --chart-4: hsl(209, 35%, 86%);
        --chart-5: hsl(168, 100%, 89%);

        /* sidebar */
        --sidebar: 0 0% 100%;
        --sidebar-foreground: 207 11% 31%; /* Outer-Space 700  */
        --sidebar-primary: 208 79% 27%;
        --sidebar-primary-foreground: 208 87% 97%; /* Curious-Blue 50 */
        --sidebar-accent: 206 35% 74%; /* Wedgewood 300    */
        --sidebar-border: var(--border);
        --sidebar-ring: 205 82% 54%; /* Curious-Blue 400 */

        /* ──────────────────────────────────────────────────────────────
           COLORS THAT NEED DARK MODE SUPPORT ONLY
           (Other colors use direct tailwind config values)
        ──────────────────────────────────────────────────────────────── */

        /* Neutral/Text colors that change in dark mode */
        --neutral-400: 201 11% 53%;        /* Outer-Space 400 */
        --neutral-600: 205 12% 36%;        /* Outer-Space 600 */
        --neutral-800: 210 9% 27%;         /* Outer-Space 800 */

        /* Background colors that need dark mode variants */
        --warning-100: 41 82% 89%;         /* Fire-Bush 100 */
        --success-100: 168 100% 89%;       /* Bright-Turquoise 100 */
        --destructive-200: 1 100% 89%;     /* Cinnabar 200 */
    }

    .dark {
        /* base surfaces & text */
        --background: 212 75% 8%; /* Curious-Blue ultra-dark-50 */
        --foreground: 210 81% 94%; /* Curious-Blue 50 */

        /* cards & popovers */
        --card: 212 70% 14%; /* Curious-Blue ultra-dark-200 */
        --card-foreground: 210 81% 94%; /* Curious-Blue 100 */

        --popover: 212 72% 16%; /* Curious-Blue ultra-dark-300 */
        --popover-foreground: 208 87% 97%; /* Curious-Blue 50  */

        /* brand / primary */
        --primary: 207 86% 39%; /* Curious-Blue 600 */
        --primary-foreground: 208 87% 97%; /* Curious-Blue 50  */

        /* secondary tone */
        --secondary: 208 35% 32%; /* Wedgewood 700    */
        --secondary-foreground: 208 87% 97%; /* Curious-Blue 50  */

        /* muted */
        --muted: 201 11% 53%; /* Outer-Space 400 */
        --muted-foreground: 204 14% 69%; /* Outer-Space 400 */

        /* accent / hover highlight */
        --accent: 209 70% 24%; /* Curious-Blue 900 */
        --accent-foreground: 208 87% 97%; /* Curious-Blue 50  */

        /* success & warning */
        --success: 171 97% 64%; /* Bright-Turquoise 300 */
        --success-foreground: 0 0% 98%;

        --warning: 30 80% 50%; /* Fire-Bush 500 */
        --warning-foreground: 40 90% 96%; /* Fire-Bush 50 */

        /* destructive */
        --destructive: 1 100% 70%; /* Cinnabar 400     */
        --destructive-foreground: 2 100% 94%; /* Cinnabar 100 */

        /* lines / controls */
        --border: 212 45% 28%; /* light but subtle blue border */
        --input: 0 0% 100%;
        --ring: 240 4.9% 83.9%;

        /* chart palette (unchanged) */
        --chart-1: hsl(2, 100%, 94%);
        --chart-2: hsl(41, 83%, 77%);
        --chart-3: hsl(207, 83%, 86%); /* Curious-Blue 300 */
        --chart-4: hsl(206, 35%, 74%); /* Wedgewood 300 */
        --chart-5: hsl(170, 100%, 78%); /* Bright-Turquoise 200 */

        /* sidebar */
        --sidebar: 212 70% 14%; /* Curious-Blue ultra-dark-200 */
        --sidebar-foreground: 208 87% 97%; /* Curious-Blue 50  */
        --sidebar-primary: 209 70% 24%; /* Curious-Blue 900 */
        --sidebar-primary-foreground: 210 81% 94%; /* Curious-Blue 100 */
        --sidebar-accent: 208 30% 24%; /* Wedgewood 900    */
        --sidebar-border: 212 45% 28%; /* light but subtle blue border */
        --sidebar-ring: 240 4.9% 83.9%;

        /* ──────────────────────────────────────────────────────────────
           ADDITIONAL FREQUENTLY USED COLOR SLOTS - DARK MODE
           (Only colors that actually need to change in dark mode)
        ──────────────────────────────────────────────────────────────── */

        /* Neutral/Text colors that need dark mode adjustment */
        --neutral-400: 204 14% 69%;        /* Outer-Space 400 */
        --neutral-600: 205 12% 36%;        /* Outer-Space 600 */
        --neutral-800: 210 81% 94%;        /* Curious-Blue 100 (lighter for dark) */

        /* Background colors that need adjustment */
        --warning-100: 15 71% 31%;         /* Fire-Bush 900 (darker bg for dark mode) */
        --success-100: 175 77% 19%;        /* Bright-Turquoise 900 (darker bg for dark mode) */
        --destructive-200: 1 80% 35%;      /* Cinnabar 900 (darker bg for dark mode) */
    }
.container {
  width: 100%;
}
@media (min-width: 320px) {

  .container {
    max-width: 320px;
  }
}
@media (min-width: 375px) {

  .container {
    max-width: 375px;
  }
}
@media (min-width: 414px) {

  .container {
    max-width: 414px;
  }
}
@media (min-width: 480px) {

  .container {
    max-width: 480px;
  }
}
@media (min-width: 600px) {

  .container {
    max-width: 600px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 834px) {

  .container {
    max-width: 834px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-x-0 {
  left: 0px;
  right: 0px;
}
.inset-x-5 {
  left: 1.25rem;
  right: 1.25rem;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.inset-y-1 {
  top: 0.25rem;
  bottom: 0.25rem;
}
.inset-y-\[31px\] {
  top: 31px;
  bottom: 31px;
}
.-left-5 {
  left: -1.25rem;
}
.-left-\[1px\] {
  left: -1px;
}
.-left-\[21px\] {
  left: -21px;
}
.-left-\[41px\] {
  left: -41px;
}
.-right-1 {
  right: -0.25rem;
}
.-right-2\.5 {
  right: -0.625rem;
}
.-right-4 {
  right: -1rem;
}
.-right-\[21px\] {
  right: -21px;
}
.-top-1 {
  top: -0.25rem;
}
.-top-1\.5 {
  top: -0.375rem;
}
.-top-2\.5 {
  top: -0.625rem;
}
.-top-\[21px\] {
  top: -21px;
}
.-top-\[42\%\] {
  top: -42%;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-1 {
  bottom: 0.25rem;
}
.bottom-10 {
  bottom: 2.5rem;
}
.bottom-12 {
  bottom: 3rem;
}
.bottom-2 {
  bottom: 0.5rem;
}
.bottom-20 {
  bottom: 5rem;
}
.bottom-7 {
  bottom: 1.75rem;
}
.left-0 {
  left: 0px;
}
.left-1 {
  left: 0.25rem;
}
.left-1\/2 {
  left: 50%;
}
.left-1\/3 {
  left: 33.333333%;
}
.left-12 {
  left: 3rem;
}
.left-2 {
  left: 0.5rem;
}
.left-4 {
  left: 1rem;
}
.left-\[18px\] {
  left: 18px;
}
.left-\[50\%\] {
  left: 50%;
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-\[0\.2rem\] {
  right: 0.2rem;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\.5 {
  top: 0.375rem;
}
.top-1\/2 {
  top: 50%;
}
.top-2 {
  top: 0.5rem;
}
.top-2\/4 {
  top: 50%;
}
.top-3 {
  top: 0.75rem;
}
.top-3\.5 {
  top: 0.875rem;
}
.top-4 {
  top: 1rem;
}
.top-\[34px\] {
  top: 34px;
}
.top-\[38svh\] {
  top: 38svh;
}
.top-\[50\%\] {
  top: 50%;
}
.-z-10 {
  z-index: -10;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-100 {
  z-index: 100;
}
.z-20 {
  z-index: 20;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[15001\] {
  z-index: 15001;
}
.z-\[15002\] {
  z-index: 15002;
}
.z-\[5\] {
  z-index: 5;
}
.z-\[60\] {
  z-index: 60;
}
.z-\[9999\] {
  z-index: 9999;
}
.col-auto {
  grid-column: auto;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-3 {
  grid-column: span 3 / span 3;
}
.col-span-4 {
  grid-column: span 4 / span 4;
}
.col-span-full {
  grid-column: 1 / -1;
}
.col-end-6 {
  grid-column-end: 6;
}
.-m-6 {
  margin: -1.5rem;
}
.m-0 {
  margin: 0px;
}
.m-1 {
  margin: 0.25rem;
}
.m-4 {
  margin: 1rem;
}
.m-\[1px\] {
  margin: 1px;
}
.m-auto {
  margin: auto;
}
.m-px {
  margin: 1px;
}
.\!my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-2\.5 {
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}
.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.my-0\.5 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.\!mr-0 {
  margin-right: 0px !important;
}
.\!mr-1\.5 {
  margin-right: 0.375rem !important;
}
.\!mt-0 {
  margin-top: 0px !important;
}
.-mb-1 {
  margin-bottom: -0.25rem;
}
.-ml-0\.5 {
  margin-left: -0.125rem;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-ml-1\.5 {
  margin-left: -0.375rem;
}
.-mt-2 {
  margin-top: -0.5rem;
}
.-mt-20 {
  margin-top: -5rem;
}
.-mt-4 {
  margin-top: -1rem;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-0\.5 {
  margin-bottom: 0.125rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-2\.5 {
  margin-bottom: 0.625rem;
}
.mb-20 {
  margin-bottom: 5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.mb-\[5px\] {
  margin-bottom: 5px;
}
.mb-\[7px\] {
  margin-bottom: 7px;
}
.me-4 {
  margin-inline-end: 1rem;
}
.ml-0\.5 {
  margin-left: 0.125rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-2\.5 {
  margin-left: 0.625rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-\[1\.5rem\] {
  margin-left: 1.5rem;
}
.ml-\[1px\] {
  margin-left: 1px;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-1\.5 {
  margin-right: 0.375rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mr-8 {
  margin-right: 2rem;
}
.mr-\[8\.5px\] {
  margin-right: 8.5px;
}
.ms-2 {
  margin-inline-start: 0.5rem;
}
.ms-3 {
  margin-inline-start: 0.75rem;
}
.mt-0 {
  margin-top: 0px;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-2\.5 {
  margin-top: 0.625rem;
}
.mt-24 {
  margin-top: 6rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-9 {
  margin-top: 2.25rem;
}
.mt-\[31px\] {
  margin-top: 31px;
}
.mt-\[7px\] {
  margin-top: 7px;
}
.mt-auto {
  margin-top: auto;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.\!table {
  display: table !important;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.\!hidden {
  display: none !important;
}
.hidden {
  display: none;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.size-10 {
  width: 2.5rem;
  height: 2.5rem;
}
.size-11 {
  width: 2.75rem;
  height: 2.75rem;
}
.size-12 {
  width: 3rem;
  height: 3rem;
}
.size-16 {
  width: 4rem;
  height: 4rem;
}
.size-2\.5 {
  width: 0.625rem;
  height: 0.625rem;
}
.size-3 {
  width: 0.75rem;
  height: 0.75rem;
}
.size-4 {
  width: 1rem;
  height: 1rem;
}
.size-5 {
  width: 1.25rem;
  height: 1.25rem;
}
.size-6 {
  width: 1.5rem;
  height: 1.5rem;
}
.size-7 {
  width: 1.75rem;
  height: 1.75rem;
}
.size-8 {
  width: 2rem;
  height: 2rem;
}
.size-9 {
  width: 2.25rem;
  height: 2.25rem;
}
.size-\[--cell-size\] {
  width: var(--cell-size);
  height: var(--cell-size);
}
.size-\[11px\] {
  width: 11px;
  height: 11px;
}
.size-\[125px\] {
  width: 125px;
  height: 125px;
}
.size-\[25px\] {
  width: 25px;
  height: 25px;
}
.size-\[42px\] {
  width: 42px;
  height: 42px;
}
.size-\[5px\] {
  width: 5px;
  height: 5px;
}
.size-fit {
  width: -moz-fit-content;
  width: fit-content;
  height: -moz-fit-content;
  height: fit-content;
}
.size-full {
  width: 100%;
  height: 100%;
}
.\!h-\[250px\] {
  height: 250px !important;
}
.\!h-full {
  height: 100% !important;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-56 {
  height: 14rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-96 {
  height: 24rem;
}
.h-\[--cell-size\] {
  height: var(--cell-size);
}
.h-\[100cvh\] {
  height: 100cvh;
}
.h-\[120px\] {
  height: 120px;
}
.h-\[14px\] {
  height: 14px;
}
.h-\[179\.605px\] {
  height: 179.605px;
}
.h-\[1px\] {
  height: 1px;
}
.h-\[200px\] {
  height: 200px;
}
.h-\[250px\] {
  height: 250px;
}
.h-\[33px\] {
  height: 33px;
}
.h-\[43px\] {
  height: 43px;
}
.h-\[50px\] {
  height: 50px;
}
.h-\[60svh\] {
  height: 60svh;
}
.h-\[61px\] {
  height: 61px;
}
.h-\[800px\] {
  height: 800px;
}
.h-\[80vh\] {
  height: 80vh;
}
.h-\[85vh\] {
  height: 85vh;
}
.h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}
.h-auto {
  height: auto;
}
.h-fit {
  height: -moz-fit-content;
  height: fit-content;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.h-svh {
  height: 100svh;
}
.max-h-11 {
  max-height: 2.75rem;
}
.max-h-48 {
  max-height: 12rem;
}
.max-h-64 {
  max-height: 16rem;
}
.max-h-80 {
  max-height: 20rem;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[200px\] {
  max-height: 200px;
}
.max-h-\[250px\] {
  max-height: 250px;
}
.max-h-\[300px\] {
  max-height: 300px;
}
.max-h-\[320px\] {
  max-height: 320px;
}
.max-h-\[400px\] {
  max-height: 400px;
}
.max-h-\[43px\] {
  max-height: 43px;
}
.max-h-\[58svh\] {
  max-height: 58svh;
}
.max-h-\[60svh\] {
  max-height: 60svh;
}
.max-h-\[90svh\] {
  max-height: 90svh;
}
.max-h-full {
  max-height: 100%;
}
.max-h-screen {
  max-height: 100vh;
}
.max-h-svh {
  max-height: 100svh;
}
.min-h-0 {
  min-height: 0px;
}
.min-h-16 {
  min-height: 4rem;
}
.min-h-20 {
  min-height: 5rem;
}
.min-h-24 {
  min-height: 6rem;
}
.min-h-60 {
  min-height: 15rem;
}
.min-h-\[10rem\] {
  min-height: 10rem;
}
.min-h-\[120px\] {
  min-height: 120px;
}
.min-h-\[150px\] {
  min-height: 150px;
}
.min-h-\[172px\] {
  min-height: 172px;
}
.min-h-\[2240px\] {
  min-height: 2240px;
}
.min-h-\[400px\] {
  min-height: 400px;
}
.min-h-\[43px\] {
  min-height: 43px;
}
.min-h-full {
  min-height: 100%;
}
.min-h-screen {
  min-height: 100vh;
}
.min-h-svh {
  min-height: 100svh;
}
.\!w-64 {
  width: 16rem !important;
}
.\!w-\[100px\] {
  width: 100px !important;
}
.\!w-\[100pxfull\] {
  width: 100pxfull !important;
}
.\!w-\[75px\] {
  width: 75px !important;
}
.\!w-auto {
  width: auto !important;
}
.\!w-full {
  width: 100% !important;
}
.w-0 {
  width: 0px;
}
.w-1 {
  width: 0.25rem;
}
.w-1\/2 {
  width: 50%;
}
.w-1\/4 {
  width: 25%;
}
.w-1\/5 {
  width: 20%;
}
.w-10 {
  width: 2.5rem;
}
.w-11 {
  width: 2.75rem;
}
.w-11\/12 {
  width: 91.666667%;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-96 {
  width: 24rem;
}
.w-\[--cell-size\] {
  width: var(--cell-size);
}
.w-\[--radix-dropdown-menu-trigger-width\] {
  width: var(--radix-dropdown-menu-trigger-width);
}
.w-\[--radix-popover-trigger-width\] {
  width: var(--radix-popover-trigger-width);
}
.w-\[--sidebar-width\] {
  width: var(--sidebar-width);
}
.w-\[10\%\] {
  width: 10%;
}
.w-\[14px\] {
  width: 14px;
}
.w-\[15\%\] {
  width: 15%;
}
.w-\[150px\] {
  width: 150px;
}
.w-\[179\.605px\] {
  width: 179.605px;
}
.w-\[180px\] {
  width: 180px;
}
.w-\[1px\] {
  width: 1px;
}
.w-\[20\%\] {
  width: 20%;
}
.w-\[233px\] {
  width: 233px;
}
.w-\[25\%\] {
  width: 25%;
}
.w-\[250px\] {
  width: 250px;
}
.w-\[256px\] {
  width: 256px;
}
.w-\[400px\] {
  width: 400px;
}
.w-\[48px\] {
  width: 48px;
}
.w-\[4rem\] {
  width: 4rem;
}
.w-\[60\%\] {
  width: 60%;
}
.w-\[60vw\] {
  width: 60vw;
}
.w-\[7\%\] {
  width: 7%;
}
.w-\[70px\] {
  width: 70px;
}
.w-\[90\%\] {
  width: 90%;
}
.w-\[90vw\] {
  width: 90vw;
}
.w-\[95vw\] {
  width: 95vw;
}
.w-auto {
  width: auto;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-max {
  width: -moz-max-content;
  width: max-content;
}
.\!min-w-48 {
  min-width: 12rem !important;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-16 {
  min-width: 4rem;
}
.min-w-28 {
  min-width: 7rem;
}
.min-w-32 {
  min-width: 8rem;
}
.min-w-48 {
  min-width: 12rem;
}
.min-w-5 {
  min-width: 1.25rem;
}
.min-w-52 {
  min-width: 13rem;
}
.min-w-60 {
  min-width: 15rem;
}
.min-w-7 {
  min-width: 1.75rem;
}
.min-w-\[--cell-size\] {
  min-width: var(--cell-size);
}
.min-w-\[150px\] {
  min-width: 150px;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-\[var\(--radix-popover-trigger-width\)\] {
  min-width: var(--radix-popover-trigger-width);
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}
.min-w-fit {
  min-width: -moz-fit-content;
  min-width: fit-content;
}
.min-w-min {
  min-width: -moz-min-content;
  min-width: min-content;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-32 {
  max-width: 8rem;
}
.max-w-40 {
  max-width: 10rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-\[--skeleton-width\] {
  max-width: var(--skeleton-width);
}
.max-w-\[150px\] {
  max-width: 150px;
}
.max-w-\[200px\] {
  max-width: 200px;
}
.max-w-\[25rem\] {
  max-width: 25rem;
}
.max-w-\[40rem\] {
  max-width: 40rem;
}
.max-w-\[90\%\] {
  max-width: 90%;
}
.max-w-\[90vw\] {
  max-width: 90vw;
}
.max-w-\[calc\(100\%-12rem\)\] {
  max-width: calc(100% - 12rem);
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-none {
  flex: none;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.grow {
  flex-grow: 1;
}
.grow-0 {
  flex-grow: 0;
}
.table-auto {
  table-layout: auto;
}
.caption-bottom {
  caption-side: bottom;
}
.border-collapse {
  border-collapse: collapse;
}
.border-separate {
  border-collapse: separate;
}
.border-spacing-0 {
  --tw-border-spacing-x: 0px;
  --tw-border-spacing-y: 0px;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
}
.border-spacing-y-2 {
  --tw-border-spacing-y: 0.5rem;
  border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
}
.origin-bottom-left {
  transform-origin: bottom left;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-2\.5 {
  --tw-translate-x: -0.625rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-2\/4 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-px {
  --tw-translate-x: -1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1 {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-2\/4 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-\[17\.5px\] {
  --tw-translate-y: -17.5px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\] {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-px {
  --tw-translate-x: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1 {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\] {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-\[45deg\] {
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-\[75deg\] {
  --tw-rotate: -75deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[135deg\] {
  --tw-rotate: 135deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[180deg\] {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[225deg\] {
  --tw-rotate: 225deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[90deg\] {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-gpu {
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.touch-none {
  touch-action: none;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.select-all {
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all;
}
.resize-none {
  resize: none;
}
.resize-y {
  resize: vertical;
}
.resize {
  resize: both;
}
.scroll-m-20 {
  scroll-margin: 5rem;
}
.list-decimal {
  list-style-type: decimal;
}
.list-disc {
  list-style-type: disc;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-baseline {
  align-items: baseline;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-evenly {
  justify-content: space-evenly;
}
.justify-items-start {
  justify-items: start;
}
.justify-items-end {
  justify-items: end;
}
.justify-items-center {
  justify-items: center;
}
.gap-0 {
  gap: 0px;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-2\.5 {
  gap: 0.625rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-36 {
  gap: 9rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-\[11px\] {
  gap: 11px;
}
.gap-\[31px\] {
  gap: 31px;
}
.gap-\[5px\] {
  gap: 5px;
}
.gap-\[8\.5px\] {
  gap: 8.5px;
}
.gap-\[8px\] {
  gap: 8px;
}
.gap-x-2\.5 {
  -moz-column-gap: 0.625rem;
       column-gap: 0.625rem;
}
.gap-x-3 {
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}
.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}
.gap-x-5 {
  -moz-column-gap: 1.25rem;
       column-gap: 1.25rem;
}
.gap-y-5 {
  row-gap: 1.25rem;
}
.gap-y-8 {
  row-gap: 2rem;
}
.-space-x-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0px * var(--tw-space-x-reverse));
  margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.625rem * var(--tw-space-x-reverse));
  margin-left: calc(0.625rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-2\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.space-y-\[7px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(7px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(7px * var(--tw-space-y-reverse));
}
.space-y-\[96px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(96px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(96px * var(--tw-space-y-reverse));
}
.justify-self-start {
  justify-self: start;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-visible {
  overflow: visible;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-normal {
  white-space: normal;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-line {
  white-space: pre-line;
}
.text-wrap {
  text-wrap: wrap;
}
.text-nowrap {
  text-wrap: nowrap;
}
.break-words {
  overflow-wrap: break-word;
}
.break-all {
  word-break: break-all;
}
.\!rounded-full {
  border-radius: 9999px !important;
}
.\!rounded-lg {
  border-radius: 0.5rem !important;
}
.\!rounded-md {
  border-radius: 0.375rem !important;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-\[2px\] {
  border-radius: 2px;
}
.rounded-\[4px\] {
  border-radius: 4px;
}
.rounded-\[6px\] {
  border-radius: 6px;
}
.rounded-\[inherit\] {
  border-radius: inherit;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-none {
  border-radius: 0px;
}
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b-md {
  border-bottom-right-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.rounded-e-lg {
  border-start-end-radius: 0.5rem;
  border-end-end-radius: 0.5rem;
}
.rounded-l-\[6px\] {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}
.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.rounded-l-xl {
  border-top-left-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rounded-r-\[6px\] {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}
.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
.rounded-r-xl {
  border-top-right-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}
.rounded-s-lg {
  border-start-start-radius: 0.5rem;
  border-end-start-radius: 0.5rem;
}
.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.rounded-t-md {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}
.rounded-bl-md {
  border-bottom-left-radius: 0.375rem;
}
.rounded-se-md {
  border-start-end-radius: 0.375rem;
}
.rounded-ss-md {
  border-start-start-radius: 0.375rem;
}
.rounded-tl-lg {
  border-top-left-radius: 0.5rem;
}
.rounded-tl-none {
  border-top-left-radius: 0px;
}
.rounded-tl-xl {
  border-top-left-radius: 0.75rem;
}
.rounded-tr-lg {
  border-top-right-radius: 0.5rem;
}
.rounded-tr-md {
  border-top-right-radius: 0.375rem;
}
.\!border {
  border-width: 1px !important;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-\[1\.5px\] {
  border-width: 1.5px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-0 {
  border-bottom-width: 0px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-0 {
  border-left-width: 0px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-0 {
  border-right-width: 0px;
}
.border-t {
  border-top-width: 1px;
}
.border-solid {
  border-style: solid;
}
.border-dashed {
  border-style: dashed;
}
.border-none {
  border-style: none;
}
.border-\[--color-border\] {
  border-color: var(--color-border);
}
.border-border {
  --tw-border-opacity: 1;
  border-color: hsl(var(--border) / var(--tw-border-opacity, 1));
}
.border-bright-turquoise-600 {
  --tw-border-opacity: 1;
  border-color: hsl(175 100% 32% / var(--tw-border-opacity, 1));
}
.border-card\/0 {
  border-color: hsl(var(--card) / 0);
}
.border-curious-blue-100 {
  --tw-border-opacity: 1;
  border-color: hsl(210 81% 94% / var(--tw-border-opacity, 1));
}
.border-curious-blue-400 {
  --tw-border-opacity: 1;
  border-color: hsl(205 82% 54% / var(--tw-border-opacity, 1));
}
.border-destructive {
  --tw-border-opacity: 1;
  border-color: hsl(var(--destructive) / var(--tw-border-opacity, 1));
}
.border-destructive\/50 {
  border-color: hsl(var(--destructive) / 0.5);
}
.border-fire-bush-500 {
  --tw-border-opacity: 1;
  border-color: hsl(30 80% 50% / var(--tw-border-opacity, 1));
}
.border-fire-bush-600 {
  --tw-border-opacity: 1;
  border-color: hsl(25 81% 44% / var(--tw-border-opacity, 1));
}
.border-fire-bush-700 {
  --tw-border-opacity: 1;
  border-color: hsl(19 78% 37% / var(--tw-border-opacity, 1));
}
.border-input {
  --tw-border-opacity: 1;
  border-color: hsl(var(--input) / var(--tw-border-opacity, 1));
}
.border-outer-space-400 {
  --tw-border-opacity: 1;
  border-color: hsl(201 11% 53% / var(--tw-border-opacity, 1));
}
.border-primary {
  --tw-border-opacity: 1;
  border-color: hsl(var(--primary) / var(--tw-border-opacity, 1));
}
.border-primary\/20 {
  border-color: hsl(var(--primary) / 0.2);
}
.border-primary\/50 {
  border-color: hsl(var(--primary) / 0.5);
}
.border-secondary {
  --tw-border-opacity: 1;
  border-color: hsl(var(--secondary) / var(--tw-border-opacity, 1));
}
.border-sidebar-border {
  --tw-border-opacity: 1;
  border-color: hsl(var(--sidebar-border) / var(--tw-border-opacity, 1));
}
.border-wedgewood-100 {
  --tw-border-opacity: 1;
  border-color: hsl(210 31% 94% / var(--tw-border-opacity, 1));
}
.border-wedgewood-200 {
  --tw-border-opacity: 1;
  border-color: hsl(209 35% 86% / var(--tw-border-opacity, 1));
}
.border-curious-blue-500 {
  --tw-border-opacity: 1;
  border-color: hsl(205 78% 48% / var(--tw-border-opacity, 1));
}
.border-l-curious-blue-600 {
  --tw-border-opacity: 1;
  border-left-color: hsl(207 86% 39% / var(--tw-border-opacity, 1));
}
.border-l-fire-bush-600 {
  --tw-border-opacity: 1;
  border-left-color: hsl(25 81% 44% / var(--tw-border-opacity, 1));
}
.border-r-border {
  --tw-border-opacity: 1;
  border-right-color: hsl(var(--border) / var(--tw-border-opacity, 1));
}
.bg-\[\#fff\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-\[--color-bg\] {
  background-color: var(--color-bg);
}
.bg-\[white\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-accent {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent) / var(--tw-bg-opacity, 1));
}
.bg-accent\/0 {
  background-color: hsl(var(--accent) / 0);
}
.bg-accent\/50 {
  background-color: hsl(var(--accent) / 0.5);
}
.bg-background {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--background) / var(--tw-bg-opacity, 1));
}
.bg-background\/50 {
  background-color: hsl(var(--background) / 0.5);
}
.bg-border {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--border) / var(--tw-bg-opacity, 1));
}
.bg-bright-turquoise-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(168 100% 89% / var(--tw-bg-opacity, 1));
}
.bg-bright-turquoise-600 {
  --tw-bg-opacity: 1;
  background-color: hsl(175 100% 32% / var(--tw-bg-opacity, 1));
}
.bg-card {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--card) / var(--tw-bg-opacity, 1));
}
.bg-card\/0 {
  background-color: hsl(var(--card) / 0);
}
.bg-cinnabar-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(2 100% 94% / var(--tw-bg-opacity, 1));
}
.bg-cinnabar-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(1 100% 89% / var(--tw-bg-opacity, 1));
}
.bg-cinnabar-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(0 100% 97% / var(--tw-bg-opacity, 1));
}
.bg-curious-blue-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(210 81% 94% / var(--tw-bg-opacity, 1));
}
.bg-curious-blue-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(207 83% 86% / var(--tw-bg-opacity, 1));
}
.bg-curious-blue-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(208 87% 97% / var(--tw-bg-opacity, 1));
}
.bg-curious-blue-600 {
  --tw-bg-opacity: 1;
  background-color: hsl(207 86% 39% / var(--tw-bg-opacity, 1));
}
.bg-curious-blue-950 {
  --tw-bg-opacity: 1;
  background-color: hsl(212 70% 16% / var(--tw-bg-opacity, 1));
}
.bg-destructive {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--destructive) / var(--tw-bg-opacity, 1));
}
.bg-destructive-foreground {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--destructive-foreground) / var(--tw-bg-opacity, 1));
}
.bg-destructive\/10 {
  background-color: hsl(var(--destructive) / 0.1);
}
.bg-fire-bush-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(41 82% 89% / var(--tw-bg-opacity, 1));
}
.bg-fire-bush-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(40 90% 96% / var(--tw-bg-opacity, 1));
}
.bg-fire-bush-600 {
  --tw-bg-opacity: 1;
  background-color: hsl(25 81% 44% / var(--tw-bg-opacity, 1));
}
.bg-muted {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--muted) / var(--tw-bg-opacity, 1));
}
.bg-muted\/20 {
  background-color: hsl(var(--muted) / 0.2);
}
.bg-muted\/30 {
  background-color: hsl(var(--muted) / 0.3);
}
.bg-muted\/50 {
  background-color: hsl(var(--muted) / 0.5);
}
.bg-muted\/80 {
  background-color: hsl(var(--muted) / 0.8);
}
.bg-neutral-400 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--neutral-400) / var(--tw-bg-opacity, 1));
}
.bg-outer-space-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(206 14% 90% / var(--tw-bg-opacity, 1));
}
.bg-outer-space-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(200 16% 96% / var(--tw-bg-opacity, 1));
}
.bg-popover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--popover) / var(--tw-bg-opacity, 1));
}
.bg-primary {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary) / var(--tw-bg-opacity, 1));
}
.bg-primary-foreground {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary-foreground) / var(--tw-bg-opacity, 1));
}
.bg-primary\/10 {
  background-color: hsl(var(--primary) / 0.1);
}
.bg-primary\/50 {
  background-color: hsl(var(--primary) / 0.5);
}
.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--secondary) / var(--tw-bg-opacity, 1));
}
.bg-secondary-foreground {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--secondary-foreground) / var(--tw-bg-opacity, 1));
}
.bg-secondary\/40 {
  background-color: hsl(var(--secondary) / 0.4);
}
.bg-sidebar {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--sidebar) / var(--tw-bg-opacity, 1));
}
.bg-sidebar-border {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--sidebar-border) / var(--tw-bg-opacity, 1));
}
.bg-warning\/10 {
  background-color: hsl(var(--warning) / 0.1);
}
.bg-wedgewood-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(216 33% 97% / var(--tw-bg-opacity, 1));
}
.\!bg-curious-blue-50 {
  --tw-bg-opacity: 1 !important;
  background-color: hsl(208 87% 97% / var(--tw-bg-opacity, 1)) !important;
}
.bg-cover {
  background-size: cover;
}
.bg-center {
  background-position: center;
}
.fill-bright-turquoise-100 {
  fill: hsl(168, 100%, 89%);
}
.fill-curious-blue-50 {
  fill: hsl(208, 87%, 97%);
}
.fill-muted-foreground {
  fill: hsl(var(--muted-foreground) / 1);
}
.stroke-bright-turquoise-600 {
  stroke: hsl(175, 100%, 32%);
}
.stroke-\[1\.5\] {
  stroke-width: 1.5;
}
.stroke-\[2px\] {
  stroke-width: 2px;
}
.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.\!p-0 {
  padding: 0px !important;
}
.\!p-2 {
  padding: 0.5rem !important;
}
.p-0 {
  padding: 0px;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.p-\[1px\] {
  padding: 1px;
}
.p-px {
  padding: 1px;
}
.\!px-0 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}
.\!py-0 {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}
.\!py-0\.5 {
  padding-top: 0.125rem !important;
  padding-bottom: 0.125rem !important;
}
.\!py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}
.px-\[--cell-size\] {
  padding-left: var(--cell-size);
  padding-right: var(--cell-size);
}
.px-\[18px\] {
  padding-left: 18px;
  padding-right: 18px;
}
.px-\[26px\] {
  padding-left: 26px;
  padding-right: 26px;
}
.px-\[47px\] {
  padding-left: 47px;
  padding-right: 47px;
}
.px-\[5px\] {
  padding-left: 5px;
  padding-right: 5px;
}
.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-\[10\.5px\] {
  padding-top: 10.5px;
  padding-bottom: 10.5px;
}
.py-\[21px\] {
  padding-top: 21px;
  padding-bottom: 21px;
}
.py-\[6px\] {
  padding-top: 6px;
  padding-bottom: 6px;
}
.py-\[9px\] {
  padding-top: 9px;
  padding-bottom: 9px;
}
.py-px {
  padding-top: 1px;
  padding-bottom: 1px;
}
.\!pb-5 {
  padding-bottom: 1.25rem !important;
}
.\!pr-1 {
  padding-right: 0.25rem !important;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-1\.5 {
  padding-bottom: 0.375rem;
}
.pb-10 {
  padding-bottom: 2.5rem;
}
.pb-16 {
  padding-bottom: 4rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-2\.5 {
  padding-bottom: 0.625rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-5 {
  padding-bottom: 1.25rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pb-8 {
  padding-bottom: 2rem;
}
.pb-\[23px\] {
  padding-bottom: 23px;
}
.pe-2 {
  padding-inline-end: 0.5rem;
}
.pe-4 {
  padding-inline-end: 1rem;
}
.pl-1 {
  padding-left: 0.25rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-2\.5 {
  padding-left: 0.625rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-5 {
  padding-left: 1.25rem;
}
.pl-6 {
  padding-left: 1.5rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pl-\[5px\] {
  padding-left: 5px;
}
.pr-0 {
  padding-right: 0px;
}
.pr-1 {
  padding-right: 0.25rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-2\.5 {
  padding-right: 0.625rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-6 {
  padding-right: 1.5rem;
}
.pr-8 {
  padding-right: 2rem;
}
.pr-px {
  padding-right: 1px;
}
.ps-2 {
  padding-inline-start: 0.5rem;
}
.ps-2\.5 {
  padding-inline-start: 0.625rem;
}
.ps-3 {
  padding-inline-start: 0.75rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-10 {
  padding-top: 2.5rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-2\.5 {
  padding-top: 0.625rem;
}
.pt-24 {
  padding-top: 6rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-5 {
  padding-top: 1.25rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-8 {
  padding-top: 2rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-start {
  text-align: start;
}
.align-top {
  vertical-align: top;
}
.align-middle {
  vertical-align: middle;
}
.align-bottom {
  vertical-align: bottom;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.\!text-sm {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-\[0\.8rem\] {
  font-size: 0.8rem;
}
.text-\[10px\] {
  font-size: 10px;
}
.text-\[12px\] {
  font-size: 12px;
}
.text-\[14px\] {
  font-size: 14px;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-black {
  font-weight: 900;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.font-thin {
  font-weight: 100;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.capitalize {
  text-transform: capitalize;
}
.normal-case {
  text-transform: none;
}
.italic {
  font-style: italic;
}
.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.leading-4 {
  line-height: 1rem;
}
.leading-5 {
  line-height: 1.25rem;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-7 {
  line-height: 1.75rem;
}
.leading-\[36px\] {
  line-height: 36px;
}
.leading-loose {
  line-height: 2;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-\[-0\.09px\] {
  letter-spacing: -0.09px;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.text-accent-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--accent-foreground) / var(--tw-text-opacity, 1));
}
.text-background {
  --tw-text-opacity: 1;
  color: hsl(var(--background) / var(--tw-text-opacity, 1));
}
.text-bright-turquoise-600 {
  --tw-text-opacity: 1;
  color: hsl(175 100% 32% / var(--tw-text-opacity, 1));
}
.text-card-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--card-foreground) / var(--tw-text-opacity, 1));
}
.text-card-foreground\/80 {
  color: hsl(var(--card-foreground) / 0.8);
}
.text-cinnabar-500 {
  --tw-text-opacity: 1;
  color: hsl(1 97% 60% / var(--tw-text-opacity, 1));
}
.text-cinnabar-600 {
  --tw-text-opacity: 1;
  color: hsl(1 83% 54% / var(--tw-text-opacity, 1));
}
.text-cinnabar-700 {
  --tw-text-opacity: 1;
  color: hsl(1 85% 42% / var(--tw-text-opacity, 1));
}
.text-curious-blue-400 {
  --tw-text-opacity: 1;
  color: hsl(205 82% 54% / var(--tw-text-opacity, 1));
}
.text-curious-blue-600 {
  --tw-text-opacity: 1;
  color: hsl(207 86% 39% / var(--tw-text-opacity, 1));
}
.text-curious-blue-800 {
  --tw-text-opacity: 1;
  color: hsl(208 79% 27% / var(--tw-text-opacity, 1));
}
.text-curious-blue-900 {
  --tw-text-opacity: 1;
  color: hsl(209 70% 24% / var(--tw-text-opacity, 1));
}
.text-curious-blue-950 {
  --tw-text-opacity: 1;
  color: hsl(212 70% 16% / var(--tw-text-opacity, 1));
}
.text-destructive {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive) / var(--tw-text-opacity, 1));
}
.text-destructive-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive-foreground) / var(--tw-text-opacity, 1));
}
.text-fire-bush-50 {
  --tw-text-opacity: 1;
  color: hsl(40 90% 96% / var(--tw-text-opacity, 1));
}
.text-fire-bush-500 {
  --tw-text-opacity: 1;
  color: hsl(30 80% 50% / var(--tw-text-opacity, 1));
}
.text-fire-bush-600 {
  --tw-text-opacity: 1;
  color: hsl(25 81% 44% / var(--tw-text-opacity, 1));
}
.text-fire-bush-700 {
  --tw-text-opacity: 1;
  color: hsl(19 78% 37% / var(--tw-text-opacity, 1));
}
.text-input {
  --tw-text-opacity: 1;
  color: hsl(var(--input) / var(--tw-text-opacity, 1));
}
.text-muted-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--muted-foreground) / var(--tw-text-opacity, 1));
}
.text-neutral-400 {
  --tw-text-opacity: 1;
  color: hsl(var(--neutral-400) / var(--tw-text-opacity, 1));
}
.text-outer-space-100 {
  --tw-text-opacity: 1;
  color: hsl(206 14% 90% / var(--tw-text-opacity, 1));
}
.text-outer-space-400 {
  --tw-text-opacity: 1;
  color: hsl(201 11% 53% / var(--tw-text-opacity, 1));
}
.text-outer-space-400\/50 {
  color: hsl(201 11% 53% / 0.5);
}
.text-outer-space-50 {
  --tw-text-opacity: 1;
  color: hsl(200 16% 96% / var(--tw-text-opacity, 1));
}
.text-outer-space-500 {
  --tw-text-opacity: 1;
  color: hsl(201 12% 43% / var(--tw-text-opacity, 1));
}
.text-outer-space-600 {
  --tw-text-opacity: 1;
  color: hsl(205 12% 36% / var(--tw-text-opacity, 1));
}
.text-popover-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--foreground) / var(--tw-text-opacity, 1));
}
.text-primary {
  --tw-text-opacity: 1;
  color: hsl(var(--primary) / var(--tw-text-opacity, 1));
}
.text-primary-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--primary-foreground) / var(--tw-text-opacity, 1));
}
.text-secondary {
  --tw-text-opacity: 1;
  color: hsl(var(--secondary) / var(--tw-text-opacity, 1));
}
.text-sidebar-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--sidebar-foreground) / var(--tw-text-opacity, 1));
}
.text-sidebar-foreground\/70 {
  color: hsl(var(--sidebar-foreground) / 0.7);
}
.text-warning {
  --tw-text-opacity: 1;
  color: hsl(var(--warning) / var(--tw-text-opacity, 1));
}
.text-warning\/80 {
  color: hsl(var(--warning) / 0.8);
}
.text-wedgewood-50 {
  --tw-text-opacity: 1;
  color: hsl(216 33% 97% / var(--tw-text-opacity, 1));
}
.text-curious-blue-500 {
  --tw-text-opacity: 1;
  color: hsl(205 78% 48% / var(--tw-text-opacity, 1));
}
.text-fire-bush-900 {
  --tw-text-opacity: 1;
  color: hsl(15 67% 26% / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.line-through {
  text-decoration-line: line-through;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.opacity-0 {
  opacity: 0;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-90 {
  opacity: 0.9;
}
.\!shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_12px_0\] {
  --tw-shadow: 0 0 12px 0;
  --tw-shadow-colored: 0 0 12px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_20px_0\] {
  --tw-shadow: 0 0 20px 0;
  --tw-shadow-colored: 0 0 20px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_1px_3px_rgba\(0\2c 0\2c 0\2c 0\.1\)\] {
  --tw-shadow: 0 1px 3px rgba(0,0,0,0.1);
  --tw-shadow-colored: 0 1px 3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_2px_0_\#FFFFFF33\] {
  --tw-shadow: 0 2px 0 #FFFFFF33;
  --tw-shadow-colored: 0 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_6px_hsla\(0\2c 0\%\2c 0\%\2c 0\.2\)\] {
  --tw-shadow: 0 4px 6px hsla(0,0%,0%,0.2);
  --tw-shadow-colored: 0 4px 6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0px_4px_6px_\#00000083\] {
  --tw-shadow: 0px 4px 6px #00000083;
  --tw-shadow-colored: 0px 4px 6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[inset_0_2px_4px_\#0000001A\] {
  --tw-shadow: inset 0 2px 4px #0000001A;
  --tw-shadow-colored: inset 0 2px 4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.outline-2 {
  outline-width: 2px;
}
.outline-outer-space-100 {
  outline-color: hsl(206, 14%, 90%);
}
.ring-0 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-inset {
  --tw-ring-inset: inset;
}
.ring-sidebar-ring {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--sidebar-ring) / var(--tw-ring-opacity, 1));
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[left\2c right\2c width\] {
  transition-property: left,right,width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[margin\2c opa\] {
  transition-property: margin,opa;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[transform\2c padding\] {
  transition-property: transform,padding;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\2c padding\] {
  transition-property: width,padding;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-\[width\] {
  transition-property: width;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  transition-timing-function: linear;
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.will-change-transform {
  will-change: transform;
}
@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.animate-in {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.animate-out {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.fade-in {
  --tw-enter-opacity: 0;
}
.fade-in-0 {
  --tw-enter-opacity: 0;
}
.fade-out {
  --tw-exit-opacity: 0;
}
.zoom-in-95 {
  --tw-enter-scale: .95;
}
.duration-200 {
  animation-duration: 200ms;
}
.duration-300 {
  animation-duration: 300ms;
}
.ease-in {
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
  animation-timing-function: linear;
}
.ease-out {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.running {
  animation-play-state: running;
}
.paused {
  animation-play-state: paused;
}
.\[--cell-size\:2\.5rem\] {
  --cell-size: 2.5rem;
}

@font-face {
    font-family: 'IBMPlexSans';
    src: url(/_next/static/media/IBMPlexSans-VariableFont_wdth,wght.b9dbc346.ttf);
}

@font-face {
    font-family: 'IBMPlexSans-italic';
    src: url(/_next/static/media/IBMPlexSans-Italic-VariableFont_wdth,wght.9ab1af88.ttf);
}

:focus,
:focus-visible {
    outline: none;
}

/* Applying global styles here */
.alert {
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: hsl(var(--destructive) / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: hsl(var(--destructive-foreground) / var(--tw-bg-opacity, 1));
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: hsl(var(--destructive) / var(--tw-text-opacity, 1));
}
.success {
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: hsl(175 100% 32% / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: hsl(168 100% 89% / var(--tw-bg-opacity, 1));
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: hsl(175 100% 32% / var(--tw-text-opacity, 1));
}
.icons {
  display: inline-block;
  height: 1.25rem;
  width: 1.25rem;
}
.chartToolTip {
  border-radius: 0.375rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: hsl(19 78% 37% / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: hsl(41 82% 89% / var(--tw-bg-opacity, 1));
  padding: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: hsl(19 78% 37% / var(--tw-text-opacity, 1));
}
.pointer-events-none {
    pointer-events: none;
}
.ql-container {
    border-radius: 0 0 0.5rem 0.5rem;
    font-size: 1rem !important;
    font-family: 'IBMPlexSans' !important;
}

.ql-toolbar {
    border-radius: 0.5rem 0.5rem 0 0;
    display: flex;
    background: hsl(208, 87%, 97%);
}

.ql-toolbar.ql-snow .ql-formats {
    display: flex;
}

.swiper-slide {
    width: auto !important;
}
.fc .fc-view-harness {
    height: 100% !important;
}
.fc .fc-toolbar-title {
    font-size: 1.5em;
    text-wrap: wrap;
}

.fc .fc-scroller-liquid {
    border-radius: 10px;
}

.fc .fc-view-harness-active > .fc-view {
    border-radius: 10px;
    position: relative;
}

.fc-theme-standard .fc-list {
    height: -moz-fit-content !important;
    height: fit-content !important;
}

.fc-toolbar-chunk {
    white-space: nowrap;
}

@media screen and (max-width: 400px) {
    .fc .fc-toolbar-title {
        font-size: 1em !important;
    }
}

ubdiv.userback-button.userback-button-e {
    display: none !important;
}

.ql-container.ql-snow {
    border: none !important;
}

.ql-editor.ql-blank::before {
    font-style: normal !important;
    color: hsl(var(--neutral-400)) !important;
}

.fc .fc-scroller-liquid-absolute,
.fc .fc-scroller-harness {
    overflow: visible !important;
}

/* FullCalendar Styling - Light & Dark Mode Optimized */

/* Calendar Container & Grid */
.fc {
    background-color: hsl(var(--card)) !important;
    color: hsl(var(--card-foreground)) !important;
    border-radius: var(--radius) !important;
    border: none !important;
}

.fc .fc-view-harness {
    background-color: hsl(var(--card)) !important;
}

.fc .fc-scrollgrid {
    border-color: hsl(var(--border)) !important;
}

.fc .fc-scrollgrid-section > * {
    border-color: hsl(var(--border)) !important;
}

.fc .fc-scrollgrid-sync-table {
    border-color: hsl(var(--border)) !important;
}

.fc .fc-view {
    border-color: hsl(var(--border)) !important;
}

.fc .fc-view-harness-active > .fc-view {
    border-color: hsl(var(--border)) !important;
}

/* Calendar Header */
.fc .fc-col-header-cell {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    border-color: hsl(var(--border)) !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

.fc .fc-toolbar-title {
    color: hsl(var(--foreground)) !important;
    font-weight: 700 !important;
    font-size: 1.25rem !important;
}

/* Calendar Days */
.fc .fc-daygrid-day {
    background-color: hsl(var(--card)) !important;
    border-color: hsl(var(--border)) !important;
}

.fc .fc-daygrid-day:hover {
    background-color: hsl(var(--accent)) !important;
}

.fc .fc-day-today {
    background-color: hsl(var(--accent)) !important;
}

.fc .fc-daygrid-day-number {
    color: hsl(var(--foreground)) !important;
    font-weight: 500 !important;
    font-size: 1rem !important;
}

/* Calendar Buttons */
.fc .fc-button-primary {
    background-color: hsl(var(--primary)) !important;
    border-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
}

.fc .fc-button-primary:hover {
    background-color: hsl(var(--primary)) !important;
    border-color: hsl(var(--primary)) !important;
    opacity: 0.9 !important;
}

.fc .fc-button-primary:focus {
    background-color: hsl(var(--primary)) !important;
    border-color: hsl(var(--ring)) !important;
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2) !important;
}

.fc .fc-button-primary:disabled {
    background-color: hsl(var(--primary)) !important;
    border-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    opacity: 0.6 !important;
}

.fc .fc-button-primary:not(:disabled):active,
.fc .fc-button-primary:not(:disabled).fc-button-active {
    background-color: hsl(var(--primary)) !important;
    border-color: hsl(var(--primary)) !important;
    opacity: 0.8 !important;
}

/* Event Styling by Type */
.fc-event {
    border-radius: var(--radius) !important;
    border-width: 1px !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    padding: 4px 8px !important;
    margin: 1px !important;
}

/* Event styling - neutral text, color coding via dots only */
.fc-event-task,
.fc-event-training-due,
.fc-event-training-completed,
.fc-event-logbook {
    background-color: hsl(var(--card)) !important;
    border-color: hsl(var(--border)) !important;
    color: hsl(var(--card-foreground)) !important;
}

.fc-event-task:hover,
.fc-event-training-due:hover,
.fc-event-training-completed:hover,
.fc-event-logbook:hover {
    background-color: hsl(var(--accent)) !important;
}

/* List View Styling */
.fc .fc-list-event {
    background-color: hsl(var(--card)) !important;
    border-color: hsl(var(--border)) !important;
    transition: background-color 0.15s ease !important;
}

.fc .fc-list-event:hover,
.fc .fc-list-table tbody tr:hover .fc-list-event {
    background-color: hsl(var(--accent)) !important;
}

/* Event type dots - color coding moved from text to dots */
.fc .fc-list-event-dot {
    border-color: hsl(var(--primary-foreground)) !important;
    background-color: hsl(var(--primary-foreground)) !important;
}

/* Task events - dot color */
.fc-event-task .fc-list-event-dot {
    border-color: hsl(var(--warning)) !important;
    background-color: hsl(var(--warning)) !important;
}

/* Training due events - dot color */
.fc-event-training-due .fc-list-event-dot {
    border-color: hsl(var(--destructive)) !important;
    background-color: hsl(var(--destructive)) !important;
}

/* Training completed events - dot color */
.fc-event-training-completed .fc-list-event-dot {
    border-color: hsl(var(--success)) !important;
    background-color: hsl(var(--success)) !important;
}

/* Log book entry events - dot color */
.fc-event-logbook .fc-list-event-dot {
    border-color: hsl(var(--primary)) !important;
    background-color: hsl(var(--primary)) !important;
}

.fc .fc-list-day-cushion {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    border-color: hsl(var(--border)) !important;
}

/* List table styling - consistent borders */
.fc .fc-list-table {
    border-color: hsl(var(--border)) !important;
}

.fc .fc-list-table th,
.fc .fc-list-table td {
    border-color: hsl(var(--border)) !important;
}

.fc .fc-list-table thead th {
    border-color: hsl(var(--border)) !important;
}

.fc .fc-list-table tbody td {
    border-color: hsl(var(--border)) !important;
}

/* List table row hover */
.fc .fc-list-table tbody tr:hover {
    background-color: hsl(var(--accent)) !important;
}

.fc .fc-list-table tbody tr:hover td {
    background-color: transparent !important;
}

/* More Events Link */
.fc .fc-more-link {
    color: hsl(var(--primary)) !important;
    font-weight: 500 !important;
}

.fc .fc-more-link:hover {
    color: hsl(var(--primary)) !important;
    opacity: 0.8 !important;
}

/* Event Text Styling */
.fc-event-title {
    font-weight: 500 !important;
    line-height: 1.4 !important;
}

/* Popover/Tooltip Styling */
.fc .fc-popover {
    background-color: hsl(var(--popover)) !important;
    border-color: hsl(var(--border)) !important;
    color: hsl(var(--popover-foreground)) !important;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
}

.fc .fc-popover-header {
    background-color: hsl(var(--primary)) !important;
    color: hsl(var(--primary-foreground)) !important;
    border-color: hsl(var(--border)) !important;
}

/* Week/Month View Specific */
.fc .fc-daygrid-week-number {
    color: hsl(var(--primary-foreground)) !important;
    background-color: hsl(var(--primary)) !important;
}

/* Time Grid (if used) */
.fc .fc-timegrid-slot {
    border-color: hsl(var(--border)) !important;
}

.fc .fc-timegrid-axis {
    color: hsl(var(--primary-foreground)) !important;
}

/* Scrollbar Styling for Dark Mode */
.fc .fc-scroller::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.fc .fc-scroller::-webkit-scrollbar-track {
    background: hsl(var(--primary));
    border-radius: 4px;
}

.fc .fc-scroller::-webkit-scrollbar-thumb {
    background: hsl(var(--primary-foreground));
    border-radius: 4px;
}

.fc .fc-scroller::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--foreground));
}

/* ──────────────────────────────────────────────────────────────
   GLOBAL COLOR TOKENS
   (adds success, warning, secondary, sidebar-accent / ring,
   and fills in previously blank values without touching any
   labelled slots or the pure-white card background)
──────────────────────────────────────────────────────────────── */
@media (min-width: 1280px) {

  .lg\:container {
    width: 100%;
  }

  @media (min-width: 320px) {

    .lg\:container {
      max-width: 320px;
    }
  }

  @media (min-width: 375px) {

    .lg\:container {
      max-width: 375px;
    }
  }

  @media (min-width: 414px) {

    .lg\:container {
      max-width: 414px;
    }
  }

  @media (min-width: 480px) {

    .lg\:container {
      max-width: 480px;
    }
  }

  @media (min-width: 600px) {

    .lg\:container {
      max-width: 600px;
    }
  }

  @media (min-width: 768px) {

    .lg\:container {
      max-width: 768px;
    }
  }

  @media (min-width: 834px) {

    .lg\:container {
      max-width: 834px;
    }
  }

  @media (min-width: 1024px) {

    .lg\:container {
      max-width: 1024px;
    }
  }

  @media (min-width: 1280px) {

    .lg\:container {
      max-width: 1280px;
    }
  }

  @media (min-width: 1536px) {

    .lg\:container {
      max-width: 1536px;
    }
  }
}
.file\:relative::file-selector-button {
  position: relative;
}
.file\:border-0::file-selector-button {
  border-width: 0px;
}
.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.file\:font-medium::file-selector-button {
  font-weight: 500;
}
.placeholder\:text-muted-foreground::-moz-placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--muted-foreground) / var(--tw-text-opacity, 1));
}
.placeholder\:text-muted-foreground::placeholder {
  --tw-text-opacity: 1;
  color: hsl(var(--muted-foreground) / var(--tw-text-opacity, 1));
}
.placeholder\:text-outer-space-400::-moz-placeholder {
  --tw-text-opacity: 1;
  color: hsl(201 11% 53% / var(--tw-text-opacity, 1));
}
.placeholder\:text-outer-space-400::placeholder {
  --tw-text-opacity: 1;
  color: hsl(201 11% 53% / var(--tw-text-opacity, 1));
}
.before\:absolute::before {
  content: var(--tw-content);
  position: absolute;
}
.before\:inset-0::before {
  content: var(--tw-content);
  inset: 0px;
}
.before\:left-2\/4::before {
  content: var(--tw-content);
  left: 50%;
}
.before\:top-2\/4::before {
  content: var(--tw-content);
  top: 50%;
}
.before\:block::before {
  content: var(--tw-content);
  display: block;
}
.before\:h-12::before {
  content: var(--tw-content);
  height: 3rem;
}
.before\:w-12::before {
  content: var(--tw-content);
  width: 3rem;
}
.before\:-translate-x-2\/4::before {
  content: var(--tw-content);
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.before\:-translate-x-full::before {
  content: var(--tw-content);
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.before\:-translate-y-2\/4::before {
  content: var(--tw-content);
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.before\:animate-\[shimmer_2s_infinite\]::before {
  content: var(--tw-content);
  animation: shimmer 2s infinite;
}
.before\:rounded-full::before {
  content: var(--tw-content);
  border-radius: 9999px;
}
.before\:bg-gradient-to-r::before {
  content: var(--tw-content);
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.before\:opacity-0::before {
  content: var(--tw-content);
  opacity: 0;
}
.before\:shadow-\[inset_0_2px_2px_\#0000001A\]::before {
  content: var(--tw-content);
  --tw-shadow: inset 0 2px 2px #0000001A;
  --tw-shadow-colored: inset 0 2px 2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.before\:transition-opacity::before {
  content: var(--tw-content);
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.before\:content-\[\"\"\]::before {
  --tw-content: "";
  content: var(--tw-content);
}
.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}
.after\:-inset-2::after {
  content: var(--tw-content);
  inset: -0.5rem;
}
.after\:inset-y-0::after {
  content: var(--tw-content);
  top: 0px;
  bottom: 0px;
}
.after\:left-1\/2::after {
  content: var(--tw-content);
  left: 50%;
}
.after\:start-\[2px\]::after {
  content: var(--tw-content);
  inset-inline-start: 2px;
}
.after\:top-\[2px\]::after {
  content: var(--tw-content);
  top: 2px;
}
.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}
.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}
.after\:w-\[2px\]::after {
  content: var(--tw-content);
  width: 2px;
}
.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}
.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}
.first\:block:first-child {
  display: block;
}
.last\:mb-0:last-child {
  margin-bottom: 0px;
}
.last\:border-b-0:last-child {
  border-bottom-width: 0px;
}
.even\:bg-muted\/20:nth-child(even) {
  background-color: hsl(var(--muted) / 0.2);
}
.hover\:w-\[233px\]:hover {
  width: 233px;
}
.hover\:border:hover {
  border-width: 1px;
}
.hover\:border-border:hover {
  --tw-border-opacity: 1;
  border-color: hsl(var(--border) / var(--tw-border-opacity, 1));
}
.hover\:border-curious-blue-400:hover {
  --tw-border-opacity: 1;
  border-color: hsl(205 82% 54% / var(--tw-border-opacity, 1));
}
.hover\:border-fire-bush-200:hover {
  --tw-border-opacity: 1;
  border-color: hsl(41 83% 77% / var(--tw-border-opacity, 1));
}
.hover\:border-input:hover {
  --tw-border-opacity: 1;
  border-color: hsl(var(--input) / var(--tw-border-opacity, 1));
}
.hover\:border-outer-space-400:hover {
  --tw-border-opacity: 1;
  border-color: hsl(201 11% 53% / var(--tw-border-opacity, 1));
}
.hover\:border-primary:hover {
  --tw-border-opacity: 1;
  border-color: hsl(var(--primary) / var(--tw-border-opacity, 1));
}
.hover\:bg-accent:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent) / var(--tw-bg-opacity, 1));
}
.hover\:bg-background:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--background) / var(--tw-bg-opacity, 1));
}
.hover\:bg-bright-turquoise-100:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(168 100% 89% / var(--tw-bg-opacity, 1));
}
.hover\:bg-card:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--card) / var(--tw-bg-opacity, 1));
}
.hover\:bg-cinnabar-200:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(1 100% 89% / var(--tw-bg-opacity, 1));
}
.hover\:bg-curious-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(208 85% 32% / var(--tw-bg-opacity, 1));
}
.hover\:bg-curious-blue-900:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(209 70% 24% / var(--tw-bg-opacity, 1));
}
.hover\:bg-destructive\/10:hover {
  background-color: hsl(var(--destructive) / 0.1);
}
.hover\:bg-fire-bush-100:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(41 82% 89% / var(--tw-bg-opacity, 1));
}
.hover\:bg-fire-bush-50:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(40 90% 96% / var(--tw-bg-opacity, 1));
}
.hover\:bg-fire-bush-800:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(15 71% 31% / var(--tw-bg-opacity, 1));
}
.hover\:bg-muted:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--muted) / var(--tw-bg-opacity, 1));
}
.hover\:bg-muted\/50:hover {
  background-color: hsl(var(--muted) / 0.5);
}
.hover\:bg-primary-foreground:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary-foreground) / var(--tw-bg-opacity, 1));
}
.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}
.hover\:px-\[6px\]:hover {
  padding-left: 6px;
  padding-right: 6px;
}
.hover\:text-accent-foreground:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--accent-foreground) / var(--tw-text-opacity, 1));
}
.hover\:text-cinnabar-800:hover {
  --tw-text-opacity: 1;
  color: hsl(1 80% 35% / var(--tw-text-opacity, 1));
}
.hover\:text-curious-blue-400:hover {
  --tw-text-opacity: 1;
  color: hsl(205 82% 54% / var(--tw-text-opacity, 1));
}
.hover\:text-destructive:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive) / var(--tw-text-opacity, 1));
}
.hover\:text-destructive\/80:hover {
  color: hsl(var(--destructive) / 0.8);
}
.hover\:text-fire-bush-500:hover {
  --tw-text-opacity: 1;
  color: hsl(30 80% 50% / var(--tw-text-opacity, 1));
}
.hover\:text-fire-bush-700:hover {
  --tw-text-opacity: 1;
  color: hsl(19 78% 37% / var(--tw-text-opacity, 1));
}
.hover\:text-fire-bush-800:hover {
  --tw-text-opacity: 1;
  color: hsl(15 71% 31% / var(--tw-text-opacity, 1));
}
.hover\:text-input:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--input) / var(--tw-text-opacity, 1));
}
.hover\:text-outer-space-400:hover {
  --tw-text-opacity: 1;
  color: hsl(201 11% 53% / var(--tw-text-opacity, 1));
}
.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--primary) / var(--tw-text-opacity, 1));
}
.hover\:text-sidebar-primary:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--sidebar-primary) / var(--tw-text-opacity, 1));
}
.hover\:text-warning:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--warning) / var(--tw-text-opacity, 1));
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:no-underline:hover {
  text-decoration-line: none;
}
.hover\:opacity-100:hover {
  opacity: 1;
}
.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:transition-all:hover {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.hover\:transition-colors:hover {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.hover\:duration-300:hover {
  transition-duration: 300ms;
}
.hover\:ease-out:hover {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.hover\:duration-300:hover {
  animation-duration: 300ms;
}
.hover\:ease-out:hover {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.hover\:before\:opacity-10:hover::before {
  content: var(--tw-content);
  opacity: 0.1;
}
.hover\:after\:bg-sidebar-border:hover::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--sidebar-border) / var(--tw-bg-opacity, 1));
}
.focus\:bg-accent:focus {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent) / var(--tw-bg-opacity, 1));
}
.focus\:bg-accent-foreground:focus {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-foreground) / var(--tw-bg-opacity, 1));
}
.focus\:bg-accent\/90:focus {
  background-color: hsl(var(--accent) / 0.9);
}
.focus\:bg-destructive\/5:focus {
  background-color: hsl(var(--destructive) / 0.05);
}
.focus\:bg-outer-space-50:focus {
  --tw-bg-opacity: 1;
  background-color: hsl(200 16% 96% / var(--tw-bg-opacity, 1));
}
.focus\:text-destructive:focus {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive) / var(--tw-text-opacity, 1));
}
.focus\:text-primary:focus {
  --tw-text-opacity: 1;
  color: hsl(var(--primary) / var(--tw-text-opacity, 1));
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-ring:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--ring) / var(--tw-ring-opacity, 1));
}
.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}
.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:outline:focus-visible {
  outline-style: solid;
}
.focus-visible\:outline-2:focus-visible {
  outline-width: 2px;
}
.focus-visible\:outline-offset-2:focus-visible {
  outline-offset: 2px;
}
.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-curious-blue-400:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(205 82% 54% / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-ring:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--ring) / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-sidebar-ring:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--sidebar-ring) / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}
.active\:bg-primary-foreground:active {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary-foreground) / var(--tw-bg-opacity, 1));
}
.active\:bg-sidebar-accent:active {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--sidebar-accent) / var(--tw-bg-opacity, 1));
}
.active\:text-sidebar-primary:active {
  --tw-text-opacity: 1;
  color: hsl(var(--sidebar-primary) / var(--tw-text-opacity, 1));
}
.active\:outline-none:active {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}
.disabled\:opacity-100:disabled {
  opacity: 1;
}
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}
.group\/menu-item:focus-within .group-focus-within\/menu-item\:opacity-100 {
  opacity: 1;
}
.group:hover .group-hover\:left-0 {
  left: 0px;
}
.group:hover .group-hover\:h-full {
  height: 100%;
}
.group:hover .group-hover\:w-\[233px\] {
  width: 233px;
}
.group:hover .group-hover\:w-full {
  width: 100%;
}
.group:hover .group-hover\:-translate-x-\[5px\] {
  --tw-translate-x: -5px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:border {
  border-width: 1px;
}
.group:hover .group-hover\:border-border {
  --tw-border-opacity: 1;
  border-color: hsl(var(--border) / var(--tw-border-opacity, 1));
}
.group:hover .group-hover\:border-primary {
  --tw-border-opacity: 1;
  border-color: hsl(var(--primary) / var(--tw-border-opacity, 1));
}
.group:hover .group-hover\:bg-accent {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent) / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-border {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--border) / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-bright-turquoise-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(170 100% 78% / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-cinnabar-300 {
  --tw-bg-opacity: 1;
  background-color: hsl(1 100% 81% / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-curious-blue-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(207 83% 86% / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-fire-bush-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(41 83% 77% / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-muted {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--muted) / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-muted\/30 {
  background-color: hsl(var(--muted) / 0.3);
}
.group:hover .group-hover\:bg-outer-space-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(200 16% 96% / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:px-\[6px\] {
  padding-left: 6px;
  padding-right: 6px;
}
.group:hover .group-hover\:text-curious-blue-400 {
  --tw-text-opacity: 1;
  color: hsl(205 82% 54% / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-curious-blue-400\/50 {
  color: hsl(205 82% 54% / 0.5);
}
.group:hover .group-hover\:text-destructive {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive) / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-outer-space-600 {
  --tw-text-opacity: 1;
  color: hsl(205 12% 36% / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-outer-space-800 {
  --tw-text-opacity: 1;
  color: hsl(210 9% 27% / var(--tw-text-opacity, 1));
}
.group\/crew:hover .group-hover\/crew\:underline {
  text-decoration-line: underline;
}
.group\/menu-item:hover .group-hover\/menu-item\:opacity-100 {
  opacity: 1;
}
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}
.group:hover .group-hover\:transition-\[height\2c color\] {
  transition-property: height,color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.group:hover .group-hover\:transition-\[width\2c left\] {
  transition-property: width,left;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.group:hover .group-hover\:transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.group:hover .group-hover\:duration-300 {
  transition-duration: 300ms;
}
.group:hover .group-hover\:ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.group:hover .group-hover\:duration-300 {
  animation-duration: 300ms;
}
.group:hover .group-hover\:ease-out {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.group.toaster .group-\[\.toaster\]\:border-border {
  --tw-border-opacity: 1;
  border-color: hsl(var(--border) / var(--tw-border-opacity, 1));
}
.group.toast .group-\[\.toast\]\:bg-muted {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--muted) / var(--tw-bg-opacity, 1));
}
.group.toast .group-\[\.toast\]\:bg-primary {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary) / var(--tw-bg-opacity, 1));
}
.group.toaster .group-\[\.toaster\]\:bg-background {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--background) / var(--tw-bg-opacity, 1));
}
.group.toast .group-\[\.toast\]\:text-muted-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--muted-foreground) / var(--tw-text-opacity, 1));
}
.group.toast .group-\[\.toast\]\:text-primary-foreground {
  --tw-text-opacity: 1;
  color: hsl(var(--primary-foreground) / var(--tw-text-opacity, 1));
}
.group.toaster .group-\[\.toaster\]\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.peer:checked ~ .peer-checked\:opacity-100 {
  opacity: 1;
}
.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.peer\/menu-button:hover ~ .peer-hover\/menu-button\:text-sidebar-primary {
  --tw-text-opacity: 1;
  color: hsl(var(--sidebar-primary) / var(--tw-text-opacity, 1));
}
.peer:focus ~ .peer-focus\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.peer:focus ~ .peer-focus\:ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}
.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: 0.7;
}
.has-\[\[data-variant\=inset\]\]\:bg-sidebar:has([data-variant=inset]) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--sidebar) / var(--tw-bg-opacity, 1));
}
.group\/menu-item:has([data-sidebar=menu-action]) .group-has-\[\[data-sidebar\=menu-action\]\]\/menu-item\:pr-8 {
  padding-right: 2rem;
}
.aria-disabled\:pointer-events-none[aria-disabled="true"] {
  pointer-events: none;
}
.aria-disabled\:opacity-100[aria-disabled="true"] {
  opacity: 1;
}
.aria-disabled\:opacity-50[aria-disabled="true"] {
  opacity: 0.5;
}
.aria-selected\:text-muted-foreground[aria-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--muted-foreground) / var(--tw-text-opacity, 1));
}
.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
  pointer-events: none;
}
.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}
.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=right\]\:translate-x-1[data-side="right"] {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=checked\]\:translate-x-4[data-state="checked"] {
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes accordion-up {

  from {
    height: var(--radix-accordion-content-height);
  }

  to {
    height: 0;
  }
}
.data-\[state\=closed\]\:animate-accordion-up[data-state="closed"] {
  animation: accordion-up 0.2s ease-out;
}
@keyframes accordion-down {

  from {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
  }
}
.data-\[state\=open\]\:animate-accordion-down[data-state="open"] {
  animation: accordion-down 0.2s ease-out;
}
.data-\[range-end\=true\]\:rounded-md[data-range-end="true"] {
  border-radius: 0.375rem;
}
.data-\[range-middle\=true\]\:rounded-none[data-range-middle="true"] {
  border-radius: 0px;
}
.data-\[range-start\=true\]\:rounded-md[data-range-start="true"] {
  border-radius: 0.375rem;
}
.data-\[selected\=true\]\:rounded-none[data-selected="true"] {
  border-radius: 0px;
}
.data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--sidebar-accent) / var(--tw-bg-opacity, 1));
}
.data-\[range-end\=true\]\:bg-primary[data-range-end="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary) / var(--tw-bg-opacity, 1));
}
.data-\[range-middle\=true\]\:bg-accent[data-range-middle="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent) / var(--tw-bg-opacity, 1));
}
.data-\[range-start\=true\]\:bg-primary[data-range-start="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary) / var(--tw-bg-opacity, 1));
}
.data-\[selected-single\=true\]\:bg-primary[data-selected-single="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary) / var(--tw-bg-opacity, 1));
}
.data-\[state\=active\]\:bg-primary[data-state="active"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary) / var(--tw-bg-opacity, 1));
}
.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary) / var(--tw-bg-opacity, 1));
}
.data-\[state\=open\]\:bg-accent[data-state="open"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent) / var(--tw-bg-opacity, 1));
}
.data-\[state\=open\]\:bg-accent-foreground[data-state="open"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent-foreground) / var(--tw-bg-opacity, 1));
}
.data-\[state\=open\]\:bg-curious-blue-900[data-state="open"] {
  --tw-bg-opacity: 1;
  background-color: hsl(209 70% 24% / var(--tw-bg-opacity, 1));
}
.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--secondary) / var(--tw-bg-opacity, 1));
}
.data-\[state\=selected\]\:bg-accent[data-state="selected"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--accent) / var(--tw-bg-opacity, 1));
}
.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--input) / var(--tw-bg-opacity, 1));
}
.data-\[active\=true\]\:font-medium[data-active="true"] {
  font-weight: 500;
}
.data-\[active\=true\]\:text-sidebar-primary[data-active="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--sidebar-primary) / var(--tw-text-opacity, 1));
}
.data-\[range-end\=true\]\:text-primary-foreground[data-range-end="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--primary-foreground) / var(--tw-text-opacity, 1));
}
.data-\[range-middle\=true\]\:text-accent-foreground[data-range-middle="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--accent-foreground) / var(--tw-text-opacity, 1));
}
.data-\[range-start\=true\]\:text-primary-foreground[data-range-start="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--primary-foreground) / var(--tw-text-opacity, 1));
}
.data-\[selected-single\=true\]\:text-primary-foreground[data-selected-single="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--primary-foreground) / var(--tw-text-opacity, 1));
}
.data-\[selected\=true\]\:text-primary[data-selected="true"] {
  --tw-text-opacity: 1;
  color: hsl(var(--primary) / var(--tw-text-opacity, 1));
}
.data-\[state\=active\]\:text-primary-foreground[data-state="active"] {
  --tw-text-opacity: 1;
  color: hsl(var(--primary-foreground) / var(--tw-text-opacity, 1));
}
.data-\[state\=checked\]\:text-accent-foreground[data-state="checked"] {
  --tw-text-opacity: 1;
  color: hsl(var(--accent-foreground) / var(--tw-text-opacity, 1));
}
.data-\[state\=checked\]\:text-fire-bush-700[data-state="checked"] {
  --tw-text-opacity: 1;
  color: hsl(19 78% 37% / var(--tw-text-opacity, 1));
}
.data-\[state\=checked\]\:text-outer-space-400[data-state="checked"] {
  --tw-text-opacity: 1;
  color: hsl(201 11% 53% / var(--tw-text-opacity, 1));
}
.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
  --tw-text-opacity: 1;
  color: hsl(var(--muted-foreground) / var(--tw-text-opacity, 1));
}
.data-\[state\=open\]\:text-outer-space-50[data-state="open"] {
  --tw-text-opacity: 1;
  color: hsl(200 16% 96% / var(--tw-text-opacity, 1));
}
.data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
  opacity: 0.5;
}
.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}
.data-\[state\=open\]\:opacity-100[data-state="open"] {
  opacity: 1;
}
.data-\[state\=active\]\:shadow[data-state="active"] {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  transition-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
  transition-duration: 500ms;
}
.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}
.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}
.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}
.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}
.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}
.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}
.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}
.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}
.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}
.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
  --tw-exit-translate-x: -50%;
}
.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}
.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}
.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
  --tw-exit-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}
.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}
.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
  --tw-enter-translate-x: -50%;
}
.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}
.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}
.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
  --tw-enter-translate-y: -48%;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  animation-duration: 300ms;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
  animation-duration: 500ms;
}
.data-\[state\=open\]\:hover\:bg-primary-foreground:hover[data-state="open"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--primary-foreground) / var(--tw-bg-opacity, 1));
}
.data-\[state\=open\]\:hover\:text-sidebar-primary:hover[data-state="open"] {
  --tw-text-opacity: 1;
  color: hsl(var(--sidebar-primary) / var(--tw-text-opacity, 1));
}
.group\/day[data-focused="true"] .group-data-\[focused\=true\]\/day\:relative {
  position: relative;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\] {
  left: calc(var(--sidebar-width) * -1);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\] {
  right: calc(var(--sidebar-width) * -1);
}
.group[data-side="left"] .group-data-\[side\=left\]\:-right-4 {
  right: -1rem;
}
.group[data-side="right"] .group-data-\[side\=right\]\:left-0 {
  left: 0px;
}
.group\/day[data-focused="true"] .group-data-\[focused\=true\]\/day\:z-10 {
  z-index: 10;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:-ml-2 {
  margin-left: -0.5rem;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:-mt-8 {
  margin-top: -2rem;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:hidden {
  display: none;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!size-9 {
  width: 2.25rem !important;
  height: 2.25rem !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[--sidebar-width-icon\] {
  width: var(--sidebar-width-icon);
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)\)\] {
  width: calc(var(--sidebar-width-icon) + 1rem);
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)_\+2px\)\] {
  width: calc(var(--sidebar-width-icon) + 1rem + 2px);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:w-0 {
  width: 0px;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-side="right"] .group-data-\[side\=right\]\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group\/collapsible[data-state="open"] .group-data-\[state\=open\]\/collapsible\:rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:gap-0 {
  gap: 0px;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:overflow-hidden {
  overflow: hidden;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:rounded-lg {
  border-radius: 0.5rem;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:border {
  border-width: 1px;
}
.group[data-side="left"] .group-data-\[side\=left\]\:border-r {
  border-right-width: 1px;
}
.group[data-side="right"] .group-data-\[side\=right\]\:border-l {
  border-left-width: 1px;
}
.group\/day[data-focused="true"] .group-data-\[focused\=true\]\/day\:border-ring {
  --tw-border-opacity: 1;
  border-color: hsl(var(--ring) / var(--tw-border-opacity, 1));
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:border-sidebar-border {
  --tw-border-opacity: 1;
  border-color: hsl(var(--sidebar-border) / var(--tw-border-opacity, 1));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-0 {
  padding: 0px !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-1 {
  padding: 0.25rem !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:opacity-0 {
  opacity: 0;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group\/day[data-focused="true"] .group-data-\[focused\=true\]\/day\:ring-\[3px\] {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.group\/day[data-focused="true"] .group-data-\[focused\=true\]\/day\:ring-ring\/50 {
  --tw-ring-color: hsl(var(--ring) / 0.5);
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:after\:left-full::after {
  content: var(--tw-content);
  left: 100%;
}
.group[data-collapsible="offcanvas"] .group-data-\[collapsible\=offcanvas\]\:hover\:bg-sidebar:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--sidebar) / var(--tw-bg-opacity, 1));
}
.peer\/menu-button[data-size="default"] ~ .peer-data-\[size\=default\]\/menu-button\:top-1\.5 {
  top: 0.375rem;
}
.peer\/menu-button[data-size="lg"] ~ .peer-data-\[size\=lg\]\/menu-button\:top-2\.5 {
  top: 0.625rem;
}
.peer\/menu-button[data-size="sm"] ~ .peer-data-\[size\=sm\]\/menu-button\:top-1 {
  top: 0.25rem;
}
.peer[data-variant="inset"] ~ .peer-data-\[variant\=inset\]\:min-h-\[calc\(100svh-theme\(spacing\.4\)\)\] {
  min-height: calc(100svh - 1rem);
}
.peer\/menu-button[data-active="true"] ~ .peer-data-\[active\=true\]\/menu-button\:text-sidebar-primary {
  --tw-text-opacity: 1;
  color: hsl(var(--sidebar-primary) / var(--tw-text-opacity, 1));
}
.dark\:border-r:is(.dark *) {
  border-right-width: 1px;
}
.dark\:border-destructive:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: hsl(var(--destructive) / var(--tw-border-opacity, 1));
}
.dark\:border-fire-bush-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: hsl(19 78% 37% / var(--tw-border-opacity, 1));
}
.dark\:border-wedgewood-900:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: hsl(208 30% 24% / var(--tw-border-opacity, 1));
}
.dark\:bg-destructive:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--destructive) / var(--tw-bg-opacity, 1));
}
.dark\:bg-fire-bush-500:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(30 80% 50% / var(--tw-bg-opacity, 1));
}
.dark\:bg-fire-bush-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(19 78% 37% / var(--tw-bg-opacity, 1));
}
.dark\:text-destructive-foreground:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive-foreground) / var(--tw-text-opacity, 1));
}
.dark\:text-fire-bush-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: hsl(41 82% 89% / var(--tw-text-opacity, 1));
}
.dark\:hover\:bg-cinnabar-600:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(1 83% 54% / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-fire-bush-600:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(25 81% 44% / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-fire-bush-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: hsl(15 71% 31% / var(--tw-bg-opacity, 1));
}
@media (min-width: 320px) {

  .tiny\:h-auto {
    height: auto;
  }

  .tiny\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .tiny\:px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }

  .tiny\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .tiny\:first\:pl-1:first-child {
    padding-left: 0.25rem;
  }

  .tiny\:last\:pr-1:last-child {
    padding-right: 0.25rem;
  }
}
@media (min-width: 375px) {

  .small\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .small\:block {
    display: block;
  }

  .small\:inline-block {
    display: inline-block;
  }

  .small\:size-16 {
    width: 4rem;
    height: 4rem;
  }

  .small\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .small\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .small\:flex-row {
    flex-direction: row;
  }

  .small\:justify-end {
    justify-content: flex-end;
  }

  .small\:text-nowrap {
    text-wrap: nowrap;
  }

  .small\:p-4 {
    padding: 1rem;
  }

  .small\:px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }

  .small\:pe-4 {
    padding-inline-end: 1rem;
  }

  .small\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .small\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .small\:first\:pl-1\.5:first-child {
    padding-left: 0.375rem;
  }

  .small\:last\:pr-1\.5:last-child {
    padding-right: 0.375rem;
  }
}
@media (min-width: 414px) and (max-width: 430px) {

  .standard\:hidden {
    display: none;
  }

  .standard\:items-center {
    align-items: center;
  }

  .standard\:text-nowrap {
    text-wrap: nowrap;
  }

  .standard\:p-0 {
    padding: 0px;
  }

  .standard\:align-top {
    vertical-align: top;
  }
}
@media (min-width: 480px) {

  .phablet\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .phablet\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .phablet\:flex-row {
    flex-direction: row;
  }

  .phablet\:items-center {
    align-items: center;
  }

  .phablet\:justify-end {
    justify-content: flex-end;
  }

  .phablet\:gap-2\.5 {
    gap: 0.625rem;
  }

  .phablet\:gap-4 {
    gap: 1rem;
  }

  .phablet\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .phablet\:border {
    border-width: 1px;
  }

  .phablet\:bg-background {
    --tw-bg-opacity: 1;
    background-color: hsl(var(--background) / var(--tw-bg-opacity, 1));
  }

  .phablet\:bg-card\/0 {
    background-color: hsl(var(--card) / 0);
  }

  .phablet\:bg-muted {
    --tw-bg-opacity: 1;
    background-color: hsl(var(--muted) / var(--tw-bg-opacity, 1));
  }

  .phablet\:bg-muted\/0 {
    background-color: hsl(var(--muted) / 0);
  }

  .phablet\:bg-curious-blue-50 {
    --tw-bg-opacity: 1;
    background-color: hsl(208 87% 97% / var(--tw-bg-opacity, 1));
  }

  .phablet\:p-0 {
    padding: 0px;
  }

  .phablet\:p-12 {
    padding: 3rem;
  }

  .phablet\:p-2 {
    padding: 0.5rem;
  }

  .phablet\:p-5 {
    padding: 1.25rem;
  }

  .phablet\:p-8 {
    padding: 2rem;
  }

  .phablet\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .phablet\:px-2\.5 {
    padding-left: 0.625rem;
    padding-right: 0.625rem;
  }

  .phablet\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .phablet\:px-\[7px\] {
    padding-left: 7px;
    padding-right: 7px;
  }

  .phablet\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .phablet\:pr-2\.5 {
    padding-right: 0.625rem;
  }

  .phablet\:pt-0 {
    padding-top: 0px;
  }

  .phablet\:pt-1 {
    padding-top: 0.25rem;
  }

  .phablet\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .phablet\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .phablet\:first\:pl-2\.5:first-child {
    padding-left: 0.625rem;
  }

  .phablet\:last\:pr-2\.5:last-child {
    padding-right: 0.625rem;
  }
}
@media (min-width: 600px) {

  .tablet-sm\:col-auto {
    grid-column: auto;
  }

  .tablet-sm\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .tablet-sm\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .tablet-sm\:block {
    display: block;
  }

  .tablet-sm\:hidden {
    display: none;
  }

  .tablet-sm\:w-\[300px\] {
    width: 300px;
  }

  .tablet-sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .tablet-sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .tablet-sm\:flex-row {
    flex-direction: row;
  }
}
@media (min-width: 768px) {

  .tablet-md\:block {
    display: block;
  }

  .tablet-md\:flex {
    display: flex;
  }

  .tablet-md\:hidden {
    display: none;
  }

  .tablet-md\:flex-row {
    flex-direction: row;
  }

  .tablet-md\:border-none {
    border-style: none;
  }
}
@media (min-width: 1280px) {

  .laptop\:static {
    position: static;
  }

  .laptop\:block {
    display: block;
  }

  .laptop\:hidden {
    display: none;
  }

  .laptop\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (min-width: 1536px) {

  .desktop\:hidden {
    display: none;
  }
}
@media (min-width: 375px) {

  .\32xs\:hidden {
    display: none;
  }

  .\32xs\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .\32xs\:max-w-\[200px\] {
    max-width: 200px;
  }

  .\32xs\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .\32xs\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}
@media (min-width: 480px) {

  .xs\:col-span-12 {
    grid-column: span 12 / span 12;
  }

  .xs\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .xs\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .xs\:block {
    display: block;
  }

  .xs\:inline {
    display: inline;
  }

  .xs\:hidden {
    display: none;
  }

  .xs\:h-80 {
    height: 20rem;
  }

  .xs\:h-\[85px\] {
    height: 85px;
  }

  .xs\:h-full {
    height: 100%;
  }

  .xs\:w-\[200px\] {
    width: 200px;
  }

  .xs\:w-auto {
    width: auto;
  }

  .xs\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .xs\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xs\:flex-row {
    flex-direction: row;
  }

  .xs\:items-center {
    align-items: center;
  }

  .xs\:justify-between {
    justify-content: space-between;
  }

  .xs\:gap-0 {
    gap: 0px;
  }

  .xs\:gap-2 {
    gap: 0.5rem;
  }

  .xs\:gap-4 {
    gap: 1rem;
  }

  .xs\:gap-6 {
    gap: 1.5rem;
  }

  .xs\:p-5 {
    padding: 1.25rem;
  }

  .xs\:\!py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .xs\:px-2\.5 {
    padding-left: 0.625rem;
    padding-right: 0.625rem;
  }

  .xs\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .xs\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .xs\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .xs\:py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }

  .xs\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .xs\:pb-\[7px\] {
    padding-bottom: 7px;
  }

  .xs\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}
@media (min-width: 768px) {

  .sm\:-left-\[25px\] {
    left: -25px;
  }

  .sm\:-left-\[46px\] {
    left: -46px;
  }

  .sm\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .sm\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .sm\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .sm\:col-end-6 {
    grid-column-end: 6;
  }

  .sm\:m-2 {
    margin: 0.5rem;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:mb-12 {
    margin-bottom: 3rem;
  }

  .sm\:ml-2 {
    margin-left: 0.5rem;
  }

  .sm\:ml-3 {
    margin-left: 0.75rem;
  }

  .sm\:ml-auto {
    margin-left: auto;
  }

  .sm\:mr-0 {
    margin-right: 0px;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:grid {
    display: grid;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:size-9 {
    width: 2.25rem;
    height: 2.25rem;
  }

  .sm\:h-12 {
    height: 3rem;
  }

  .sm\:h-24 {
    height: 6rem;
  }

  .sm\:h-9 {
    height: 2.25rem;
  }

  .sm\:h-full {
    height: 100%;
  }

  .sm\:w-12 {
    width: 3rem;
  }

  .sm\:w-9 {
    width: 2.25rem;
  }

  .sm\:w-96 {
    width: 24rem;
  }

  .sm\:w-\[540px\] {
    width: 540px;
  }

  .sm\:w-\[600px\] {
    width: 600px;
  }

  .sm\:w-\[60vw\] {
    width: 60vw;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .sm\:min-w-\[300px\] {
    min-width: 300px;
  }

  .sm\:max-w-\[600px\] {
    max-width: 600px;
  }

  .sm\:max-w-fit {
    max-width: -moz-fit-content;
    max-width: fit-content;
  }

  .sm\:max-w-lg {
    max-width: 32rem;
  }

  .sm\:max-w-md {
    max-width: 28rem;
  }

  .sm\:max-w-xl {
    max-width: 36rem;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-wrap {
    flex-wrap: wrap;
  }

  .sm\:items-end {
    align-items: flex-end;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-2 {
    gap: 0.5rem;
  }

  .sm\:gap-2\.5 {
    gap: 0.625rem;
  }

  .sm\:gap-3 {
    gap: 0.75rem;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:gap-5 {
    gap: 1.25rem;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }

  .sm\:rounded-lg {
    border-radius: 0.5rem;
  }

  .sm\:rounded-xl {
    border-radius: 0.75rem;
  }

  .sm\:rounded-l-xl {
    border-top-left-radius: 0.75rem;
    border-bottom-left-radius: 0.75rem;
  }

  .sm\:rounded-r-none {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }

  .sm\:border {
    border-width: 1px;
  }

  .sm\:p-5 {
    padding: 1.25rem;
  }

  .sm\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }

  .sm\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .sm\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .sm\:pe-4 {
    padding-inline-end: 1rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:opacity-0 {
    opacity: 0;
  }

  .after\:sm\:hidden::after {
    content: var(--tw-content);
    display: none;
  }

  .peer[data-variant="inset"] ~ .sm\:peer-data-\[variant\=inset\]\:m-2 {
    margin: 0.5rem;
  }

  .peer[data-state="collapsed"][data-variant="inset"] ~ .sm\:peer-data-\[state\=collapsed\]\:peer-data-\[variant\=inset\]\:ml-2 {
    margin-left: 0.5rem;
  }

  .peer[data-variant="inset"] ~ .sm\:peer-data-\[variant\=inset\]\:ml-0 {
    margin-left: 0px;
  }

  .peer[data-variant="inset"] ~ .sm\:peer-data-\[variant\=inset\]\:rounded-xl {
    border-radius: 0.75rem;
  }

  .peer[data-variant="inset"] ~ .sm\:peer-data-\[variant\=inset\]\:shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
@media (min-width: 1024px) {

  .md\:bottom-0 {
    bottom: 0px;
  }

  .md\:right-6 {
    right: 1.5rem;
  }

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .md\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .md\:my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .md\:-mt-24 {
    margin-top: -6rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:block {
    display: block;
  }

  .md\:inline-block {
    display: inline-block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:inline-flex {
    display: inline-flex;
  }

  .md\:table-cell {
    display: table-cell;
  }

  .md\:table-row {
    display: table-row;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:\!h-\[500px\] {
    height: 500px !important;
  }

  .md\:h-8 {
    height: 2rem;
  }

  .md\:h-9 {
    height: 2.25rem;
  }

  .md\:h-\[500px\] {
    height: 500px;
  }

  .md\:h-auto {
    height: auto;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-2\/3 {
    width: 66.666667%;
  }

  .md\:w-3\/4 {
    width: 75%;
  }

  .md\:w-64 {
    width: 16rem;
  }

  .md\:w-8 {
    width: 2rem;
  }

  .md\:w-9 {
    width: 2.25rem;
  }

  .md\:w-96 {
    width: 24rem;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-max {
    width: -moz-max-content;
    width: max-content;
  }

  .md\:min-w-64 {
    min-width: 16rem;
  }

  .md\:max-w-5xl {
    max-width: 64rem;
  }

  .md\:max-w-80 {
    max-width: 20rem;
  }

  .md\:flex-none {
    flex: none;
  }

  .md\:border-collapse {
    border-collapse: collapse;
  }

  .md\:border-spacing-0 {
    --tw-border-spacing-x: 0px;
    --tw-border-spacing-y: 0px;
    border-spacing: var(--tw-border-spacing-x) var(--tw-border-spacing-y);
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-cols-\[120px_1fr\] {
    grid-template-columns: 120px 1fr;
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-col {
    flex-direction: column;
  }

  .md\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-3 {
    gap: 0.75rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:space-x-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.25rem * var(--tw-space-x-reverse));
    margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:p-1 {
    padding: 0.25rem;
  }

  .md\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pb-2 {
    padding-bottom: 0.5rem;
  }

  .md\:pl-\[4\.7rem\] {
    padding-left: 4.7rem;
  }

  .md\:pr-5 {
    padding-right: 1.25rem;
  }

  .md\:opacity-0 {
    opacity: 0;
  }

  .group:hover .group-hover\:md\:opacity-100 {
    opacity: 1;
  }
}
@media (min-width: 1280px) {

  .lg\:bottom-0 {
    bottom: 0px;
  }

  .lg\:\!col-span-1 {
    grid-column: span 1 / span 1 !important;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .lg\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .lg\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .lg\:m-3 {
    margin: 0.75rem;
  }

  .lg\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .lg\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .lg\:-ml-1 {
    margin-left: -0.25rem;
  }

  .lg\:ml-4 {
    margin-left: 1rem;
  }

  .lg\:mr-2 {
    margin-right: 0.5rem;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:block {
    display: block;
  }

  .lg\:inline {
    display: inline;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:table-cell {
    display: table-cell;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:\!h-\[500px\] {
    height: 500px !important;
  }

  .lg\:h-\[500px\] {
    height: 500px;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-4\/5 {
    width: 80%;
  }

  .lg\:w-96 {
    width: 24rem;
  }

  .lg\:w-\[250px\] {
    width: 250px;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:max-w-lg {
    max-width: 32rem;
  }

  .lg\:max-w-none {
    max-width: none;
  }

  .lg\:\!grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-6 {
    gap: 1.5rem;
  }

  .lg\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:overflow-visible {
    overflow: visible;
  }

  .lg\:p-3 {
    padding: 0.75rem;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:pb-\[23px\] {
    padding-bottom: 23px;
  }

  .lg\:pl-7 {
    padding-left: 1.75rem;
  }

  .lg\:pr-8 {
    padding-right: 2rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:pt-4 {
    padding-top: 1rem;
  }

  .lg\:align-middle {
    vertical-align: middle;
  }

  .lg\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}
@media (min-width: 1536px) {

  .xl\:block {
    display: block;
  }

  .xl\:inline-block {
    display: inline-block;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:gap-8 {
    gap: 2rem;
  }

  .xl\:rounded-t-lg {
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
  }

  .xl\:border {
    border-width: 1px;
  }

  .xl\:border-0 {
    border-width: 0px;
  }

  .xl\:border-r {
    border-right-width: 1px;
  }

  .xl\:border-t {
    border-top-width: 1px;
  }

  .xl\:p-8 {
    padding: 2rem;
  }

  .xl\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .xl\:pl-2 {
    padding-left: 0.5rem;
  }

  .xl\:shadow-2xl {
    --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }

  .\32xl\:flex {
    display: flex;
  }

  .\32xl\:gap-2 {
    gap: 0.5rem;
  }

  .\32xl\:align-middle {
    vertical-align: middle;
  }
}
@media (orientation: landscape) {

  .landscape\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .landscape\:hidden {
    display: none;
  }

  .landscape\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .landscape\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .landscape\:gap-6 {
    gap: 1.5rem;
  }

  .landscape\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .landscape\:border {
    border-width: 1px;
  }

  .landscape\:border-border {
    --tw-border-opacity: 1;
    border-color: hsl(var(--border) / var(--tw-border-opacity, 1));
  }

  .landscape\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}
.rtl\:text-right:where([dir="rtl"], [dir="rtl"] *) {
  text-align: right;
}
.peer:checked ~ .rtl\:peer-checked\:after\:-translate-x-full:where([dir="rtl"], [dir="rtl"] *)::after {
  content: var(--tw-content);
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\:first-child\[data-selected\=true\]_button\]\:rounded-l-md:first-child[data-selected=true] button {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role=checkbox]) {
  padding-right: 0px;
}
.\[\&\:last-child\[data-selected\=true\]_button\]\:rounded-r-md:last-child[data-selected=true] button {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
.\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\]>[role=checkbox] {
  --tw-translate-y: 2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>button\]\:hidden>button {
  display: none;
}
.\[\&\>span\:last-child\]\:truncate>span:last-child {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.\[\&\>span\]\:line-clamp-1>span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.\[\&\>span\]\:text-sm>span {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.\[\&\>span\]\:opacity-70>span {
  opacity: 0.7;
}
.\[\&\>svg\+div\]\:translate-y-\[-3px\]>svg+div {
  --tw-translate-y: -3px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>svg\]\:absolute>svg {
  position: absolute;
}
.\[\&\>svg\]\:left-4>svg {
  left: 1rem;
}
.\[\&\>svg\]\:top-4>svg {
  top: 1rem;
}
.\[\&\>svg\]\:size-3\.5>svg {
  width: 0.875rem;
  height: 0.875rem;
}
.\[\&\>svg\]\:size-4>svg {
  width: 1rem;
  height: 1rem;
}
.\[\&\>svg\]\:size-6>svg {
  width: 1.5rem;
  height: 1.5rem;
}
.\[\&\>svg\]\:h-2\.5>svg {
  height: 0.625rem;
}
.\[\&\>svg\]\:h-3>svg {
  height: 0.75rem;
}
.\[\&\>svg\]\:h-3\.5>svg {
  height: 0.875rem;
}
.\[\&\>svg\]\:w-2\.5>svg {
  width: 0.625rem;
}
.\[\&\>svg\]\:w-3>svg {
  width: 0.75rem;
}
.\[\&\>svg\]\:w-3\.5>svg {
  width: 0.875rem;
}
.\[\&\>svg\]\:shrink-0>svg {
  flex-shrink: 0;
}
.\[\&\>svg\]\:text-destructive>svg {
  --tw-text-opacity: 1;
  color: hsl(var(--destructive) / var(--tw-text-opacity, 1));
}
.\[\&\>svg\]\:text-muted-foreground>svg {
  --tw-text-opacity: 1;
  color: hsl(var(--muted-foreground) / var(--tw-text-opacity, 1));
}
.\[\&\>svg\]\:text-sidebar-primary>svg {
  --tw-text-opacity: 1;
  color: hsl(var(--sidebar-primary) / var(--tw-text-opacity, 1));
}
.\[\&\>svg\~\*\]\:pl-7>svg~* {
  padding-left: 1.75rem;
}
.\[\&\>td\:first-child\]\:rounded-l-lg>td:first-child {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.\[\&\>td\:last-child\]\:rounded-r-lg>td:last-child {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.\[\&\>td\>span\>span\]\:bg-destructive-200>td>span>span {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--destructive-200) / var(--tw-bg-opacity, 1));
}
.\[\&\>td\>span\>span\]\:bg-warning-100>td>span>span {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--warning-100) / var(--tw-bg-opacity, 1));
}
.\[\&\>tr\]\:last\:border-b-0:last-child>tr {
  border-bottom-width: 0px;
}
.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
  fill: hsl(var(--muted-foreground) / 1);
}
.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke='#ccc'] {
  stroke: hsl(var(--border) / 0.5);
}
.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
  stroke: hsl(var(--border) / 1);
}
.\[\&_\.recharts-layer\]\:outline-none .recharts-layer {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke='#ccc'] {
  stroke: hsl(var(--border) / 1);
}
.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector {
  fill: hsl(var(--muted) / 1);
}
.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
  fill: hsl(var(--muted) / 1);
}
.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke='#ccc'] {
  stroke: hsl(var(--border) / 1);
}
.\[\&_\.recharts-sector\]\:outline-none .recharts-sector {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\.recharts-surface\]\:outline-none .recharts-surface {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-group-heading\]\]\:py-1 [cmdk-group-heading] {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
  --tw-text-opacity: 1;
  color: hsl(var(--muted-foreground) / var(--tw-text-opacity, 1));
}
.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group] {
  padding-top: 0px;
}
.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
  height: 1.25rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
  width: 1.25rem;
}
.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
  height: 3rem;
}
.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
  height: 1.25rem;
}
.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
  width: 1.25rem;
}
.\[\&_\[data-fallback\]\]\:border-none [data-fallback] {
  border-style: none;
}
.\[\&_\[data-fallback\]\]\:border-curious-blue-50 [data-fallback] {
  --tw-border-opacity: 1;
  border-color: hsl(208 87% 97% / var(--tw-border-opacity, 1));
}
.\[\&_\[data-fallback\]\]\:text-\[14px\] [data-fallback] {
  font-size: 14px;
}
.\[\&_\[data-fallback\]\]\:text-\[18px\] [data-fallback] {
  font-size: 18px;
}
.\[\&_\[data-fallback\]\]\:text-\[21px\] [data-fallback] {
  font-size: 21px;
}
.\[\&_\[data-fallback\]\]\:text-\[24px\] [data-fallback] {
  font-size: 24px;
}
.\[\&_\[data-fallback\]\]\:text-\[32px\] [data-fallback] {
  font-size: 32px;
}
.\[\&_\[data-fallback\]\]\:shadow-none [data-fallback] {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.\[\&_img\]\:\!size-11 img {
  width: 2.75rem !important;
  height: 2.75rem !important;
}
.\[\&_img\]\:\!size-5 img {
  width: 1.25rem !important;
  height: 1.25rem !important;
}
.\[\&_img\]\:\!size-6 img {
  width: 1.5rem !important;
  height: 1.5rem !important;
}
.\[\&_img\]\:\!size-8 img {
  width: 2rem !important;
  height: 2rem !important;
}
.\[\&_p\]\:leading-relaxed p {
  line-height: 1.625;
}
.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}
.\[\&_svg\]\:invisible svg {
  visibility: hidden;
}
.\[\&_svg\]\:\!size-11 svg {
  width: 2.75rem !important;
  height: 2.75rem !important;
}
.\[\&_svg\]\:\!size-5 svg {
  width: 1.25rem !important;
  height: 1.25rem !important;
}
.\[\&_svg\]\:\!size-6 svg {
  width: 1.5rem !important;
  height: 1.5rem !important;
}
.\[\&_svg\]\:\!size-8 svg {
  width: 2rem !important;
  height: 2rem !important;
}
.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}
.\[\&_svg\]\:size-5 svg {
  width: 1.25rem;
  height: 1.25rem;
}
.\[\&_svg\]\:size-auto svg {
  width: auto;
  height: auto;
}
.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}
.\[\&_td_span\]\:inset-y-0 td span {
  top: 0px;
  bottom: 0px;
}
.\[\&_tr\:last-child\]\:border-0 tr:last-child {
  border-width: 0px;
}
.\[\&_tr\]\:border-border tr {
  --tw-border-opacity: 1;
  border-color: hsl(var(--border) / var(--tw-border-opacity, 1));
}
[data-side=left][data-collapsible=offcanvas] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
  right: -0.5rem;
}
[data-side=left][data-state=collapsed] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
  cursor: e-resize;
}
[data-side=left] .\[\[data-side\=left\]_\&\]\:cursor-w-resize {
  cursor: w-resize;
}
[data-side=right][data-collapsible=offcanvas] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
  left: -0.5rem;
}
[data-side=right][data-state=collapsed] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
  cursor: w-resize;
}
[data-side=right] .\[\[data-side\=right\]_\&\]\:cursor-e-resize {
  cursor: e-resize;
}

