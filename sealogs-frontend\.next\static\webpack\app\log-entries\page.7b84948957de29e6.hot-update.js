"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/restricted-visibility.tsx":
/*!************************************************************!*\
  !*** ./src/app/ui/logbook/forms/restricted-visibility.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RestrictedVisibility; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/offline/models/tripEvent */ \"(app-pages-browser)/./src/app/offline/models/tripEvent.js\");\n/* harmony import */ var _app_offline_models_eventType_RestrictedVisibility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/offline/models/eventType_RestrictedVisibility */ \"(app-pages-browser)/./src/app/offline/models/eventType_RestrictedVisibility.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _editor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../editor */ \"(app-pages-browser)/./src/app/ui/editor.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/slider */ \"(app-pages-browser)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/alert-dialog-new */ \"(app-pages-browser)/./src/components/ui/alert-dialog-new.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/check-field-label */ \"(app-pages-browser)/./src/components/ui/check-field-label.tsx\");\n/* harmony import */ var _components_CloudFlareCaptures__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ../components/CloudFlareCaptures */ \"(app-pages-browser)/./src/app/ui/logbook/components/CloudFlareCaptures.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction RestrictedVisibility(param) {\n    let { currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, logBookConfig, locked, offline = false, members } = param;\n    var _tripEvent_eventType_RestrictedVisibility, _tripEvent_eventType_RestrictedVisibility1, _tripEvent_eventType_RestrictedVisibility2, _tripEvent_eventType_RestrictedVisibility3, _tripEvent_eventType_RestrictedVisibility4, _tripEvent_eventType_RestrictedVisibility5, _currentRisk_mitigationStrategy_nodes, _currentRisk_mitigationStrategy, _currentRisk_mitigationStrategy1;\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_13__.useSearchParams)();\n    var _searchParams_get;\n    const vesselID = (_searchParams_get = searchParams.get(\"vesselID\")) !== null && _searchParams_get !== void 0 ? _searchParams_get : 0;\n    const [crossingTime, setCrossingTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [crossedTime, setCrossedTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [restrictedVisibility, setRestrictedVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [openProcedureChecklist, setOpenProcedureChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Used in the component to track if SOP should be displayed\n    const [displaySOP, setDisplaySOP] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [selectedAuthor, setSelectedAuthor] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [openRiskDialog, setOpenRiskDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentRisk, setCurrentRisk] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [riskValue, setRiskValue] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [riskToDelete, setRiskToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [riskFactors, setRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewMembers, setCrewMembers] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [allRisks, setAllRisks] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [openRecommendedstrategy, setOpenRecommendedstrategy] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [allRiskFactors, setAllRiskFactors] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [currentStrategies, setCurrentStrategies] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [recommendedStratagies, setRecommendedStratagies] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [openDeleteConfirmation, setOpenDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentStartLocation, setCurrentStartLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentEndLocation, setCurrentEndLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const tripEventModel = new _app_offline_models_tripEvent__WEBPACK_IMPORTED_MODULE_7__[\"default\"]();\n    const restrictedVisibilityModel = new _app_offline_models_eventType_RestrictedVisibility__WEBPACK_IMPORTED_MODULE_8__[\"default\"]();\n    const currentEventRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [closeOnSave, setCloseOnSave] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleCrossingTimeChange = (date)=>{\n        setCrossingTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    const handleCrossedTimeChange = (date)=>{\n        setCrossedTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n    };\n    // Function to set display SOP state - used in the component\n    const handleSetDisplaySOP = (value)=>{\n        setDisplaySOP(value);\n        setOpenProcedureChecklist(value);\n    };\n    const handleEditorChange = (newContent)=>{\n        setContent(newContent);\n    };\n    const handleDeleteRisk = async ()=>{\n        updateRiskFactor({\n            variables: {\n                input: {\n                    id: riskToDelete.id,\n                    eventType_RestrictedVisibilityID: 0,\n                    vesselID: 0\n                }\n            }\n        });\n        setOpenDeleteConfirmation(false);\n    };\n    const handleSetRiskToDelete = (risk)=>{\n        setRiskToDelete(risk);\n        setOpenDeleteConfirmation(true);\n    };\n    const handleNewStrategy = async ()=>{\n        if (content) {\n            createMitigationStrategy({\n                variables: {\n                    input: {\n                        strategy: content\n                    }\n                }\n            });\n        }\n        setOpenRecommendedstrategy(false);\n    };\n    const [createMitigationStrategy] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateMitigationStrategy, {\n        onCompleted: (data)=>{\n            setCurrentStrategies([\n                ...currentStrategies,\n                {\n                    id: data.createMitigationStrategy.id,\n                    strategy: content\n                }\n            ]);\n            setContent(\"\");\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleSetCurrentStrategies = (strategy)=>{\n        if (currentStrategies.length > 0) {\n            if (currentStrategies.find((s)=>s.id === strategy.id)) {\n                setCurrentStrategies(currentStrategies.filter((s)=>s.id !== strategy.id));\n            } else {\n                setCurrentStrategies([\n                    ...currentStrategies,\n                    strategy\n                ]);\n            }\n        } else {\n            setCurrentStrategies([\n                strategy\n            ]);\n        }\n    };\n    const handleSetRiskValue = (v)=>{\n        setRiskValue({\n            value: v.title,\n            label: v.title\n        });\n        if (v.mitigationStrategy.nodes) {\n            setCurrentStrategies(v.mitigationStrategy.nodes);\n        }\n        if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n            var _risk_mitigationStrategy_nodes;\n            return risk.title === v.title && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n        }).length) > 0) {\n            setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                var _r_mitigationStrategy_nodes;\n                return r.title === v.title && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n            }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                    id: s.id,\n                    strategy: s.strategy\n                })))));\n        } else {\n            setRecommendedStratagies(false);\n        }\n    };\n    // This function is no longer used as its functionality is now in handleRiskValue\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getRiskFactors({\n            variables: {\n                filter: {\n                    type: {\n                        eq: \"RestrictedVisibility\"\n                    }\n                }\n            }\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        getRiskFactors({\n            variables: {\n                filter: {\n                    type: {\n                        eq: \"RestrictedVisibility\"\n                    }\n                }\n            }\n        });\n    }, [\n        openProcedureChecklist\n    ]);\n    const [getRiskFactors] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GetRiskFactors, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            var _Array_from, _data_readRiskFactors_nodes;\n            const risks = (_Array_from = Array.from(new Set((_data_readRiskFactors_nodes = data.readRiskFactors.nodes) === null || _data_readRiskFactors_nodes === void 0 ? void 0 : _data_readRiskFactors_nodes.map((risk)=>risk.title)))) === null || _Array_from === void 0 ? void 0 : _Array_from.map((risk)=>({\n                    label: risk,\n                    value: risk\n                }));\n            setAllRisks(risks);\n            setAllRiskFactors(data.readRiskFactors.nodes);\n            setRiskFactors(data.readRiskFactors.nodes.filter((r)=>r.eventType_RestrictedVisibilityID == (restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.id)));\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setRestrictedVisibility(false);\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentEvent(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setRestrictedVisibility(false);\n        if (currentEvent) {\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentEvent = async (id)=>{\n        getTripEvent({\n            variables: {\n                id: id\n            }\n        });\n    };\n    const [getTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_4__.GetTripEvent, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripEvent;\n            if (event) {\n                var _event_eventType_RestrictedVisibility, _event_eventType_RestrictedVisibility1, _event_eventType_RestrictedVisibility2, _event_eventType_RestrictedVisibility3, _event_eventType_RestrictedVisibility4, _event_eventType_RestrictedVisibility5, _event_eventType_RestrictedVisibility6, _event_eventType_RestrictedVisibility7, _event_eventType_RestrictedVisibility8, _event_eventType_RestrictedVisibility9, _event_eventType_RestrictedVisibility10, _event_eventType_RestrictedVisibility11, _event_eventType_RestrictedVisibility12, _event_eventType_RestrictedVisibility13, _event_eventType_RestrictedVisibility14, _event_eventType_RestrictedVisibility15, _event_eventType_RestrictedVisibility16, _event_eventType_RestrictedVisibility17, _event_eventType_RestrictedVisibility18, _event_eventType_RestrictedVisibility19, _event_eventType_RestrictedVisibility20, _event_eventType_RestrictedVisibility21, _event_eventType_RestrictedVisibility22, _event_eventType_RestrictedVisibility23, _event_eventType_RestrictedVisibility24, _event_eventType_RestrictedVisibility25, _event_eventType_RestrictedVisibility26, _event_eventType_RestrictedVisibility27, _event_eventType_RestrictedVisibility28, _event_eventType_RestrictedVisibility29, _event_eventType_RestrictedVisibility30, _event_eventType_RestrictedVisibility31, _event_eventType_RestrictedVisibility32, _event_eventType_RestrictedVisibility33, _event_eventType_RestrictedVisibility34;\n                setTripEvent(event);\n                setRestrictedVisibility({\n                    id: +event.eventType_RestrictedVisibility.id,\n                    startLocationID: (_event_eventType_RestrictedVisibility = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility === void 0 ? void 0 : _event_eventType_RestrictedVisibility.startLocationID,\n                    crossingTime: (_event_eventType_RestrictedVisibility1 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility1 === void 0 ? void 0 : _event_eventType_RestrictedVisibility1.crossingTime,\n                    estSafeSpeed: (_event_eventType_RestrictedVisibility2 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility2 === void 0 ? void 0 : _event_eventType_RestrictedVisibility2.estSafeSpeed,\n                    stopAssessPlan: (_event_eventType_RestrictedVisibility3 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility3 === void 0 ? void 0 : _event_eventType_RestrictedVisibility3.stopAssessPlan,\n                    crewBriefing: (_event_eventType_RestrictedVisibility4 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility4 === void 0 ? void 0 : _event_eventType_RestrictedVisibility4.crewBriefing,\n                    navLights: (_event_eventType_RestrictedVisibility5 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility5 === void 0 ? void 0 : _event_eventType_RestrictedVisibility5.navLights,\n                    soundSignal: (_event_eventType_RestrictedVisibility6 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility6 === void 0 ? void 0 : _event_eventType_RestrictedVisibility6.soundSignal,\n                    lookout: (_event_eventType_RestrictedVisibility7 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility7 === void 0 ? void 0 : _event_eventType_RestrictedVisibility7.lookout,\n                    soundSignals: (_event_eventType_RestrictedVisibility8 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility8 === void 0 ? void 0 : _event_eventType_RestrictedVisibility8.soundSignals,\n                    radarWatch: (_event_eventType_RestrictedVisibility9 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility9 === void 0 ? void 0 : _event_eventType_RestrictedVisibility9.radarWatch,\n                    radioWatch: (_event_eventType_RestrictedVisibility10 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility10 === void 0 ? void 0 : _event_eventType_RestrictedVisibility10.radioWatch,\n                    endLocationID: (_event_eventType_RestrictedVisibility11 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility11 === void 0 ? void 0 : _event_eventType_RestrictedVisibility11.endLocationID,\n                    crossedTime: (_event_eventType_RestrictedVisibility12 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility12 === void 0 ? void 0 : _event_eventType_RestrictedVisibility12.crossedTime,\n                    approxSafeSpeed: (_event_eventType_RestrictedVisibility13 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility13 === void 0 ? void 0 : _event_eventType_RestrictedVisibility13.approxSafeSpeed,\n                    report: (_event_eventType_RestrictedVisibility14 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility14 === void 0 ? void 0 : _event_eventType_RestrictedVisibility14.report,\n                    startLat: (_event_eventType_RestrictedVisibility15 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility15 === void 0 ? void 0 : _event_eventType_RestrictedVisibility15.startLat,\n                    startLong: (_event_eventType_RestrictedVisibility16 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility16 === void 0 ? void 0 : _event_eventType_RestrictedVisibility16.startLong,\n                    endLat: (_event_eventType_RestrictedVisibility17 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility17 === void 0 ? void 0 : _event_eventType_RestrictedVisibility17.endLat,\n                    endLong: (_event_eventType_RestrictedVisibility18 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility18 === void 0 ? void 0 : _event_eventType_RestrictedVisibility18.endLong\n                });\n                if (((_event_eventType_RestrictedVisibility19 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility19 === void 0 ? void 0 : _event_eventType_RestrictedVisibility19.startLat) && ((_event_eventType_RestrictedVisibility20 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility20 === void 0 ? void 0 : _event_eventType_RestrictedVisibility20.startLong)) {\n                    var _event_eventType_RestrictedVisibility35, _event_eventType_RestrictedVisibility36;\n                    setCurrentStartLocation({\n                        latitude: (_event_eventType_RestrictedVisibility35 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility35 === void 0 ? void 0 : _event_eventType_RestrictedVisibility35.startLat,\n                        longitude: (_event_eventType_RestrictedVisibility36 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility36 === void 0 ? void 0 : _event_eventType_RestrictedVisibility36.startLong\n                    });\n                }\n                if (((_event_eventType_RestrictedVisibility21 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility21 === void 0 ? void 0 : _event_eventType_RestrictedVisibility21.endLat) && ((_event_eventType_RestrictedVisibility22 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility22 === void 0 ? void 0 : _event_eventType_RestrictedVisibility22.endLong)) {\n                    var _event_eventType_RestrictedVisibility37, _event_eventType_RestrictedVisibility38;\n                    setCurrentEndLocation({\n                        latitude: (_event_eventType_RestrictedVisibility37 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility37 === void 0 ? void 0 : _event_eventType_RestrictedVisibility37.endLat,\n                        longitude: (_event_eventType_RestrictedVisibility38 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility38 === void 0 ? void 0 : _event_eventType_RestrictedVisibility38.endLong\n                    });\n                }\n                setCrossedTime((_event_eventType_RestrictedVisibility23 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility23 === void 0 ? void 0 : _event_eventType_RestrictedVisibility23.crossedTime);\n                setCrossingTime((_event_eventType_RestrictedVisibility24 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility24 === void 0 ? void 0 : _event_eventType_RestrictedVisibility24.crossingTime);\n                if (((_event_eventType_RestrictedVisibility25 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility25 === void 0 ? void 0 : _event_eventType_RestrictedVisibility25.memberID) > 0) {\n                    var _event_eventType_RestrictedVisibility39, _event_eventType_RestrictedVisibility40, _event_eventType_RestrictedVisibility41;\n                    setSelectedAuthor({\n                        label: \"\".concat((_event_eventType_RestrictedVisibility39 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility39 === void 0 ? void 0 : _event_eventType_RestrictedVisibility39.member.firstName, \" \").concat((_event_eventType_RestrictedVisibility40 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility40 === void 0 ? void 0 : _event_eventType_RestrictedVisibility40.member.surname),\n                        value: (_event_eventType_RestrictedVisibility41 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility41 === void 0 ? void 0 : _event_eventType_RestrictedVisibility41.memberID\n                    });\n                }\n                if (((_event_eventType_RestrictedVisibility26 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility26 === void 0 ? void 0 : _event_eventType_RestrictedVisibility26.stopAssessPlan) || ((_event_eventType_RestrictedVisibility27 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility27 === void 0 ? void 0 : _event_eventType_RestrictedVisibility27.crewBriefing) || ((_event_eventType_RestrictedVisibility28 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility28 === void 0 ? void 0 : _event_eventType_RestrictedVisibility28.navLights) || ((_event_eventType_RestrictedVisibility29 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility29 === void 0 ? void 0 : _event_eventType_RestrictedVisibility29.soundSignal) || ((_event_eventType_RestrictedVisibility30 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility30 === void 0 ? void 0 : _event_eventType_RestrictedVisibility30.lookout) || ((_event_eventType_RestrictedVisibility31 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility31 === void 0 ? void 0 : _event_eventType_RestrictedVisibility31.soundSignals) || ((_event_eventType_RestrictedVisibility32 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility32 === void 0 ? void 0 : _event_eventType_RestrictedVisibility32.radarWatch) || ((_event_eventType_RestrictedVisibility33 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility33 === void 0 ? void 0 : _event_eventType_RestrictedVisibility33.radioWatch) || ((_event_eventType_RestrictedVisibility34 = event.eventType_RestrictedVisibility) === null || _event_eventType_RestrictedVisibility34 === void 0 ? void 0 : _event_eventType_RestrictedVisibility34.memberID) > 0) {\n                    setDisplaySOP(true);\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    const handleSave = async ()=>{\n        const variables = {\n            input: {\n                startLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.startLocationID,\n                crossingTime: crossingTime !== null && crossingTime !== void 0 ? crossingTime : dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"),\n                estSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.estSafeSpeed,\n                stopAssessPlan: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.stopAssessPlan,\n                crewBriefing: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.crewBriefing,\n                navLights: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.navLights,\n                soundSignal: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignal,\n                lookout: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.lookout,\n                soundSignals: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignals,\n                radarWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radarWatch,\n                radioWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radioWatch,\n                endLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.endLocationID,\n                crossedTime: crossedTime !== null && crossedTime !== void 0 ? crossedTime : dayjs__WEBPACK_IMPORTED_MODULE_1___default()().format(\"HH:mm\"),\n                approxSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.approxSafeSpeed,\n                report: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.report,\n                startLat: currentStartLocation.latitude.toString(),\n                startLong: currentStartLocation.longitude.toString(),\n                endLat: currentEndLocation.latitude.toString(),\n                endLong: currentEndLocation.longitude.toString(),\n                memberID: selectedAuthor === null || selectedAuthor === void 0 ? void 0 : selectedAuthor.value\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                await tripEventModel.save({\n                    id: +currentEvent.id,\n                    eventCategory: \"RestrictedVisibility\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripEvent({\n                    variables: {\n                        input: {\n                            id: +currentEvent.id,\n                            eventCategory: \"RestrictedVisibility\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n            if (offline) {\n                await restrictedVisibilityModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_RestrictedVisibilityID),\n                    ...variables.input\n                });\n            } else {\n                updateEventType_RestrictedVisibility({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.eventType_RestrictedVisibilityID),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                const tripEventData = await tripEventModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_9__.generateUniqueId)(),\n                    eventCategory: \"RestrictedVisibility\",\n                    logBookEntrySectionID: currentTrip.id\n                });\n                setCurrentEvent(tripEventData);\n                const restrictedVisibilityData = await restrictedVisibilityModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_9__.generateUniqueId)(),\n                    startLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.startLocationID,\n                    crossingTime: crossingTime,\n                    estSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.estSafeSpeed,\n                    stopAssessPlan: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.stopAssessPlan,\n                    crewBriefing: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.crewBriefing,\n                    navLights: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.navLights,\n                    soundSignal: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignal,\n                    lookout: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.lookout,\n                    soundSignals: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignals,\n                    radarWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radarWatch,\n                    radioWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radioWatch,\n                    endLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.endLocationID,\n                    crossedTime: crossedTime,\n                    approxSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.approxSafeSpeed,\n                    report: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.report,\n                    startLat: currentStartLocation.latitude.toString(),\n                    startLong: currentStartLocation.longitude.toString(),\n                    endLat: currentEndLocation.latitude.toString(),\n                    endLong: currentEndLocation.longitude.toString()\n                });\n                await tripEventModel.save({\n                    id: tripEventData.id,\n                    eventCategory: \"RestrictedVisibility\",\n                    eventType_RestrictedVisibilityID: restrictedVisibilityData.id\n                });\n                getCurrentEvent(tripEventData.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                if (closeOnSave) {\n                    setCloseOnSave(false);\n                    closeModal();\n                }\n            } else {\n                createTripEvent({\n                    variables: {\n                        input: {\n                            eventCategory: \"RestrictedVisibility\",\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateTripEvent, {\n        onCompleted: (response)=>{\n            const data = response.createTripEvent;\n            currentEventRef.current = data;\n            setCurrentEvent(data);\n            createEventType_RestrictedVisibility({\n                variables: {\n                    input: {\n                        startLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.startLocationID,\n                        crossingTime: crossingTime,\n                        estSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.estSafeSpeed,\n                        stopAssessPlan: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.stopAssessPlan,\n                        crewBriefing: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.crewBriefing,\n                        navLights: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.navLights,\n                        soundSignal: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignal,\n                        lookout: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.lookout,\n                        soundSignals: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.soundSignals,\n                        radarWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radarWatch,\n                        radioWatch: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.radioWatch,\n                        endLocationID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.endLocationID,\n                        crossedTime: crossedTime,\n                        approxSafeSpeed: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.approxSafeSpeed,\n                        report: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.report,\n                        startLat: currentStartLocation.latitude.toString(),\n                        startLong: currentStartLocation.longitude.toString(),\n                        endLat: currentEndLocation.latitude.toString(),\n                        endLong: currentEndLocation.longitude.toString(),\n                        memberID: selectedAuthor === null || selectedAuthor === void 0 ? void 0 : selectedAuthor.value\n                    }\n                }\n            });\n            updateTripEvent({\n                variables: {\n                    input: {\n                        id: data.id,\n                        eventCategory: \"RestrictedVisibility\",\n                        eventType_RestrictedVisibilityID: data.id\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error creating trip event\", error);\n        }\n    });\n    const [createEventType_RestrictedVisibility] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateEventType_RestrictedVisibility, {\n        onCompleted: (response)=>{\n            var _currentEventRef_current;\n            const data = response.createEventType_RestrictedVisibility;\n            updateTripEvent({\n                variables: {\n                    input: {\n                        id: (_currentEventRef_current = currentEventRef.current) === null || _currentEventRef_current === void 0 ? void 0 : _currentEventRef_current.id,\n                        eventType_RestrictedVisibilityID: data.id\n                    }\n                }\n            });\n            if (closeOnSave) {\n                setCloseOnSave(false);\n                closeModal();\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error creating Person rescue\", error);\n        }\n    });\n    const [updateEventType_RestrictedVisibility] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateEventType_RestrictedVisibility, {\n        onCompleted: ()=>{\n            // Successfully updated restricted visibility\n            if (closeOnSave) {\n                setCloseOnSave(false);\n                closeModal();\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error updating restricted visibility\", error);\n        }\n    });\n    const [updateTripEvent] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripEvent, {\n        onCompleted: ()=>{\n            getCurrentEvent(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Error updating trip event\", error);\n        }\n    });\n    const displayField = (fieldName)=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents, _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0 && ((_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.filter((field)=>field.fieldName === fieldName && field.status !== \"Off\").length) > 0) {\n            return true;\n        }\n        return false;\n    };\n    const handleStartLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setRestrictedVisibility({\n                ...restrictedVisibility,\n                startLocationID: +value.value,\n                startLat: null,\n                startLong: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setRestrictedVisibility({\n                ...restrictedVisibility,\n                startLocationID: 0,\n                startLat: value.latitude,\n                startLong: value.longitude\n            });\n        }\n    };\n    const handleEndLocationChange = (value)=>{\n        // If value is null or undefined, return early\n        if (!value) return;\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setRestrictedVisibility({\n                ...restrictedVisibility,\n                endLocationID: +value.value,\n                endLat: null,\n                endLong: null\n            });\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setRestrictedVisibility({\n                ...restrictedVisibility,\n                endLocationID: 0,\n                endLat: value.latitude,\n                endLong: value.longitude\n            });\n        }\n    };\n    const startLocationData = {\n        geoLocationID: (restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.startLocationID) > 0 ? restrictedVisibility.startLocationID : (_tripEvent_eventType_RestrictedVisibility = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility.startLocationID,\n        lat: (_tripEvent_eventType_RestrictedVisibility1 = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility1 === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility1.startLat,\n        long: (_tripEvent_eventType_RestrictedVisibility2 = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility2 === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility2.startLong\n    };\n    const endLocationData = {\n        geoLocationID: (restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.endLocationID) > 0 ? restrictedVisibility.endLocationID : (_tripEvent_eventType_RestrictedVisibility3 = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility3 === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility3.endLocationID,\n        lat: (_tripEvent_eventType_RestrictedVisibility4 = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility4 === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility4.endLat,\n        long: (_tripEvent_eventType_RestrictedVisibility5 = tripEvent.eventType_RestrictedVisibility) === null || _tripEvent_eventType_RestrictedVisibility5 === void 0 ? void 0 : _tripEvent_eventType_RestrictedVisibility5.endLong\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (members) {\n            const crewMembers = members.map((member)=>{\n                var _member_crewMember_firstName, _member_crewMember_surname;\n                return {\n                    label: \"\".concat((_member_crewMember_firstName = member.crewMember.firstName) !== null && _member_crewMember_firstName !== void 0 ? _member_crewMember_firstName : \"\", \" \").concat((_member_crewMember_surname = member.crewMember.surname) !== null && _member_crewMember_surname !== void 0 ? _member_crewMember_surname : \"\"),\n                    value: member.crewMemberID\n                };\n            });\n            setCrewMembers(crewMembers);\n        }\n    }, [\n        members\n    ]);\n    const riskImpacts = [\n        {\n            value: \"Low\",\n            label: \"Low impact\"\n        },\n        {\n            value: \"Medium\",\n            label: \"Medium impact\"\n        },\n        {\n            value: \"High\",\n            label: \"High impact\"\n        },\n        {\n            value: \"Severe\",\n            label: \"Severe impact\"\n        }\n    ];\n    const handleSaveRisk = async ()=>{\n        if (currentRisk.id > 0) {\n            updateRiskFactor({\n                variables: {\n                    input: {\n                        id: currentRisk.id,\n                        type: \"RestrictedVisibility\",\n                        title: currentRisk.title,\n                        impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                        probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                        mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                        eventType_RestrictedVisibilityID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.id\n                    }\n                }\n            });\n        } else {\n            createRiskFactor({\n                variables: {\n                    input: {\n                        type: \"RestrictedVisibility\",\n                        title: currentRisk.title,\n                        impact: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact : \"Low\",\n                        probability: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) ? currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability : 5,\n                        mitigationStrategy: currentStrategies.length > 0 ? currentStrategies.map((s)=>s.id).join(\",\") : \"\",\n                        eventType_RestrictedVisibilityID: restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.id,\n                        vesselID: vesselID\n                    }\n                }\n            });\n        }\n    };\n    const [createRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.CreateRiskFactor, {\n        onCompleted: ()=>{\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"RestrictedVisibility\"\n                        }\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const [updateRiskFactor] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateRiskFactor, {\n        onCompleted: ()=>{\n            setOpenRiskDialog(false);\n            getRiskFactors({\n                variables: {\n                    filter: {\n                        type: {\n                            eq: \"RestrictedVisibility\"\n                        }\n                    }\n                }\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n        }\n    });\n    const handleCreateRisk = (inputValue)=>{\n        setCurrentRisk({\n            ...currentRisk,\n            title: inputValue\n        });\n        setRiskValue({\n            value: inputValue,\n            label: inputValue\n        });\n        if (allRisks) {\n            const risk = [\n                ...allRisks,\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ];\n            setAllRisks(risk);\n        } else {\n            setAllRisks([\n                {\n                    value: inputValue,\n                    label: inputValue\n                }\n            ]);\n        }\n    };\n    const handleRiskValue = (v)=>{\n        // If v is null, user cleared the selection\n        if (!v) {\n            setCurrentRisk({\n                ...currentRisk,\n                title: \"\"\n            });\n            setRiskValue(null);\n            setRecommendedStratagies(false);\n            return;\n        }\n        // Check if this is a new value (not in existing options)\n        const isNewValue = !allRisks.some((risk)=>risk.value === v.value);\n        if (isNewValue) {\n            // Handle creating a new risk option\n            handleCreateRisk(v.value);\n        } else {\n            // Handle selecting an existing risk\n            setCurrentRisk({\n                ...currentRisk,\n                title: v.value\n            });\n            setRiskValue({\n                value: v.value,\n                label: v.value\n            });\n            if ((allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((risk)=>{\n                var _risk_mitigationStrategy_nodes;\n                return risk.title === v.value && ((_risk_mitigationStrategy_nodes = risk.mitigationStrategy.nodes) === null || _risk_mitigationStrategy_nodes === void 0 ? void 0 : _risk_mitigationStrategy_nodes.length) > 0;\n            }).length) > 0) {\n                setRecommendedStratagies(Array.from(new Set(allRiskFactors === null || allRiskFactors === void 0 ? void 0 : allRiskFactors.filter((r)=>{\n                    var _r_mitigationStrategy_nodes;\n                    return r.title === v.value && ((_r_mitigationStrategy_nodes = r.mitigationStrategy.nodes) === null || _r_mitigationStrategy_nodes === void 0 ? void 0 : _r_mitigationStrategy_nodes.length) > 0;\n                }).map((r)=>r.mitigationStrategy.nodes)[0].map((s)=>({\n                        id: s.id,\n                        strategy: s.strategy\n                    })))));\n            } else {\n                setRecommendedStratagies(false);\n            }\n        }\n    };\n    var _restrictedVisibility_estSafeSpeed, _restrictedVisibility_approxSafeSpeed, _restrictedVisibility_report;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            displayField(\"RestrictedVisibility_CrossingTime\") || displayField(\"RestrictedVisibility_StartLocation\") || displayField(\"RestrictedVisibility_EstSafeSpeed\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                    children: [\n                        displayField(\"RestrictedVisibility_StartLocation\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                            htmlFor: \"startLocation\",\n                            disabled: locked,\n                            label: \"Location where limited visibility starts\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                offline: offline,\n                                setCurrentLocation: setCurrentStartLocation,\n                                handleLocationChange: handleStartLocationChange,\n                                currentEvent: startLocationData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 871,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 867,\n                            columnNumber: 29\n                        }, this),\n                        displayField(\"RestrictedVisibility_CrossingTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                            htmlFor: \"crossingTime\",\n                            disabled: locked,\n                            label: \"Time where limited visibility starts\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                time: crossingTime,\n                                handleTimeChange: handleCrossingTimeChange,\n                                timeID: \"crossingTime\",\n                                fieldName: \"Time vis. restriction started\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 886,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 882,\n                            columnNumber: 29\n                        }, this),\n                        displayField(\"RestrictedVisibility_EstSafeSpeed\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                            htmlFor: \"estSafeSpeed\",\n                            disabled: locked,\n                            label: \"Estimated safe speed for conditions\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                id: \"estSafeSpeed\",\n                                type: \"number\",\n                                placeholder: \"Enter safe speed for conditions\",\n                                min: 1,\n                                className: \"w-full\",\n                                value: (_restrictedVisibility_estSafeSpeed = restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.estSafeSpeed) !== null && _restrictedVisibility_estSafeSpeed !== void 0 ? _restrictedVisibility_estSafeSpeed : undefined,\n                                onChange: (e)=>{\n                                    setRestrictedVisibility({\n                                        ...restrictedVisibility,\n                                        estSafeSpeed: e.target.value\n                                    });\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 899,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 895,\n                            columnNumber: 29\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                    lineNumber: 865,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false) : null,\n            displayField(\"RestrictedVisibility_StopAssessPlan\") || displayField(\"RestrictedVisibility_CrewBriefing\") || displayField(\"RestrictedVisibility_NavLights\") || displayField(\"RestrictedVisibility_SoundSignal\") || displayField(\"RestrictedVisibility_Lookout\") || displayField(\"RestrictedVisibility_SoundSignals\") || displayField(\"RestrictedVisibility_RadarWatch\") || displayField(\"RestrictedVisibility_RadioWatch\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 930,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                id: \"displaySOP\",\n                                checked: restrictedVisibility !== false,\n                                onClick: ()=>setOpenProcedureChecklist(true),\n                                label: \"Safe operating procedures checklist\",\n                                variant: \"success\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 931,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 949,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true) : null,\n            displayField(\"RestrictedVisibility_EndLocation\") || displayField(\"RestrictedVisibility_CrossedTime\") || displayField(\"RestrictedVisibility_ApproxSafeSpeed\") || displayField(\"RestrictedVisibility_Report\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col space-y-6\",\n                children: [\n                    displayField(\"RestrictedVisibility_EndLocation\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        htmlFor: \"endLocation\",\n                        disabled: locked,\n                        label: \"Location where limited visibility ends\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            offline: offline,\n                            setCurrentLocation: setCurrentEndLocation,\n                            handleLocationChange: handleEndLocationChange,\n                            currentEvent: endLocationData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 962,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 958,\n                        columnNumber: 25\n                    }, this),\n                    displayField(\"RestrictedVisibility_CrossedTime\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        htmlFor: \"crossedTime\",\n                        disabled: locked,\n                        label: \"Time when limited visibility ends\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            time: crossedTime,\n                            handleTimeChange: handleCrossedTimeChange,\n                            timeID: \"crossedTime\",\n                            fieldName: \"End time\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 971,\n                        columnNumber: 25\n                    }, this),\n                    displayField(\"RestrictedVisibility_ApproxSafeSpeed\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        htmlFor: \"approxSafeSpeed\",\n                        disabled: locked,\n                        label: \"Approximate average speed during restricted visibility period\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                            id: \"approxSafeSpeed\",\n                            type: \"number\",\n                            placeholder: \"Enter approximate average speed\",\n                            min: 1,\n                            className: \"w-full\",\n                            value: (_restrictedVisibility_approxSafeSpeed = restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.approxSafeSpeed) !== null && _restrictedVisibility_approxSafeSpeed !== void 0 ? _restrictedVisibility_approxSafeSpeed : undefined,\n                            onChange: (e)=>{\n                                setRestrictedVisibility({\n                                    ...restrictedVisibility,\n                                    approxSafeSpeed: e.target.value\n                                });\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 988,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 984,\n                        columnNumber: 25\n                    }, this),\n                    displayField(\"RestrictedVisibility_Report\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        htmlFor: \"restricted-visibility-report\",\n                        disabled: locked,\n                        label: \"Comments or observations\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                            id: \"restricted-visibility-report\",\n                            className: \"w-full min-h-[120px]\",\n                            rows: 4,\n                            placeholder: \"Add any comments or observations pertinant to the limited visibility event\",\n                            value: (_restrictedVisibility_report = restrictedVisibility === null || restrictedVisibility === void 0 ? void 0 : restrictedVisibility.report) !== null && _restrictedVisibility_report !== void 0 ? _restrictedVisibility_report : undefined,\n                            onChange: (e)=>{\n                                setRestrictedVisibility({\n                                    ...restrictedVisibility,\n                                    report: e.target.value\n                                });\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 1012,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1008,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-1 md:col-span-2 flex flex-col sm:flex-row justify-end gap-2 mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                variant: \"back\",\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"],\n                                onClick: closeModal,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1030,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                variant: \"primary\",\n                                onClick: locked ? ()=>{} : ()=>{\n                                    setCloseOnSave(true);\n                                    handleSave();\n                                },\n                                disabled: locked,\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                children: selectedEvent ? \"Update\" : \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1036,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1029,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                lineNumber: 956,\n                columnNumber: 17\n            }, this) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.Sheet, {\n                open: openProcedureChecklist,\n                onOpenChange: setOpenProcedureChecklist,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetContent, {\n                    side: \"right\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetTitle, {\n                                children: \"Safe operating procedures checklist\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1058,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 1057,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetBody, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" grid grid-cols-1 gap-6\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        displayField(\"RestrictedVisibility_StopAssessPlan\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"stopAssessPlan\",\n                                            checked: restrictedVisibility.stopAssessPlan,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    stopAssessPlan: checked === true\n                                                });\n                                            },\n                                            label: \"Stopped, assessed, planned\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 37\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_CrewBriefing\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"crewBriefing\",\n                                            checked: restrictedVisibility.crewBriefing,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    crewBriefing: checked === true\n                                                });\n                                            },\n                                            label: \"Briefed crew\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1090,\n                                            columnNumber: 37\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_NavLights\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"navLights\",\n                                            checked: restrictedVisibility.navLights,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    navLights: checked === true\n                                                });\n                                            },\n                                            label: \"Navigation lights on\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1110,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {\n                                            className: \"my-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1125,\n                                            columnNumber: 33\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_SoundSignal\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pl-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                                    htmlFor: \"soundSignal\",\n                                                    label: \"Sounds signals used (pick one)\",\n                                                    className: \"mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                    lineNumber: 1131,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_21__.RadioGroup, {\n                                                    defaultValue: restrictedVisibility.soundSignal,\n                                                    onValueChange: (value)=>{\n                                                        setRestrictedVisibility({\n                                                            ...restrictedVisibility,\n                                                            soundSignal: value\n                                                        });\n                                                    },\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                                            type: \"radio\",\n                                                            id: \"soundSignalNone\",\n                                                            value: \"None\",\n                                                            label: \"None needed\",\n                                                            radioGroupValue: restrictedVisibility.soundSignal,\n                                                            variant: \"warning\",\n                                                            onCheckedChange: ()=>setRestrictedVisibility({\n                                                                    ...restrictedVisibility,\n                                                                    soundSignal: \"None\"\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                            lineNumber: 1149,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                                            type: \"radio\",\n                                                            id: \"soundSignalMakingWay\",\n                                                            value: \"MakingWay\",\n                                                            label: \"Making way (1 long / 2 mins)\",\n                                                            radioGroupValue: restrictedVisibility.soundSignal,\n                                                            variant: \"warning\",\n                                                            onCheckedChange: ()=>setRestrictedVisibility({\n                                                                    ...restrictedVisibility,\n                                                                    soundSignal: \"MakingWay\"\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                            lineNumber: 1166,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                                            type: \"radio\",\n                                                            id: \"soundSignalNotMakingWay\",\n                                                            value: \"NotMakingWay\",\n                                                            label: \"Not making way (2 long / 2 mins)\",\n                                                            radioGroupValue: restrictedVisibility.soundSignal,\n                                                            variant: \"warning\",\n                                                            onCheckedChange: ()=>setRestrictedVisibility({\n                                                                    ...restrictedVisibility,\n                                                                    soundSignal: \"NotMakingWay\"\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                            lineNumber: 1184,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                                            type: \"radio\",\n                                                            id: \"soundSignalTowing\",\n                                                            value: \"Towing\",\n                                                            label: \"Towing (1 long + 2 short / 2 mins)\",\n                                                            radioGroupValue: restrictedVisibility.soundSignal,\n                                                            variant: \"warning\",\n                                                            onCheckedChange: ()=>setRestrictedVisibility({\n                                                                    ...restrictedVisibility,\n                                                                    soundSignal: \"Towing\"\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                            lineNumber: 1202,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                    lineNumber: 1136,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1130,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_19__.Separator, {\n                                            className: \"my-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1222,\n                                            columnNumber: 33\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_Lookout\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"lookout\",\n                                            checked: restrictedVisibility.lookout,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    lookout: checked === true\n                                                });\n                                            },\n                                            label: \"Set proper lookout\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1227,\n                                            columnNumber: 37\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_SoundSignals\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"soundSignals\",\n                                            checked: restrictedVisibility.soundSignals,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    soundSignals: checked === true\n                                                });\n                                            },\n                                            label: \"Listening for other sound signals\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1245,\n                                            columnNumber: 37\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_RadarWatch\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"radarWatch\",\n                                            checked: restrictedVisibility.radarWatch,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    radarWatch: checked === true\n                                                });\n                                            },\n                                            label: \"Radar watch on\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1265,\n                                            columnNumber: 37\n                                        }, this),\n                                        displayField(\"RestrictedVisibility_RadioWatch\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_check_field_label__WEBPACK_IMPORTED_MODULE_23__.CheckFieldLabel, {\n                                            id: \"radioWatch\",\n                                            checked: restrictedVisibility.radioWatch,\n                                            onCheckedChange: (checked)=>{\n                                                setRestrictedVisibility({\n                                                    ...restrictedVisibility,\n                                                    radioWatch: checked === true\n                                                });\n                                            },\n                                            label: \"Radio watch on\",\n                                            variant: \"warning\",\n                                            className: \"mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1285,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full flex flex-col space-y-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CloudFlareCaptures__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                inputId: (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id) || 0,\n                                                sectionId: currentTrip.id,\n                                                buttonType: \"button\",\n                                                sectionName: \"tripEventID\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                lineNumber: 1302,\n                                                columnNumber: 37\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1301,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                    lineNumber: 1065,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1063,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 1062,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_10__.SheetFooter, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"],\n                                onClick: ()=>setOpenProcedureChecklist(false),\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1313,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                            lineNumber: 1312,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                    lineNumber: 1056,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                lineNumber: 1053,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__.AlertDialogNew, {\n                openDialog: openRiskDialog,\n                setOpenDialog: setOpenRiskDialog,\n                handleCreate: handleSaveRisk,\n                title: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.id) > 0 ? \"Update Risk\" : \"Create New Risk\",\n                actionText: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.id) > 0 ? \"Update\" : \"Create Risk\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                htmlFor: \"impact\",\n                                label: \"Risk\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1328,\n                                columnNumber: 21\n                            }, this),\n                            allRisks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                id: \"impact\",\n                                options: allRisks,\n                                placeholder: \"Select or enter a risk\",\n                                value: riskValue,\n                                onChange: handleRiskValue,\n                                buttonClassName: \"w-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1330,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1327,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                htmlFor: \"risk-impact\",\n                                label: \"Risk impact\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1341,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_12__.Combobox, {\n                                id: \"impact\",\n                                options: riskImpacts,\n                                placeholder: \"Select risk impact\",\n                                value: (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact) ? riskImpacts === null || riskImpacts === void 0 ? void 0 : riskImpacts.find((impact)=>impact.value == (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.impact)) : null,\n                                onChange: (value)=>setCurrentRisk({\n                                        ...currentRisk,\n                                        impact: value === null || value === void 0 ? void 0 : value.value\n                                    }),\n                                buttonClassName: \"w-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1342,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1340,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                htmlFor: \"risk-probability\",\n                                label: \"Risk probability\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1364,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_17__.Slider, {\n                                        defaultValue: [\n                                            (currentRisk === null || currentRisk === void 0 ? void 0 : currentRisk.probability) || 5\n                                        ],\n                                        className: \"my-4\",\n                                        onValueChange: (value)=>setCurrentRisk({\n                                                ...currentRisk,\n                                                probability: value[0]\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1369,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Low\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                lineNumber: 1380,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"High\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                lineNumber: 1381,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1379,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1368,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1363,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                                className: \"text-lg font-semibold leading-6 text-gray-700 mb-4\",\n                                children: \"Mitigation strategy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1386,\n                                columnNumber: 21\n                            }, this),\n                            (currentRisk === null || currentRisk === void 0 ? void 0 : (_currentRisk_mitigationStrategy = currentRisk.mitigationStrategy) === null || _currentRisk_mitigationStrategy === void 0 ? void 0 : (_currentRisk_mitigationStrategy_nodes = _currentRisk_mitigationStrategy.nodes) === null || _currentRisk_mitigationStrategy_nodes === void 0 ? void 0 : _currentRisk_mitigationStrategy_nodes.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-4 bg-gray-50 rounded-md\",\n                                children: currentRisk === null || currentRisk === void 0 ? void 0 : (_currentRisk_mitigationStrategy1 = currentRisk.mitigationStrategy) === null || _currentRisk_mitigationStrategy1 === void 0 ? void 0 : _currentRisk_mitigationStrategy1.nodes.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-2 last:mb-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            dangerouslySetInnerHTML: {\n                                                __html: s.strategy\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                            lineNumber: 1395,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, s.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1394,\n                                        columnNumber: 37\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1391,\n                                columnNumber: 25\n                            }, this),\n                            content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-4 bg-gray-50 rounded-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm\",\n                                    dangerouslySetInnerHTML: {\n                                        __html: content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                    lineNumber: 1408,\n                                    columnNumber: 29\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1407,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                    variant: \"primary\",\n                                    className: \"bg-orange-400 hover:bg-orange-500\",\n                                    onClick: ()=>setOpenRecommendedstrategy(true),\n                                    children: \"Add strategy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                    lineNumber: 1417,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1416,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1385,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                lineNumber: 1321,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__.AlertDialogNew, {\n                openDialog: openRecommendedstrategy,\n                setOpenDialog: setOpenRecommendedstrategy,\n                handleCreate: handleNewStrategy,\n                title: \"Recommended strategy\",\n                actionText: \"Save\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                                children: \"Available strategies\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1433,\n                                columnNumber: 21\n                            }, this),\n                            recommendedStratagies ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-3\",\n                                        children: recommendedStratagies === null || recommendedStratagies === void 0 ? void 0 : recommendedStratagies.map((risk)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                                onClick: ()=>{\n                                                    handleSetCurrentStrategies(risk);\n                                                    if (currentRisk) {\n                                                        handleSetRiskValue({\n                                                            title: currentRisk.title,\n                                                            mitigationStrategy: {\n                                                                nodes: [\n                                                                    risk\n                                                                ]\n                                                            }\n                                                        });\n                                                    }\n                                                },\n                                                className: \"\".concat((currentStrategies === null || currentStrategies === void 0 ? void 0 : currentStrategies.find((s)=>s.id === risk.id)) ? \"border-orange-400 bg-orange-50\" : \"border-gray-200 bg-gray-50\", \" border p-4 rounded-lg cursor-pointer text-left w-full\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    dangerouslySetInnerHTML: {\n                                                        __html: risk === null || risk === void 0 ? void 0 : risk.strategy\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                    lineNumber: 1452,\n                                                    columnNumber: 41\n                                                }, this)\n                                            }, risk.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                                lineNumber: 1438,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1436,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                                        className: \"text-lg font-normal leading-6 text-gray-700 mt-6\",\n                                        children: \"or add new Mitigation strategy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1460,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                                        className: \"p-4 bg-gray-50 rounded-md text-gray-600 text-center\",\n                                        children: \"No recommendations available!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1466,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_20__.H4, {\n                                        className: \"text-lg font-normal leading-6 mt-4 mb-2 text-gray-700\",\n                                        children: \"Create a new strategy instead\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                        lineNumber: 1469,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1432,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                                htmlFor: \"strategy\",\n                                label: \"Strategy details\",\n                                className: \"block mb-2 font-medium text-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1476,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border rounded-md overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_editor__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    id: \"strategy\",\n                                    placeholder: \"Mitigation strategy\",\n                                    className: \"w-full\",\n                                    content: content,\n                                    handleEditorChange: handleEditorChange\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                    lineNumber: 1482,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                                lineNumber: 1481,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                        lineNumber: 1475,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                lineNumber: 1426,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog_new__WEBPACK_IMPORTED_MODULE_18__.AlertDialogNew, {\n                openDialog: openDeleteConfirmation,\n                setOpenDialog: setOpenDeleteConfirmation,\n                handleCreate: handleDeleteRisk,\n                title: \"Delete risk analysis!\",\n                actionText: \"Delete\",\n                showDestructiveAction: true,\n                destructiveActionText: \"Delete\",\n                handleDestructiveAction: handleDeleteRisk,\n                children: \"Are you sure you want to delete this risk?\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n                lineNumber: 1492,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\restricted-visibility.tsx\",\n        lineNumber: 860,\n        columnNumber: 9\n    }, this);\n}\n_s(RestrictedVisibility, \"EgioK3yv1FmAu+UeMCuY7LVYwY4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_13__.useSearchParams,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_26__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_25__.useMutation\n    ];\n});\n_c = RestrictedVisibility;\nvar _c;\n$RefreshReg$(_c, \"RestrictedVisibility\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/restricted-visibility.tsx\n"));

/***/ })

});